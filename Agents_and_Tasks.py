from crewai import Agent, Task, LLM
from dotenv import load_dotenv
load_dotenv()

# llm = LLM(
# model="vertex_ai/gemini-2.0-flash-exp")

llm = LLM(
model="vertex_ai/gemini-2.0-flash-thinking-exp-01-21", temperature=0.6)

proposal_info_extraction_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Procurement Document Extractor",
    goal=(
        "Extract EXACTLY the content related to 15 mandatory fields from procurement documents (RFPs, RFQs, RFIs, IFBs) "
        "as written, including section references. Never infer, or analyze. "
        "Fields: Solicitation Number, NAICS Code, Name/Title, Solicitation Type, Department/Agency Name, "
        "Inquiries/Questions Due Date, Proposal Due Date, Mode of Submission, Place of Performance, POC, "
        "Set Aside, Period of Performance, Key Personnel, Security Clearance, Task Order Type. "
        "Return just 'NA' for missing fields."
    ),
    backstory=(
        "You are a meticulous document scanner trained to: \n"
        "1. **Locate, not interpret**: Treat every procurement document as raw input \n"
        "2. **Section mapping**: Tag every extracted content with its source section (e.g., 'Section L.4.2(c)')\n"
        "3. **Zero creativity**: If not explicitly stated, return 'NA' with no section reference.\n"
        "Built for compliance teams who need verbatim data extraction with audit-ready references."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "The output should be in markdown format."
    )
)


proposal_info_extraction_task = Task(
    description=(
        "Using the provided procuremnet document content, do the below: \n"
        "1. **Validate Document Type & Version:**\n"
        "   - Confirm the document is an RFP, RFQ, RFI, or IFB using explicit labels or headings.\n"
        "   - Check for amendments/updates and note version conflicts.\n\n"
        "2. **Extract Mandatory Fields:**\n"
        "   - Extract all the content related to 15 fields from the document. If there are multiple values mentioned for a filed and the values are different, then mention all the values along with section/page references\n"
        "Fields: Solicitation Number, NAICS Code, Name/Title of the procurement/project, Solicitation Type, Department/Agency Name which is issuing this procurement/project, "
        "Inquiries/Questions Due Date, Proposal Due Date, Mode of Submission, Place of Performance, POC(POint of contact), "
        "Set Aside, Period of Performance, Key Personnel, Security Clearance, Task Order Type. "
        "   - Follow strict rules:\n"
        "     - No assumptions (e.g., don't infer Set-Aside if not explicitly mentioned).\n"
        "     - Include exact section references (e.g., 'Section F.1').\n"
        "     - Use 'NA' only when data is absent in the document.\n\n"
        "3. **Ensure Format Compliance:**\n"
        "   - Structure the output just like it is in document content with section references.\n"
        "The provided content is:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "[Full, verbatim content as it appears in the document]\n"
        "[Repeat for each relevant section]"
    ),
    agent=proposal_info_extraction_agent,  # Your predefined procurement_agent
    constraints=[
        "STRICTLY follow the document content  no external knowledge",
        "Never guess missing fields, use 'NA' and no section references.",
        "Prioritize accuracy over completion speed"
    ],
    # examples=(
    #     "**Example Output:**\n"
    #     "- **Period of Performance:**\n"
    #     "  Base Period: 12 months (Section B.5)\n"
    #     "  Option Period 1: 6 months (Section B.5)\n"
    #     "- **Security Clearance Requirements:** Secret clearance (Section H.3)"
    # )
)


proposal_info_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Proposal Info Drafting Agent",
    goal=(
        "Using the extracted procurement document content, generate a clean, audit-ready summary section titled 'Proposal Info' "
        "in markdown table format. The table must have three columns: Specification Name, Value, and Section Reference. "
        "It must include exactly the following rows with their corresponding section references as extracted: "
        "Solicitation Number, NAICS Code, Name/Title, Solicitation Type, Department/Agency Name, Inquiries/Questions Due Date and Time, "
        "Proposal Due Date and Time, Mode of Submission, Place of Performance, Point of Contact (POC), Set Aside, Period of Performance, "
        "Key Personnel, Security Clearance Requirements, and Task Order Type. For the Period of Performance row, include all relevant "
        "information in the Value column. This should consist of the main period of performance content followed by any Option Periods and/or "
        "Extensions, each separated by a newline character (\\n). The Section Reference column for Period of Performance should contain "
        "the main section reference. If a field is not present in the original document, output 'NA' for both the value and the section reference. "
        "No additional commentary or formatting is allowed."
    ),
    backstory=(
        "You are a detail-oriented summary drafter whose responsibility is to compile and format the extracted data into a concise summary. "
        "Your output must be exactly as specified, using the previously extracted field values along with their respective section references. "
        "Maintain accuracy, clarity, and consistency without inferring any missing information."
    ),
    allow_delegation=False,
    verbose=False,
    output_format="The output should be in markdown format, starting with the '## Proposal Info' without any additional text or commentary which is unrelated to the Proposal Info."
)

proposal_info_drafting_task = Task(
    description=(
        "Draft the summary section titled 'Proposal Info' using the extracted content provided from the previous extraction task. "
        "Ensure the summary is structured exactly as a markdown table with three columns: Specification Name, Value, and Section Reference. "
        "The table should include rows for each of the following specifications:\n\n"
        "| Specification Name                        | Value                                                                                                                          | Section Reference                |\n"
        "|-------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------|----------------------------------|\n"
        "| Solicitation Number                       | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| NAICS Code                                | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Name/Title                                | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Solicitation Type                         | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Department/Agency Name                    | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Inquiries/Questions Due Date and Time     | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Proposal Due Date and Time                | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Mode of Submission                        | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Place of Performance                      | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Point of Contact (POC)                    | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Set Aside                                 | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Period of Performance                     | <main period of performance content or 'NA'>\\nOption Periods: <if any, list as 'Option Period 1: <content> (<section>)', 'Option Period 2: <content> (<section>)', etc., each on a new line>\\nExtensions: <if any, list as 'Extension 1: <content> (<section>)', 'Extension 2: <content> (<section>)', etc., each on a new line> | <main section reference for period of performance or 'NA'> |\n"
        "| Key Personnel                             | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Security Clearance Requirements           | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n"
        "| Task Order Type                           | <extracted content or 'NA'>                                                                                                    | <extracted section or 'NA'>      |\n\n"
        "Do not include any extra text or commentary. Use the exact values as extracted previously; if any field is missing, output 'NA' for both the value and section reference. "
        "{user_guidelines}"
        "The extracted procurement content is:\n\n{content}"
    ),
    expected_output=(
        "## Proposal Info\n\n"
        "| Specification Name                        | Value                                                                                                                          | Section Reference                |\n"
        "|-------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------|----------------------------------|\n"
        "| Solicitation Number                       | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| NAICS Code                                | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Name/Title                                | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Solicitation Type                         | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Department/Agency Name                    | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Inquiries/Questions Due Date and Time     | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Proposal Due Date and Time                | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Mode of Submission                        | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Place of Performance                      | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Point of Contact (POC)                    | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Set Aside                                 | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Period of Performance                     | <main period of performance content or 'NA'>\\nOption Periods: <if any, list as 'Option Period 1: <content> (<section>)', 'Option Period 2: <content> (<section>)', ...>\\nExtensions: <if any, list as 'Extension 1: <content> (<section>)', 'Extension 2: <content> (<section>)', ...> | <section reference or 'NA'> |\n"
        "| Key Personnel                             | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Security Clearance Requirements           | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |\n"
        "| Task Order Type                           | <extracted content or 'NA'>                                                                                                    | <section reference or 'NA'>      |"
    ),
    agent=proposal_info_drafting_agent,
    constraints=[
        "STRICTLY use only the extracted content provided from the previous task.",
        "Do not add any commentary or extraneous text beyond the specified format.",
        "If a field is not present, output 'NA' for both the value and section reference.",
        "Maintain exact markdown table formatting as described. For the Period of Performance row, all information (main content, Option Periods, and Extensions) must be included in the Value column in a single row, with each additional item separated by a newline (\\n)."
    ]
)





# -------------------------------BACKGROUND------------------------------------
# Agent 1: Background Content Extractor
background_extraction_agent = Agent(
    llm=llm,
    role="Background Section Extractor",
    goal=(
        "Locate and extract verbatim content from explicit Background/Purpose/Introduction sections "
        "with exact section references. Exclude all other content."
    ),
    backstory=(
        "You are a surgical text extraction system trained to:\n"
        "1. Identify ONLY sections explicitly meant for 'Background', 'Purpose', or 'Introduction'\n"
        "2. Capture complete related content with original formatting\n"
        "3. Tag each extracted block with its exact document section reference\n"
        "4. Reject content from adjacent unrelated sections like Scope or Objectives"
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "Provide the output in markdown format."
    )
)

background_extraction_task = Task(
    description=(
        "Process the document to:\n"
        "1. Identify explicit section containing 'Background', 'Purpose', or 'Introduction'\n"
        "2. Extract ALL content under identified sections until next major section break\n"
        "3. Preserve original formatting (bullet points, numbering, etc.)\n"
        "4. Record exact section references (e.g., 'Section C.2.1')\n"
        "5. Exclude content from irrelevant sections like:\n"
        "   - SOW/PWS beyond introductory paragraphs\n"
        "   - Scope/Objectives/Task Areas\n"
        "   - Submission requirements\n\n"
        "Document Content:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "[Full, verbatim content as it appears in the document]\n"
        "[Repeat for each relevant section]"
        "OR\n"
        "No Background Identified"
    ),
    agent=background_extraction_agent,
    constraints=[
        "Never combine content from multiple sections",
        "Reject inferred/misplaced content - only explicit sections",
        "Preserve original line breaks and formatting",
    ]
)

background_summary_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Background Summary Drafting Agent",
    goal=(
        "Using the provided extracted content, identify and extract all explicit Background, Purpose, or Introduction sections from the procurement document without rephrasing, omitting, or creating any additional headings. Reformat the content in markdown to increase readability while ensuring no redundancy of content and preserving exact wording and section references."
    ),
    backstory=(
        "You are an expert procurement/solicitation analyst specializing in summarizing specific sections from procurement documents such as RFPs, RFQs, RFIs, IFBs, etc. Your task is to accurately extract the Background, Purpose, or Introduction section without altering the original text. Only reformat the extracted content in markdown for improved readability. Do not add any external commentary or additional headings."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "Markdown format. Do not create additional headings. The output must be exactly the reformatted extracted content, preserving all original wording and section references, without any extra commentary."
    )
)

background_summary_drafting_task = Task(
    description=(
        "Draft a summary for the Background, Purpose, or Introduction section of the procurement document using only the extracted content provided. "
        "The summary must be a detailed, self-contained output that includes all relevant details and section references, reformatted in markdown for enhanced readability. "
        "Do not rephrase, omit, or add any additional headings, and avoid redundancy. "
        "If no relevant content is available, output exactly 'No Background Identified'."
        "{user_guidelines}"
        "The extracted procurement content is:\n\n {content}"
    ),
    expected_output=(
        "A markdown formatted summary containing the exact extracted Background, Purpose, or Introduction content with improved readability. "
        "No extra commentary or headings should be added beyond what is provided."
    ),
    agent=background_summary_drafting_agent,
    constraints=[
        "STRICTLY use only the extracted content provided from the previous extraction task.",
        "Do not add any external commentary or additional text beyond the specified format.",
        "Do not rephrase, omit, or create any additional headings; only reformat the content in markdown to increase readability.",
        "Ensure no redundancy of content.",
        "If no relevant content exists, output exactly 'No Background Identified'."
    ]
)



#--------------------------------------SCOPE---------------------------------------


# Agent 1: Scope Content Extractor
scope_extraction_agent = Agent(
    llm=llm,
    role="Scope Section Extractor",
    goal=(
        "Locate and extract verbatim content from explicit Scope sections."
    ),
    backstory=(
        "You are a precision extraction system trained to:\n"
        "1. Identify ONLY sections explicitly defining 'Scope' of the procurement/project."
        "2. Capture complete scope definitions.\n"
        "3. Preserve original formatting and content.\n"
        "4. Reject content from adjacent unrelated sections like Objectives or Deliverables, Tasks, Task Areas."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "The output should be in markdown format."
    )
)

scope_extraction_task = Task(
    description=(
        "Process the document to:\n"
        "1. Identify explicit scope-related sections that define the scope of the procurement/project.\n"
        "2. Extract ALL content under identified sections until next major section break\n"
        "3. Record exact section references (e.g., 'Section C.2.1')\n"
        "5. Exclude content from unrelated sections like:\n"
        "   - Background/Introduction sections\n"
        "   - Deliverables lists\n"
        "   - Evaluation criteria\n"
        "   - Administrative requirements\n\n"
        "Document Content:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "[Full, verbatim content as it appears in the document]\n"
        "[Repeat for each relevant section]"
        "OR\n"
        "No Scope Identified"
    ),
    agent=scope_extraction_agent,
    constraints=[
        "Never combine scope definitions with task areas",
        "Reject implied/inferred scope elements",
        "Preserve original numbering and bullet points",
    ]
)



scope_summary_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Scope Summary Drafting Agent",
    goal=(
        "Using the provided extracted content, identify and extract only the content explicitly stated as the scope of the procurement/contract. "
        "Generate an accurate, self-contained summary in markdown format with improved readability without adding any new headings. "
        "Do not rephrase, omit, or add any additional commentary. Remove any redundancy and output the content exactly as extracted. "
        "If no scope content was extracted, output exactly 'No Scope Identified'."
    ),
    backstory=(
        "You are a procurement/solicitation expert specializing in generating precise scope summaries from procurement documents. "
        "Your objective is to produce a detailed summary of the Scope section using the exact wording provided, without any modifications. "
        "Reformat the content in markdown for better readability but do not create new headings or modify the text. "
        "Task Areas Overview is not required."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "The output should be in markdown format. Do not create any new headings or additional text. "
        "If no scope content was extracted, output exactly 'No Scope Identified'."
    )
)

scope_summary_drafting_task = Task(
    description=(
        "Draft a summary for the Scope of the procurement document using the extracted content provided. "
        "Identify and extract only the content explicitly defined as scope. "
        "The summary must be formatted in markdown for improved readability, without creating any new headings or additional text. "
        "The summary must use exactly the provided wording, including all section references, without any rephrasing, omission, or redundancy. "
        "If no scope content was extracted, output exactly 'No Scope Identified'."
        "{user_guidelines}"
        "The extracted procurement content is:\n\n {content}"
    ),
    expected_output=(
        "A markdown formatted summary of the scope content exactly as provided, with improved readability but no new headings or additional text, "
        "or exactly 'No Scope Identified' if no content is available."
    ),
    agent=scope_summary_drafting_agent,
    constraints=[
        "STRICTLY use only the extracted scope content provided from the previous extraction task.",
        "Do not add any commentary, create new headings, or modify the text in any way.",
        "If no scope content exists, output exactly 'No Scope Identified'.",
        "Ensure no redundancy of content and no rephrasing."
    ]
)



# ----------------------------------------------TASK AREAS---------------------

task_extraction_agent = Agent(
    llm=llm,
    role="Task Area Content Extractor",
    goal=(
        "Identify and extract ONLY task areas from the provided content by analyzing content for broad, conceptual groupings of related activities, "
        "and provide the FULL, VERBATIM content of those sections as they appear in the document. "
        "This applies to both UCF-based and non-UCF procurements. Assume that at least one task area will always be present."
    ),
    backstory=(
        "You are a procurement domain expert trained to:\n"
        "1. Recognize broad, conceptual groupings of related activities (task areas) in any procurement format.\n"
        "2. Distinguish task areas from deliverables, individual tasks, and scope.\n"
        "3. Extract the FULL, VERBATIM content of task area sections while preserving original formatting, structure, and wording.\n"
        "4. Ensure that a task area is always extracted from the provided procurement content."
    ),
    allow_delegation=False,
    verbose=False,
    output_format="The output should be in markdown format."
)

task_extraction_task = Task(
    description=(
        "Analyze the document to identify and extract ONLY task areas:\n"
        "1. **Identify Conceptual Groupings:**\n"
        "   - Look for broad categories of related activities/responsibilities.\n"
        "   - Focus on high-level domains in both UCF (e.g., Section C, Section F, Section B, Section H) and non‑UCF (e.g., SOW/PWS, Specifications, Schedules) formats.\n"
        "2. **Exclude Non-Task Areas:**\n"
        "   - Do not include deliverables (e.g., 'Final Report', 'Risk Assessment').\n"
        "   - Exclude individual tasks (e.g., 'Draft RFP', 'Evaluate Bids') and scope descriptions (e.g., project boundaries, timelines).\n"
        "3. **Extract Full Content:**\n"
        "   - Provide the FULL, VERBATIM content of each identified task area section.\n"
        "   - Preserve original formatting, structure, and wording.\n"
        "   - Include document references (section/page) as they appear.\n\n"
        "Document Content:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "Full, verbatim content as it appears in the document\n"
        "[Repeat for each relevant section]"
    ),
    agent=task_extraction_agent,
    constraints=[
        "Only extract broad, conceptual groupings that represent task areas.",
        "Exclude deliverables, individual tasks, and scope details.",
        "Preserve FULL, VERBATIM content without any rephrasing or inference.",
        "Ensure at least one task area is extracted from the document."
    ]
)


task_area_summary_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Procurement Task Area Extraction Agent",
    goal=(
        "Using the provided procurement document content, extract the explicit task area content and associated sub-tasks that the offeror is expected to perform. "
        "Reformat the extracted content in markdown for improved readability without rephrasing, omitting, or adding any new headings beyond what is present in the extracted content. "
        "Ensure there is no redundancy of content and that all section references are included exactly as provided. "
        "If no task area content was extracted, output exactly 'No Task Areas Identified'."
    ),
    backstory=(
        "You are a procurement analysis expert specializing in summarizing task-area content from procurement documents (RFPs, RFQs, RFIs, IFBs). "
        "Your objective is to create an accurate, structured summary that faithfully reflects the original document's organizational flow, focusing solely on the tasks explicitly assigned to the contractor. "
        "Use the exact wording from the document and reformat the content in markdown for enhanced readability without rephrasing, omitting any detail, or adding extra headings."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "Markdown format starting with an H2 heading '## Task Areas'. Each task area should be presented using the exact headings from the original text (typically H3 headings) along with their content and section references. "
        "If explicitly extracted sub-tasks exist, include them under a '### Subtasks:' bullet list with section references. "
        "Do not rephrase, summarize, or modify the extracted content in any way."
    )
)

task_area_summary_drafting_task = Task(
    description=(
        "From the provided procurement document content, extract only the explicit task area content and associated sub-tasks that the offeror is expected to perform. "
        "Exclude any deliverables, minor/general tasks, scope, background, or other non-task area items. "
        "Reformat the extracted content in markdown for improved readability without rephrasing or modifying the original wording. "
        "Maintain the original organizational structure, including all section references, and do not add any additional headings beyond what is present. "
        "If no task area content is extracted, output exactly 'No Task Areas Identified'.\n\n"
        "{user_guidelines}"
        "The extracted procurement content is:\n\n {content}"
    ),
    expected_output=(
        "A markdown formatted summary starting with an H2 heading '## Task Areas' followed by the task area content exactly as extracted, "
        "including any H3 headings for individual task areas and a '### Subtasks:' bullet list if applicable, with all section references intact. "
        "No rephrasing or additional text should be present, or exactly 'No Task Areas Identified' if no content is available."
    ),
    agent=task_area_summary_drafting_agent,
    constraints=[
        "Use only the explicit task area content provided from the procurement document.",
        "Exclude deliverables, minor/general tasks, scope, background, or any content not directly related to the tasks the offeror is expected to perform.",
        "Preserve the original organizational structure and include all section references exactly as provided.",
        "Do not rephrase, summarize, or modify the extracted content in any way; output it exactly as provided.",
        "If no task area content exists, output exactly 'No Task Areas Identified'."
    ]
)




# ---------------------------------------SUB INSTRUCTIONS-------------------------


submission_extraction_agent = Agent(
    llm=llm,
    role="Submission Instructions Extractor",
    goal=(
        "Identify and extract ALL submission-related requirements from procurement documents using semantic analysis of directive language, "
        "capturing verbatim content and precise document references. This applies to both UCF-based and non-UCF formats, and you must assume that "
        "submission instructions are always present."
    ),
    backstory=(
        "You are a procurement documentation specialist trained to:\n"
        "1. Recognize all submission-related instructions in solicitation documents regardless of section headings or keywords.\n"
        "2. Distinguish submission instructions from evaluation criteria and other non-directive content.\n"
        "3. Capture exact wording, formatting details, and document cross-references without paraphrasing or interpretation.\n"
        "4. Preserve the original structure and content, ensuring that the extracted text remains as faithful as possible to the source.\n"
        "Rely on semantic understanding rather than predefined keywords to locate submission instructions."
    ),
    allow_delegation=False,
    verbose=False,
    output_format="markdown"
)

submission_extraction_task = Task(
    description=(
        "Analyze the provided procurement document to extract ALL submission requirements as follows:\n"
        "1. **Semantic Identification of Submission Instructions:**\n"
        "   - Locate any sections that conveys actionable, directive language (e.g., instructions on how to submit proposals, bids, or offers), "
        "regardless of the section title.\n"
        "2. **Capture Verbatim Content:**\n"
        "   - Extract the sections exactly as it appears, preserving original wording, formatting, numbering, and cross-references.\n"
        " - If some section referencing is mentioned then include the content from that referenced section as well. e.g If 'Refer to section L', then included all of the submission instructions from that section"
        "3. **Preserve Structural Hierarchy:**\n"
        "   - Maintain the document's original section hierarchy and include all associated document references.\n\n"
        "Document Content:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "[Full, verbatim content as it appears in the document]\n"
        "[Repeat for each relevant section]"
    ),
    agent=submission_extraction_agent,
    constraints=[
        "Extract ALL submission-related content using semantic analysis, not just keyword matching.",
        "Preserve exact wording, formatting, and structural hierarchy including cross-references.",
        "Do not provide interpretations or modify the original text in any way."
    ]
)




submission_summary_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Submission Instructions Summary Drafting Agent",
    goal=(
        "Using the provided extracted submission instruction content, output the content exactly as provided, reformatting it in markdown for improved readability without rephrasing, omitting, or altering any of the original content. "
        "Ensure no redundancy or duplication occurs. The output must begin with the H2 heading '## Submission Instructions' (if not already present) and include all original formatting, section references, and ordering."
    ),
    backstory=(
        "You are a procurement/solicitation analyst tasked with generating a thorough, self-contained summary of the 'Submission Instructions' section from procurement documents. "
        "Your role is to reformat the verbatim extracted content in markdown to enhance readability without rephrasing, omitting, or modifying any detail. "
        "You must preserve all original formatting, section references, and ordering, ensuring that only submission instructions are included."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "Markdown format. The output must begin with '## Submission Instructions' followed by the extracted submission instruction content exactly as provided, with improved readability formatting (e.g., line breaks, bullet points) without altering the text."
    )
)

submission_summary_drafting_task = Task(
    description=(
        "Reformat the submission instruction content from the procurement document into a markdown summary. "
        "Include only all extracted submission instruction content exactly as provided, preserving original formatting, ordering, and section references. "
        "You may add formatting elements (such as line breaks or bullet points) to enhance readability, but do not rephrase, omit, or modify any part of the content, and do not create additional headings beyond what is provided. "
        "If the content does not already start with an H2 heading '## Submission Instructions', then prepend that heading.\n\n"
        "{user_guidelines}\n\n"
        "The extracted procurement content is:\n\n {content}"
    ),
    expected_output=(
        "A markdown formatted summary starting with '## Submission Instructions' followed by the submission instruction content exactly as provided, with enhanced readability formatting but no changes to wording, ordering, or section references."
    ),
    agent=submission_summary_drafting_agent,
    constraints=[
        "Use only the provided procurement content  and output only the content related to submission instructions.",
        "Do not rephrase, omit, or modify any part of the original content.",
        "Do not create any new headings aside from ensuring the output begins with '## Submission Instructions'.",
        "Enhance readability only by reformatting (adding line breaks, bullet points) without altering the text.",
        "Ensure there is no redundant or duplicated content in the final output."
    ]
)



# ------------------------------EVAL. FACTORS FOR AWARD--------------


# Enhanced Evaluation Factor Extraction Agent

evaluation_extraction_agent = Agent(
    llm=llm,
    role="Evaluation Factor Content Extractor",
    goal=(
        "Identify and extract ONLY evaluation criteria/factors from the document by analyzing "
        "content related to how the proposal is evaluated. Extract the FULL, VERBATIM content along "
        "with section/page references. Since evaluation factors are always present, ensure that every procurement document returns the evaluation criteria."
    ),
    backstory=(
        "You are a procurement domain expert specialized in extracting evaluation-related content from proposals. "
        "Your expertise allows you to identify all sections that detail how an offeror's proposal will be evaluated, whether in UCF or non‑UCF solicitations. "
        "You must extract the sections related to evaluation exactly as they appear in the document, preserving formatting, structure, and wording."
    ),
    allow_delegation=False,
    verbose=False,
    output_format="Markdown"
)

evaluation_extraction_task = Task(
    description=(
        "Analyze the document to identify and extract ONLY evaluation factors related to the proposal. Focus on:\n"
        "1. **Evaluation Criteria & Sub-Factors:**\n"
        "   - Capture sections that detail evaluation factors (e.g., technical, management, cost, past performance).\n"
        "   - Include any sub-factors or additional metrics that contribute to the evaluation.\n\n"
        "2. **Weights and Scoring Mechanisms (if provided):**\n"
        "   - Extract information on weighting for each factor, scoring matrices, rating scales, or thresholds.\n\n"
        "3. **Evaluation Phases and Order (if provided):**\n"
        "   - Identify any multi-phase evaluations and provide details on the sequence or importance.\n\n"
        "4. **Content Integrity:**\n"
        "   - Provide the FULL, VERBATIM content as it appears in the document, including exact formatting and section/page references.\n\n"
        "5. **Exclusions:**\n"
        "   - Exclude all content that does not relate directly to evaluation (e.g., submission instructions, proposal preparation, post-award details).\n\n"
        "If no evaluation factors are found, return 'No evaluation factors found in the document.'\n\n"
        "Document Content:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "Full, verbatim content as it appears in the document\n"
        "[Repeat for each relevant section]\n"
        "OR\n"
        "No evaluation factors found in the document."
    ),
    agent=evaluation_extraction_agent,
    constraints=[
        "Extract only sections related to how the offeror's proposal is evaluated.",
        "Include evaluation sub-factors, weights, scoring mechanisms, and phases if present.",
        "Preserve FULL, VERBATIM content with exact formatting and document references.",
        "Exclude all non-evaluation related content such as submission instructions or proposal preparation.",
        "If no evaluation factors are found, return 'No evaluation factors found in the document.'"
    ]
)

# Enhanced Evaluation Factor Summary Drafting Agent

evaluation_summary_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Evaluation Summary Drafting Agent",
    goal=(
        "Using the provided procurement content, output the content related to evaluation factors/criteria only, exactly as provided, reformatting it in markdown for improved readability without rephrasing, omitting, or altering any of the original content. "
        "Do not add any new headings. Ensure no redundancy or duplication of content and remove any meaningless special characters if present. "
        "Maintain all original formatting, section references, and ordering."
    ),
    backstory=(
        "You are an expert procurement/solicitation analyst tasked with extracting and summarizing only the evaluation criteria/factors and their related content from procurement documents. "
        "Your role is to reformat the verbatim extracted evaluation criteria content in markdown to enhance readability without modifying, rephrasing, or omitting any part of the original content. "
        "Do not create any new headings; only reformat what is provided."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "Markdown format. Reformat the extracted evaluation criteria/factors content for improved readability without adding any new headings. "
        "Preserve all original formatting, section references, and ordering exactly as provided."
    )
)

evaluation_summary_drafting_task = Task(
    description=(
        "Extract and format only the evaluation criteria/factors related content from the provided procurement document. "
        "Include all critical evaluation details such as evaluation factors, sub-factors, weights, scoring mechanisms, evaluation phases, and any other criteria necessary for understanding the evaluation process. "
        "Exclude any sections unrelated to evaluation (e.g., submission instructions, proposal preparation, post-award details). "
        "Reformat the content in markdown for enhanced readability without rephrasing, omitting, or modifying any part of the original text, and do not create any new headings. "
        "Remove any redundant or duplicated content and any meaningless special characters if present. "
        "Ensure the final output is within a 5500-word limit.\n\n"
        "{user_guidelines}\n\n"
        "The extracted procurement content is:\n\n {content}"
    ),
    expected_output=(
        "A markdown formatted summary containing the evaluation criteria/factors content exactly as provided, with improved readability formatting but no modifications to wording, ordering, or structure, and with no new headings added."
    ),
    agent=evaluation_summary_drafting_agent,
    constraints=[
        "Strictly use only the extracted evaluation criteria/factors content provided from the previous extraction task.",
        "Do not rephrase, summarize, or alter the extracted content in any way.",
        "Do not create any new headings; preserve the original formatting and structure as provided.",
        "Remove any redundant or duplicated content and any meaningless special characters if present.",
        "Ensure the final output does not exceed 5500 words."
    ]
)





# --------------------------------LIST OF ATTACHMENTS--------------------

attachment_extraction_agent = Agent(
    llm=llm,
    role="Explicit Attachment Identification Specialist",
    goal=(
        "Identify and extract ONLY explicitly labeled attachments from RFP documents "
        "with exact document references and verbatim descriptions"
    ),
    backstory=(
        "You are a procurement documentation analyst trained to:\n"
        "1. Recognize explicitly labeled attachments (e.g., 'Attachment A')\n"
        "2. Extract verbatim titles and descriptions\n"
        "3. Capture mandatory/optional status and special requirements\n"
        "4. Preserve original section references and ordering"
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "The output should be in markdown format."
    )
)

attachment_extraction_task = Task(
    description=(
        "Extract explicit labelled attachments from RFP:\n"
        "1. **Identification Protocol:**\n"
        "   - Look for explicit labels: 'Attachment X', 'Appendix X'\n"
        "   - Verify document section declares it as an attachment\n"
        "2. **Content Extraction:**\n"
        "   - Capture verbatim title/description\n"
        "   - Note down any content related to the attachment\n"
        "   - Record file format requirements (If any)\n"
        "3. **Documentation:**\n"
        "   - Preserve original section numbering\n"
        "   - Maintain attachment order from document\n"
        "4. **Exclusion Criteria:**\n"
        "   - Reject exhibits, FAR clauses, non-labeled items\n"
        "   - Omit inferred or implied attachments\n\n"
        "Document Content:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "[Full, verbatim content as it appears in the document]\n"
        "[Repeat for each relevant section]"
        "OR\n"
        "## No Attachments Found"
    ),
    agent=attachment_extraction_agent,
    constraints=[
        "Only extract explicitly labeled attachments",
        "Preserve original labeling (Attachment vs Appendix)",
        "No inference of unlabeled attachments",
        "Maintain document order"
    ]
)

attachments_summary_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="List of Attachments Summary Drafting Agent",
    goal=(
        "Using the provided procurement content, output only the content related to list of attachments, exactly as provided, reformatting it in markdown for improved readability without rephrasing, omitting, or altering any of the original text. "
        "If a list of attachments is provided, present it in a table with columns 'Attachment ID', 'Title/Description', and 'Additional Notes'. "
        "If no list of attachments is explicitly mentioned, output exactly 'No attachments are explicitly mentioned.'"
    ),
    backstory=(
        "You are an expert procurement/solicitation analyst tasked with creating an accurate, concise summary of explicitly mentioned list of attachments in an RFP document, with a strict 5500-word limit. "
        "Focus only on attachments explicitly listed in the document, and do not include exhibits, FAR clauses, or any unrelated content. "
        "Your responsibility is to reformat the provided content in markdown for improved readability without rephrasing, omitting, or altering any of the original content, ensuring no redundancy."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "Markdown format. The output must begin with '## List of Attachments'. "
        "If attachments are present, display them in a table with columns 'Attachment ID', 'Title/Description', and 'Additional Notes'. "
        "If no attachments are explicitly mentioned, output exactly 'No attachments are explicitly mentioned.'"
    )
)

attachments_summary_drafting_task = Task(
    description=(
        "Using the provided content, format it into a markdown summary that includes only the explicitly labeled list of attachments in a table format. "
        "Do not rephrase, omit, or alter any part of the extracted content; reformat only for improved readability. "
        "If a list of attachments is provided, present them in a table with the following columns:\n\n"
        "| Attachment ID | Title/Description | Additional Notes |\n"
        "| --- | --- | --- |\n"
        "| Attachment ... | ... | ... |\n\n"
        "If no attachments are explicitly mentioned, output exactly:\n\n"
        "## List of Attachments\n\n"
        "No attachments are explicitly mentioned.\n\n"
        "{user_guidelines}\n\n"
        "The extracted procurement content is:\n\n {content}"
    ),
    expected_output=(
        "A markdown formatted summary starting with '## List of Attachments' followed by a table that lists all explicitly extracted attachments with columns 'Attachment ID', 'Title/Description', and 'Additional Notes', "
        "or exactly 'No attachments are explicitly mentioned.' if no such attachments exist."
    ),
    agent=attachments_summary_drafting_agent,
    constraints=[
        "STRICTLY use only the list of attachments content, if provided in the procurement document content.",
        "Do not include any inferred or non-explicit attachments.",
        "Preserve the original labeling, ordering, and all provided details exactly as extracted.",
        "Do not rephrase, summarize, or alter the extracted content in any way.",
        "If no attachment content exists, output exactly 'No attachments are explicitly mentioned.'",
        "Ensure no redundant or duplicated content is present in the final output."
    ]
)



# -----------------------------KEY PERSONNEL--------------------------


key_personnel_extraction_agent = Agent(
    llm=llm,
    role="Key Personnel Requirements Extractor",
    goal=(
        "Identify and extract ONLY explicitly labeled key personnel requirements "
        "from procurement documents with verbatim content and exact section references"
    ),
    backstory=(
        "You are a procurement compliance specialist trained to:\n"
        "1. Recognize explicit 'key personnel' designations\n"
        "2. Extract verbatim content related to key personnel.\n"
        "3. Preserve original document content, structure and references"
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "The output should be in markdown format."
    )
)

key_personnel_extraction_task = Task(
    description=(
        "Extract explicit key personnel requirements:\n"
        "1. **Identification Protocol:**\n"
        "   - Search for explicit 'key personnel' labels\n"
        "   - Verify role-specific designation in text\n"
        "2. **Content Extraction:**\n"
        "   - Capture verbatim key personnel related content.\n"
        "3. **Documentation:**\n"
        "   - Preserve original section numbering, content\n"
        "   - Maintain document order of roles\n"
        "4. **Exclusion Criteria:**\n"
        "   - Reject non-designated roles\n"
        "   - Omit general staffing requirements\n\n"
        "Document Content:\n{content}"
    ),
    expected_output=(
        "[Section Title][Section Reference: Exact Document Section]\n"
        "[Full, verbatim content as it appears in the document]\n"
        "[Repeat for each relevant section]"
        "OR\n"
        "## No Key Personnel Found"
    ),
    agent=key_personnel_extraction_agent,
    constraints=[
        "Only extract explicitly labeled key personnel",
        "Preserve exact qualification wording",
        "No inference of unlabeled roles",
        "Maintain document order"
    ]
)


key_personnel_summary_drafting_agent = Agent(
    llm=llm,  # Your configured LLM
    role="Key Personnel Summary Drafting Agent",
    goal=(
        "Using the provided extracted key personnel content, output the content exactly as provided, reformatting it in markdown for improved readability without rephrasing, omitting, or altering any part of the original text. "
        "Do not add any additional headings beyond those explicitly provided. "
        "If key personnel content is present, present it using the exact structure as extracted. "
        "If no key personnel content is explicitly mentioned, output exactly '## Key Personnel\n\nNo Key Personnel mentioned.'"
    ),
    backstory=(
        "You are an expert procurement analyst tasked with documenting a complete summary of key personnel requirements from an RFP document. "
        "Focus exclusively on roles explicitly designated as 'key personnel' and include all related details exactly as extracted, with correct section references. "
        "Exclude any non-designated roles or general staffing requirements. "
        "Your output must be formatted in markdown for improved readability without rephrasing, omitting, or modifying any of the original text."
    ),
    allow_delegation=False,
    verbose=False,
    output_format=(
        "Markdown format. The output must begin with '## Key Personnel'. For each key personnel role, use the following structure exactly as extracted:\n\n"
        "### [Key Personnel Role/Title]\n"
        "- **Responsibilities**: [Exact extracted content and section reference]\n"
        "- **Qualifications**: [Exact extracted content and section reference]\n"
        "- **Staffing Level**: [Exact extracted content and section reference]\n"
        "- **Resume Requirements**: [Exact extracted content and section reference]\n"
        "- **Special Requirements**: [Exact extracted content and section reference]"
    )
)

key_personnel_summary_drafting_task = Task(
    description=(
        "Using the provided extracted key personnel content, format it into a markdown summary that includes only the explicitly labeled key personnel and all related details exactly as extracted. "
        "Do not rephrase, summarize, or modify any part of the extracted content; reformat only for improved readability. "
        "The summary should follow this exact structure:\n\n"
        "## Key Personnel\n\n"
        "### [Key Personnel Role/Title]\n"
        "- **Responsibilities**: [Exact extracted content and section reference]\n"
        "- **Qualifications**: [Exact extracted content and section reference]\n"
        "- **Staffing Level**: [Exact extracted content and section reference]\n"
        "- **Resume Requirements**: [Exact extracted content and section reference]\n"
        "- **Special Requirements**: [Exact extracted content and section reference]\n\n"
        "If no key personnel are explicitly mentioned, the output should be exactly:\n\n"
        "## Key Personnel\n\nNo Key Personnel mentioned.\n\n"
        "{user_guidelines}\n\n"
        "The extracted procurement content is:\n\n {content}"
    ),
    expected_output=(
        "A markdown formatted summary starting with '## Key Personnel' followed by each key personnel role and its details exactly as extracted, "
        "or exactly '## Key Personnel\n\nNo Key Personnel mentioned.' if no key personnel content is present."
    ),
    agent=key_personnel_summary_drafting_agent,
    constraints=[
        "STRICTLY use only the extracted key personnel content provided from the previous extraction task.",
        "Do not rephrase, omit, or modify any part of the extracted content.",
        "Preserve all original formatting, ordering, and section references exactly as provided.",
        "Do not add any additional headings beyond those explicitly extracted.",
        "If no key personnel content exists, output exactly '## Key Personnel\n\nNo Key Personnel mentioned.'"
    ]
)
