"""Authentication and Authorization Module

This module handles all authentication and authorization related functionality including:
- JWT token generation and validation
- Password hashing and verification
- User authentication
- Email verification
- Password reset functionality
- User management and access control

The module uses JWT for token-based authentication and bcrypt for password hashing.
"""

from fastapi import Depends, HTTPException, status, Request
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from pydantic import BaseModel, EmailStr
from utils.database import users_collection, db, verification_tokens_collection, password_reset_tokens_collection
import os
from bson import ObjectId
import secrets
import logging

# JWT configuration settings
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 4320        # 3 days
VERIFICATION_TOKEN_EXPIRE_MINUTES = 720   # 12 hours
FORGET_PWD_TOKEN_EXPIRE_MINUTES = 30      # 30 minutes

# Initialize password hashing context using bcrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class Token(BaseModel):
    """Token model for JWT authentication response"""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Token payload data model"""
    user_id: Optional[str] = None

class User(BaseModel):
    """Base user model containing user information and status flags"""
    id: Optional[str] = None
    email: EmailStr
    full_name: str
    phone_number: str
    disabled: bool = False
    is_verified: bool = False
    is_approved: bool = False
    is_admin: bool = False

class UserInDB(User):
    """User model with hashed password for database storage"""
    hashed_password: str

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against its hash
    
    Args:
        plain_password: The plain text password to verify
        hashed_password: The bcrypt hashed password from database
        
    Returns:
        bool: True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate a bcrypt hash for a password
    
    Args:
        password: Plain text password to hash
        
    Returns:
        str: Bcrypt hashed password
    """
    return pwd_context.hash(password)

async def get_user(user_id: str):
    """Retrieve a user from the database by their ID
    
    Args:
        user_id: The ObjectId of the user as a string
        
    Returns:
        UserInDB: User object if found, None otherwise
    """
    user_dict = await users_collection.find_one({"_id": ObjectId(user_id)})
    if user_dict:
        user_dict['id'] = str(user_dict['_id'])
        return UserInDB(**user_dict)

async def authenticate_user(email: str, password: str):
    """Authenticate a user with email and password
    
    Args:
        email: User's email address
        password: User's plain text password
        
    Returns:
        UserInDB: User object if authentication successful, False otherwise
    """
    user = await get_user_by_email(email)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token
    
    Args:
        data: Dictionary containing token payload data
        expires_delta: Optional custom expiration time
        
    Returns:
        str: Encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(request: Request):
    """Extract and validate the current user from JWT token
    
    Args:
        request: FastAPI request object containing cookies
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # Get token from cookie
    token = request.cookies.get("access_token")
    if not token:
        raise credentials_exception
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        token_data = TokenData(user_id=user_id)
    except JWTError:
        raise credentials_exception
    
    user = await get_user(user_id=token_data.user_id)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    """Verify that the current user is active, verified, and approved
    
    Args:
        current_user: User object from get_current_user dependency
        
    Returns:
        User: Verified and active user
        
    Raises:
        HTTPException: If user is inactive, unverified, or not approved
    """
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    if not current_user.is_verified:
        raise HTTPException(status_code=400, detail="Email not verified")
    if not current_user.is_approved:
        raise HTTPException(status_code=403, detail="Your account is pending approval")
    return current_user

# Password reset token secret key
FORGET_PWD_SECRET_KEY = os.getenv("FORGET_PWD_SECRET_KEY")

async def get_user_by_email(email: str):
    """Retrieve a user from the database by their email address
    
    Args:
        email: User's email address
        
    Returns:
        UserInDB: User object if found, None otherwise
    """
    user_dict = await users_collection.find_one({"email": email})
    if user_dict:
        user_dict['id'] = str(user_dict['_id'])
        return UserInDB(**user_dict)

async def update_user_password(user_id: str, new_hashed_password: str):
    """Update a user's password in the database
    
    Args:
        user_id: User's ObjectId as string
        new_hashed_password: New bcrypt hashed password
    """
    await users_collection.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": {"hashed_password": new_hashed_password}}
    )

async def create_password_reset_token(email: str) -> str:
    """Create a password reset token for a user
    
    Args:
        email: User's email address
        
    Returns:
        str: Generated reset token
    """
    token = secrets.token_urlsafe(32)
    expires = datetime.utcnow() + timedelta(minutes=FORGET_PWD_TOKEN_EXPIRE_MINUTES)
    
    await password_reset_tokens_collection.insert_one({
        "email": email,
        "token": token,
        "expires": expires
    })
    
    return token

async def check_password_reset_tokens():
    """Clean up expired password reset tokens from the database"""
    current_time = datetime.utcnow()
    result = await password_reset_tokens_collection.delete_many({
        "expires": {"$lt": current_time}
    })
    logging.info(f"Cleaned up {result.deleted_count} expired password reset tokens")

async def verify_password_reset_token(token: str) -> Optional[str]:
    """Verify a password reset token and return associated email if valid
    
    Args:
        token: Reset token to verify
        
    Returns:
        str: Associated email if token is valid, None otherwise
    """
    logging.info(f"Attempting to verify password reset token: {token}")
    
    token_data = await password_reset_tokens_collection.find_one({"token": token})
    
    if not token_data:
        logging.warning(f"Token not found in database: {token}")
        return None
    
    logging.info(f"Token data found: {token_data}")
    
    if datetime.utcnow() > token_data["expires"]:
        logging.warning(f"Token has expired: {token}")
        await password_reset_tokens_collection.delete_one({"token": token})
        return None
    
    email = token_data["email"]
    logging.info(f"Valid token found for email: {email}")
    await password_reset_tokens_collection.delete_one({"token": token})
    return email

async def verify_email_token(token: str) -> Optional[str]:
    """Verify an email verification token
    
    Args:
        token: Verification token to check
        
    Returns:
        str: Associated email if token is valid, None otherwise
    """
    token_data = await verification_tokens_collection.find_one({"token": token})
    
    if not token_data:
        return None
    
    if datetime.utcnow() > token_data["expires"]:
        await verification_tokens_collection.delete_one({"token": token})
        return None
    
    email = token_data["email"]
    await verification_tokens_collection.delete_one({"token": token})
    return email

async def create_verification_token(email: str):
    """Create an email verification token

    Args:
        email: Email address to create token for

    Returns:
        str: Generated verification token
    """
    await verification_tokens_collection.delete_many({"email": email})

    token = secrets.token_urlsafe(32)
    expire = datetime.utcnow() + timedelta(minutes=VERIFICATION_TOKEN_EXPIRE_MINUTES)

    await verification_tokens_collection.insert_one({
        "token": token,
        "email": email,
        "expires": expire,
        "created_at": datetime.utcnow()
    })

    return token

async def is_admin(user: User) -> bool:
    """Check if a user has admin privileges

    Args:
        user: User object to check

    Returns:
        bool: True if user is an admin, False otherwise
    """
    return user.is_admin if user else False
