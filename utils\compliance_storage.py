"""Compliance Data Storage Module

This module provides functionality for storing and retrieving compliance data from Google Cloud Storage.
It follows the same patterns established in content_review.py for GCS operations.
"""

import json
import logging
import asyncio
from typing import Optional, Dict, Any, List
from google.cloud import storage
from google.cloud.exceptions import NotFound
from config import settings
from utils.constants import StoragePaths

logger = logging.getLogger(__name__)


async def save_compliance_data(project_id: str, compliance_data: Dict[str, Any]) -> None:
    """Save compliance data to GCS for a specific project.

    Args:
        project_id: The project ID
        compliance_data: Dictionary containing compliance data with keys:
                        - proposal_format: List of proposal format requirements
                        - proposal_organization: List of proposal organization requirements
                        - compliance_matrix: List of compliance matrix items

    Raises:
        Exception: If there's an error saving the compliance data to GCS
    """
    try:
        # Get project name from MongoDB
        from utils.database import projects_collection
        project_metadata = await projects_collection.find_one({
            "project_id": project_id,
            "$or": [
                {"is_deleted": False},
                {"is_deleted": {"$exists": False}}
            ]
        })

        if not project_metadata:
            raise Exception(f"Project {project_id} not found")

        project_name = project_metadata.get('project_name', 'Untitled Project')

        # Initialize GCS client
        storage_client = storage.Client()
        bucket = storage_client.bucket(settings.BUCKET_NAME)

        # Construct the file path using the same pattern as other project files
        project_prefix = StoragePaths.PROJECT_PREFIX_TEMPLATE.format(
            project_name=project_name,
            project_id=project_id
        )
        file_path = f"{project_prefix}{StoragePaths.COMPLIANCE_FOLDER}{StoragePaths.COMPLIANCE_FILE}"

        # Get blob reference
        blob = bucket.blob(file_path)

        # Prepare the data structure to save
        data_to_save = {
            "project_id": project_id,
            "compliances": compliance_data
        }

        # Save to GCS using asyncio.to_thread for async operation
        await asyncio.to_thread(blob.upload_from_string, json.dumps(data_to_save, indent=2))

        logger.info(f"Successfully saved compliance data to GCS: {file_path}")

    except Exception as e:
        logger.error(f"Error saving compliance data to GCS: {str(e)}")
        raise Exception(f"Error saving compliance data: {str(e)}")


async def get_compliance_data(project_id: str) -> Optional[Dict[str, Any]]:
    """Retrieve compliance data from GCS for a specific project.

    Args:
        project_id: The project ID

    Returns:
        Optional[Dict[str, Any]]: Compliance data if found, None if not found

    Raises:
        Exception: If there's an error retrieving the compliance data from GCS
    """
    try:
        # Get project name from MongoDB
        from utils.database import projects_collection
        project_metadata = await projects_collection.find_one({
            "project_id": project_id,
            "$or": [
                {"is_deleted": False},
                {"is_deleted": {"$exists": False}}
            ]
        })

        if not project_metadata:
            logger.info(f"Project {project_id} not found")
            return None

        project_name = project_metadata.get('project_name', 'Untitled Project')

        # Initialize GCS client
        storage_client = storage.Client()
        bucket = storage_client.bucket(settings.BUCKET_NAME)

        # Construct the file path
        project_prefix = StoragePaths.PROJECT_PREFIX_TEMPLATE.format(
            project_name=project_name,
            project_id=project_id
        )
        file_path = f"{project_prefix}{StoragePaths.COMPLIANCE_FOLDER}{StoragePaths.COMPLIANCE_FILE}"

        # Get blob reference
        blob = bucket.blob(file_path)

        # Check if file exists
        if not await asyncio.to_thread(blob.exists):
            logger.info(f"No compliance data found for project {project_id}")
            return None

        # Download and parse content
        content = await asyncio.to_thread(blob.download_as_text)
        compliance_data = json.loads(content)

        logger.info(f"Successfully retrieved compliance data from GCS: {file_path}")
        return compliance_data

    except NotFound:
        logger.info(f"Compliance data file not found for project {project_id}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format in compliance data: {str(e)}")
        raise Exception(f"Invalid JSON format in compliance data: {str(e)}")
    except Exception as e:
        logger.error(f"Error retrieving compliance data from GCS: {str(e)}")
        raise Exception(f"Error retrieving compliance data: {str(e)}")


async def get_all_compliance_data() -> List[Dict[str, Any]]:
    """Retrieve all compliance data from GCS across all projects.

    Returns:
        List[Dict[str, Any]]: List of all compliance data found

    Raises:
        Exception: If there's an error retrieving compliance data from GCS
    """
    try:
        # Initialize GCS client
        storage_client = storage.Client()
        bucket = storage_client.bucket(settings.BUCKET_NAME)

        # List all blobs that match the compliance file pattern
        compliance_files = []
        prefix = "Projects/"
        
        # Use asyncio.to_thread for the list_blobs operation
        blobs = await asyncio.to_thread(
            lambda: list(bucket.list_blobs(prefix=prefix))
        )
        
        for blob in blobs:
            if blob.name.endswith(f"{StoragePaths.COMPLIANCE_FOLDER}{StoragePaths.COMPLIANCE_FILE}"):
                compliance_files.append(blob)

        logger.info(f"Found {len(compliance_files)} compliance files")

        # Download and parse each compliance file
        all_compliance_data = []
        for blob in compliance_files:
            try:
                content = await asyncio.to_thread(blob.download_as_text)
                compliance_data = json.loads(content)
                all_compliance_data.append(compliance_data)
            except Exception as e:
                logger.warning(f"Error reading compliance file {blob.name}: {str(e)}")
                continue

        logger.info(f"Successfully retrieved {len(all_compliance_data)} compliance records")
        return all_compliance_data

    except Exception as e:
        logger.error(f"Error retrieving all compliance data from GCS: {str(e)}")
        raise Exception(f"Error retrieving all compliance data: {str(e)}")


async def delete_compliance_data(project_id: str) -> bool:
    """Delete compliance data from GCS for a specific project.

    Args:
        project_id: The project ID

    Returns:
        bool: True if deletion was successful, False if file didn't exist

    Raises:
        Exception: If there's an error deleting the compliance data from GCS
    """
    try:
        # Get project name from MongoDB
        from utils.database import projects_collection
        project_metadata = await projects_collection.find_one({
            "project_id": project_id,
            "$or": [
                {"is_deleted": False},
                {"is_deleted": {"$exists": False}}
            ]
        })

        if not project_metadata:
            logger.info(f"Project {project_id} not found")
            return False

        project_name = project_metadata.get('project_name', 'Untitled Project')

        # Initialize GCS client
        storage_client = storage.Client()
        bucket = storage_client.bucket(settings.BUCKET_NAME)

        # Construct the file path
        project_prefix = StoragePaths.PROJECT_PREFIX_TEMPLATE.format(
            project_name=project_name,
            project_id=project_id
        )
        file_path = f"{project_prefix}{StoragePaths.COMPLIANCE_FOLDER}{StoragePaths.COMPLIANCE_FILE}"

        # Get blob reference
        blob = bucket.blob(file_path)

        # Check if file exists
        if not await asyncio.to_thread(blob.exists):
            logger.info(f"No compliance data found to delete for project {project_id}")
            return False

        # Delete the file
        await asyncio.to_thread(blob.delete)

        logger.info(f"Successfully deleted compliance data from GCS: {file_path}")
        return True

    except Exception as e:
        logger.error(f"Error deleting compliance data from GCS: {str(e)}")
        raise Exception(f"Error deleting compliance data: {str(e)}")
