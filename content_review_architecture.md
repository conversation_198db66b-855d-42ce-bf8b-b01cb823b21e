# Content Review Architecture

This document outlines the architecture and workflow for the professional proposal reviewer system that evaluates proposal content against specific evaluation rules.

## Overview

The Content Review system is designed as a professional proposal reviewer that applies individual evaluation rules to entire proposal documents, providing comprehensive assessments with structured feedback and scoring.

## System Architecture

### Core Components

1. **Proposal Reviewer Engine**: The main evaluation processor
2. **Rule Application Module**: Handles individual evaluation rule processing
3. **Content Analysis Engine**: Processes entire proposal documents
4. **JSON Output Formatter**: Ensures strict JSON compliance
5. **Validation Layer**: Ensures output format compliance

## Data Transformations and Flow

### Input Data
- **Evaluation Rule**: Single rule with name, type, and evaluation criteria
- **Proposal Content**: Complete proposal document to be evaluated
- **Rule Name**: Identifier for the specific evaluation rule
- **Rule Type**: Category/classification of the evaluation rule

### Output Data
- **Structured JSON Response**: Contains rule_name, rule_type, score, feedback, and suggestions
- **Standardized Scoring**: X/10 scale or N/A for non-applicable rules
- **Actionable Feedback**: Observations and improvement suggestions

### Transformation Process
The system transforms unstructured proposal content and evaluation rules into structured, actionable assessment data.

## Workflow Architecture

```mermaid
flowchart TD
    A[Start: Professional Proposal Reviewer] --> B[Receive Inputs]
    B --> C[Parse Evaluation Rule]
    B --> D[Load Proposal Content]
    
    C --> E[Extract Rule Components<br/>- Rule Name<br/>- Rule Type<br/>- Evaluation Criteria]
    D --> F[Process Entire Proposal Document<br/>- Content Analysis<br/>- Context Understanding<br/>- Requirement Mapping]
    
    E --> G[Rule Analysis Complete]
    F --> H[Content Analysis Complete]
    
    G --> I[Apply Rule to Entire Document]
    H --> I
    
    I --> J[Comprehensive Evaluation<br/>- Document-wide assessment<br/>- Rule compliance check<br/>- Evidence gathering]
    
    J --> K[Generate Assessment Components]
    K --> L[Create Score<br/>X/10 or N/A]
    K --> M[Generate Feedback<br/>Observations about rule reflection]
    K --> N[Develop Suggestions<br/>Improvement recommendations]
    
    L --> O[Compile JSON Response]
    M --> O
    N --> O
    
    O --> P{Validation Check}
    P -->|Invalid Format| Q[Format Correction<br/>- Ensure string fields<br/>- Remove arrays/lists<br/>- Validate JSON structure]
    Q --> P
    
    P -->|Valid Format| R[Output Strict JSON]
    R --> S[End]

    style A fill:#e1f5fe
    style I fill:#f3e5f5
    style R fill:#c8e6c9
    style S fill:#ffcdd2
    style P fill:#fff3e0
```

## Process Details

### 1. Input Processing Phase
- **Rule Parsing**: Extracts rule name, type, and evaluation criteria
- **Content Loading**: Processes the entire proposal document for comprehensive analysis
- **Context Preparation**: Sets up evaluation context for document-wide assessment

### 2. Evaluation Phase
- **Holistic Analysis**: Applies the evaluation rule to the entire proposal document
- **Evidence Collection**: Gathers supporting evidence from across the document
- **Compliance Assessment**: Determines how well the proposal meets the specific rule

### 3. Assessment Generation Phase
- **Scoring**: Assigns numerical score (X/10) or N/A for non-applicable rules
- **Feedback Creation**: Develops observations about rule reflection across the proposal
- **Suggestion Development**: Creates actionable improvement recommendations

### 4. Output Formatting Phase
- **JSON Compilation**: Assembles all assessment components into structured JSON
- **Format Validation**: Ensures strict compliance with output requirements
- **Quality Assurance**: Verifies all fields are strings and properly formatted

## Output Specifications

### JSON Structure
```json
{
  "rule_name": "string - Name of the applied evaluation rule",
  "rule_type": "string - Type/category of the evaluation rule", 
  "score": "string - X/10 numerical score or N/A",
  "feedback": "string - Comprehensive observation about rule reflection",
  "suggestions": "string - Improvement recommendations or N/A"
}
```

### Format Requirements
- **Strict JSON Only**: No markdown formatting or code blocks
- **String Fields Only**: All values must be strings, no arrays or lists
- **Raw Output**: Must start with '{' and end with '}'
- **No External Text**: Only JSON content in response
- **Paragraph Format**: Tips and suggestions as single paragraphs or numbered lines within strings

## Key Principles

1. **Document-Wide Evaluation**: Rules are applied to the entire proposal, not individual sections
2. **Single Rule Focus**: Each evaluation session processes one rule comprehensively
3. **Structured Output**: Consistent JSON format for all evaluations
4. **Actionable Feedback**: Observations and suggestions that provide clear guidance
5. **Format Compliance**: Strict adherence to JSON output requirements

## Benefits

- **Comprehensive Assessment**: Evaluates entire documents for complete coverage
- **Consistent Scoring**: Standardized 10-point scale for comparable results
- **Actionable Insights**: Specific feedback and improvement suggestions
- **Structured Data**: JSON format enables easy integration and processing
- **Quality Assurance**: Validation ensures reliable, consistent outputs
