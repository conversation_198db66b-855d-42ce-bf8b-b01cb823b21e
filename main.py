"""Document Summarization API Main Application

This is the main entry point for the Document Summarization API service. It configures and
initializes the FastAPI application, sets up middleware, logging, and routes.

Features:
- FastAPI application configuration
- CORS middleware for cross-origin requests
- Structured logging setup
- Route organization by functionality
- Database initialization
- Development server configuration

The application provides endpoints for:
- User authentication and management
- Project handling
- Document summarization
- Outline generation
- File management
- Content editing and version management
- Content version retrieval and history
- Administrative functions
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from utils.database import setup_database
import os

# Import route modules organized by functionality
from routes import (
    auth_routes,
    compliance_routes,      # Compliance endpoints
    user_routes,      # User management endpoints
    project_routes,   # Project handling endpoints
    summary_routes,   # Document summarization endpoints
    outline_routes,   # Outline generation endpoints
    admin_routes,     # Administrative endpoints
    file_routes,      # File management endpoints
    content_routes,   # Content editing endpoints
    version_routes,   # Content version management endpoints
    response_content_routes,  # Response content endpoints
    content_review_routes,    # Content review endpoints
    health_routes     # Health check and monitoring endpoints
)

# Initialize FastAPI application with metadata
app = FastAPI(title="Document Summarization API")

# Print all registered routes for debugging
def print_routes(app):
    print("Registered routes:")
    for route in app.routes:
        print(f"{route.path}  {route.methods}")

print_routes(app)

# Configure CORS middleware to handle cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://localhost:8000",          # Local development
        "https://ddbtuui4e9bya.cloudfront.net",  # Production frontend
    ],
    allow_credentials=True,  # Allow credentials (cookies, authorization headers)
    allow_methods=["*"],     # Allow all HTTP methods
    allow_headers=["*"],     # Allow all headers
)

# Set up logging configuration
log_directory = "Logs"
if not os.path.exists(log_directory):
    os.makedirs(log_directory)

# Configure logging with both file and console output
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_directory, "Summarization_api_logs.log"), encoding="utf-8"),  # File logging
        logging.StreamHandler(),  # Console logging
    ],
)
logger = logging.getLogger(__name__)

# Register route handlers organized by functionality
app.include_router(health_routes.router, tags=["Health"])
app.include_router(auth_routes.router, tags=["Authentication"])
app.include_router(user_routes.router, tags=["Users"])
app.include_router(project_routes.router, tags=["Projects"])
app.include_router(summary_routes.router, tags=["Summaries"])
app.include_router(outline_routes.router, tags=["Outlines"])
app.include_router(admin_routes.router, tags=["Admin"])
app.include_router(file_routes.router, tags=["Files"])
app.include_router(content_routes.router, tags=["Content Editing"])
app.include_router(version_routes.router, tags=["Content Versions"])
app.include_router(response_content_routes.router, tags=["Response Content"])
app.include_router(content_review_routes.router, tags=["Content Review"])
app.include_router(compliance_routes.router, tags=["Compliance"])
@app.on_event("startup")
async def startup_event():
    """Initialize application dependencies on startup.

    This function runs when the FastAPI application starts up.
    It handles database initialization and any other required startup tasks.
    """
    await setup_database()

if __name__ == "__main__":
    logger.info("Starting FastAPI application")
    import uvicorn
    # Run the development server
    uvicorn.run(app, host="127.0.0.1", port=8000)