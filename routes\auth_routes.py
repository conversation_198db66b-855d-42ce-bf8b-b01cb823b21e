"""Authentication routes module for user authentication and password management.

This module provides FastAPI routes for user authentication operations including:
- User login with token generation
- Password reset functionality (forgot password flow)
- Password reset validation and execution
- Email verification
- User logout
All endpoints handle proper logging and secure cookie management.
"""

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPEx<PERSON>, Response, <PERSON><PERSON>
from bson import ObjectId
from fastapi.responses import JSONResponse
from utils.constants import HTTPStatus
from fastapi.security import OAuth2PasswordRequestForm
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from datetime import timedelta
import logging

from utils.auth import (
    ACCESS_TOKEN_EXPIRE_MINUTES, authenticate_user, create_access_token,
    get_current_active_user, get_user_by_email, verify_password_reset_token,
    create_password_reset_token, update_user_password, check_password_reset_tokens, get_password_hash,
    verify_email_token
)
from config import settings
from schemas import LoginRequest, ForgetPasswordRequest, ResetPasswordRequest
from utils.auth import Token, User
from utils.database import users_collection

router = APIRouter()
logger = logging.getLogger(__name__)

async def send_password_reset_email(email: str, reset_link: str):
    """Send a password reset email to the user.

    Args:
        email (str): The recipient's email address
        reset_link (str): The password reset link to be included in the email

    Raises:
        Exception: If email sending fails
    """
    logger.info(f"Preparing to send password reset email to {email}")
    conf = ConnectionConfig(
        MAIL_USERNAME=settings.MAIL_USERNAME,
        MAIL_PASSWORD=settings.MAIL_PASSWORD,
        MAIL_FROM=settings.MAIL_FROM,
        MAIL_PORT=settings.MAIL_PORT,
        MAIL_SERVER=settings.MAIL_SERVER,
        MAIL_FROM_NAME=settings.MAIL_FROM_NAME,
        MAIL_STARTTLS=True,
        MAIL_SSL_TLS=False,
        USE_CREDENTIALS=True,
        VALIDATE_CERTS=True,
    )

    message = MessageSchema(
        subject="Password Reset",
        recipients=[email],
        body=f"Click the following link to reset your password: {reset_link}",
        subtype="html",
    )

    fm = FastMail(conf)
    await fm.send_message(message)

@router.post("/login", response_model=Token)
async def login(response: Response, login_data: LoginRequest):
    """Handle user login and token generation.

    Args:
        response (Response): FastAPI response object for setting cookies
        login_data (LoginRequest): User login credentials

    Returns:
        Token: Access token and token type

    Raises:
        HTTPException: 401 for invalid credentials
                      403 if email not verified or account not approved
    """
    logger.info(f"Login attempt for user: {login_data.username}")
    user = await authenticate_user(login_data.username, login_data.password)
    if not user:
        logger.warning(
            f"Login failed for user {login_data.username}: Incorrect username or password"
        )
        raise HTTPException(
            status_code=HTTPStatus.UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user's email is verified
    if not user.is_verified:
        logger.warning(f"Login failed for user {user.full_name}: Email not verified")
        raise HTTPException(
            status_code=HTTPStatus.FORBIDDEN,
            detail="Email not verified. Please verify your email before logging in.",
        )

    # Check if user's account is approved
    if not user.is_approved:
        logger.warning(f"Login failed for user {user.full_name}: Account not approved")
        raise HTTPException(
            status_code=HTTPStatus.FORBIDDEN,
            detail="Your account is pending approval. Please wait for administrator approval.",
        )

    # Generate access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )

    # Set secure HTTP-only cookie with the access token
    response.set_cookie(
        key="access_token",
        value=access_token,
        httponly=True,
        secure=True,
        samesite="None",
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        expires=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        path="/",
    )

    logger.info(f"User {user.full_name} logged in successfully")
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/forgot-password")
async def forgot_password(request: ForgetPasswordRequest):
    """Handle forgot password requests by sending reset links.

    Args:
        request (ForgetPasswordRequest): Contains the user's email

    Returns:
        JSONResponse: Success message (same response whether email exists or not for security)

    Raises:
        HTTPException: 500 if token creation or email sending fails
    """
    logger.info(f"Password reset requested for email: {request.email}")
    user = await get_user_by_email(request.email)
    if not user:
        logger.warning(f"User not found for email: {request.email}")
        return JSONResponse(
            status_code=HTTPStatus.OK,
            content={"message": "If the email exists, a password reset link has been sent."}
        )

    # Create password reset token
    token = await create_password_reset_token(user.email)
    if not token:
        logger.error(f"Failed to create password reset token for email: {request.email}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Failed to create password reset token")

    reset_link = f"{settings.APP_HOST}/reset-password?token={token}"
    logger.info(f"Reset link generated: {reset_link}")

    try:
        await send_password_reset_email(user.email, reset_link)
        logger.info(f"Password reset link sent to email: {request.email}")
    except Exception as e:
        logger.error(f"Failed to send password reset email: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Failed to send password reset email")

    return JSONResponse(
        status_code=HTTPStatus.OK,
        content={"message": "If the email exists, a password reset link has been sent."}
    )

@router.get("/reset-password")
async def reset_password_validate(token: str):
    """Validate a password reset token.

    Args:
        token (str): The password reset token to validate

    Returns:
        JSONResponse: Success message with email if token is valid

    Raises:
        HTTPException: 400 if token is invalid or expired
    """
    logger.info(f"Validating password reset token: {token}")

    # Clean up expired tokens
    await check_password_reset_tokens()

    email = await verify_password_reset_token(token)
    if not email:
        logger.error(f"Invalid or expired token: {token}")
        return JSONResponse(
            status_code=HTTPStatus.BAD_REQUEST,
            content={"detail": "Invalid or expired token"}
        )

    logger.info(f"Valid token found for email: {email}")
    return JSONResponse(
        status_code=HTTPStatus.OK,
        content={"message": "Token is valid", "email": email}
    )

@router.post("/reset-password")
async def reset_password(request: ResetPasswordRequest):
    """Reset user's password using a valid reset token.

    Args:
        request (ResetPasswordRequest): Contains the reset token and new password

    Returns:
        JSONResponse: Success message after password reset

    Raises:
        HTTPException: 400 if token is invalid
                      404 if user not found
    """
    logger.info("Password reset attempt")
    email = await verify_password_reset_token(request.token)
    if not email:
        logger.error(f"Invalid or expired token: {request.token}")
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail="Invalid or expired token")

    user = await get_user_by_email(email)
    if not user:
        logger.error(f"User not found for email: {email}")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="User not found")

    # Hash the new password and update in database
    hashed_password = get_password_hash(request.new_password)
    await update_user_password(user.id, hashed_password)
    logger.info(f"Password reset successful for user: {user.full_name}")
    return JSONResponse(
        status_code=HTTPStatus.OK,
        content={"message": "Password reset successful"}
    )

@router.get("/verify-email")
async def verify_email(token: str):
    """Verify a user's email address using a verification token.

    Args:
        token (str): The email verification token

    Returns:
        dict: Success message after email verification

    Raises:
        HTTPException: 400 if token is invalid or expired
                      404 if user not found
    """
    logger.info("Verifying email with token")
    email = await verify_email_token(token)
    if not email:
        logger.error("Invalid or expired token provided for email verification")
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail="Invalid or expired token")

    user = await get_user_by_email(email)
    if not user:
        logger.error(f"User not found for email: {email}")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="User not found")

    if user.is_verified:
        logger.info(f"Email {email} is already verified")
        return {"message": "Email already verified"}

    await users_collection.update_one(
        {"_id": ObjectId(user.id)}, {"$set": {"is_verified": True}}
    )

    logger.info(f"Email {email} verified successfully")
    return {"message": "Email verified successfully"}

@router.post("/logout")
async def logout(response: Response):
    """Handle user logout by removing the access token cookie.

    Args:
        response (Response): FastAPI response object for cookie manipulation

    Returns:
        dict: Success message
    """
    response.delete_cookie(
        key="access_token",
        httponly=True,
        secure=True,
        samesite="None",
        path="/"
    )
    logger.info("User logged out successfully")
    return {"message": "Logged out successfully"}