"""Vector database management module.

This module handles all vector database operations including:
- Vector database initialization and updates
- Document chunking and embedding
- Vector search operations
"""

import asyncio
import logging
from typing import Any, List, Optional

from models import ProjectDocument
from utils.constants import StoragePaths


class VectorDBManager:
    """Manages vector database operations for projects.

    This class handles all aspects of vector database management including:
    - Vector database initialization and updates
    - Document chunking and embedding
    - Vector search operations

    Attributes:
        project_id: Unique identifier for the project
        project_prefix: Project-specific path prefix in storage
        vector_db_path: Path to the vector database in storage
        vector_db: Vector database instance
    """

    def __init__(self, project_id: str, project_prefix: str, vector_db_path: str):
        """Initialize VectorDBManager instance.

        Args:
            project_id: Unique identifier for the project
            project_prefix: Project-specific path prefix in storage
            vector_db_path: Path to the vector database in storage
        """
        self.project_id = project_id
        self.project_prefix = project_prefix
        self.vector_db_path = vector_db_path
        self.vector_db = None

    async def update_vector_db(self, document: ProjectDocument):
        """Update project's vector database with a new document.

        Args:
            document: ProjectDocument instance to add to the vector database

        Raises:
            Exception: If vector database update fails
        """
        from RAG.chunking import default_text_chunking
        from RAG.vector_db import get_embeddings, create_vector_db, load_vector_db, save_vector_db

        try:
            # Create chunks with metadata including tags
            chunks = await asyncio.to_thread(default_text_chunking, [document])
            for chunk in chunks:
                chunk.metadata.update({
                    "tags": document.tags,
                    "filename": document.file_name
                })

            # Try to load existing vector DB from GCS
            try:
                self.vector_db = await load_vector_db(self.vector_db_path)
                # Add new chunks to existing DB
                await asyncio.to_thread(self.vector_db.add_documents, chunks)
            except Exception as e:
                logging.info(f"No existing vector DB found or error loading: {str(e)}. Creating new one.")
                # Create new vector DB if none exists
                embeddings = await get_embeddings()
                self.vector_db = await create_vector_db(chunks, embeddings)

            # Save updated vector DB to GCS
            await save_vector_db(self.vector_db, self.vector_db_path)
            logging.info(f"Updated vector DB for project {self.project_id} with document {document.file_name}")

        except Exception as e:
            logging.error(f"Error updating vector DB: {str(e)}")
            raise

    async def get_pickled_sections_content(self):
        """Read and unpickle the extracted sections content from GCS.

        Returns:
            The unpickled sections content, or None if file doesn't exist

        Raises:
            Exception: If there's an error reading or unpickling the content
        """
        try:
            from google.cloud import storage

            # Get the storage client
            storage_client = storage.Client()
            bucket = storage_client.bucket("mvp-development-431809_cloudbuild")

            # Get the blob reference
            sections_blob = bucket.blob(f"{self.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}sections.pkl")

            # Download and unpickle the content
            loop = asyncio.get_event_loop()
            try:
                pickled_content = await loop.run_in_executor(None, sections_blob.download_as_bytes)

                # Unpickle the content
                import pickle
                sections_content = pickle.loads(pickled_content)
                logging.info(f"Successfully loaded pickled sections content for project {self.project_id}")
                return sections_content
            except Exception as e:
                logging.error(f"Error downloading or unpickling sections content: {str(e)}")
                return None

        except Exception as e:
            logging.error(f"Error loading pickled sections content: {str(e)}")
            return None
