"""Document Processing Models Module

This module defines the core data models used in the document processing system.
It provides classes for handling document sections and project documents,
with support for various file formats and content processing capabilities.

Key Components:
- DocumentSection: Represents a section of a document with processing configuration
- ProjectDocument: Handles document file processing and content management
- Support for PDF and DOCX file formats
- Special page handling (tables, images)
- Content extraction and processing utilities

The module integrates with external utilities for text extraction and processing,
while maintaining a clean interface for document handling throughout the application.
"""

import pickle
from typing import List, Dict, Optional, Any, Union
from dataclasses import dataclass, field
import fitz
import logging
from utils import extract_sections, read_docx, read_excel, identify_special_pages
import tempfile
import os
from RAG.documents_loader import extract_page_as_image, extract_text_with_vision
import aiofiles
import aiofiles.os as aiofiles_os
import uuid
import asyncio
import shutil

@dataclass
class DocumentSection:
    """Represents a section of a document with its processing configuration and content.
    
    This class handles individual document sections, their prompts, processing settings,
    and maintains the state of processed content including tokens and responses.
    
    Attributes:
        section_title: Name/title of the section
        section_prompt: Prompt template for processing this section
        source: Source identifier for the section
        model_name: Name of the LLM model to use
        config_settings: Dictionary of model configuration parameters
        final_response: Processed response for the section
        user_guidelines: Additional processing guidelines
        input_tokens: Count of input tokens used
        output_tokens: Count of output tokens generated
    """
    section_title: str
    section_prompt: str
    source: str
    model_name: str
    config_settings: Dict[str, any]
    final_response: str = ""
    user_guidelines: str = ""
    input_tokens: int = 0
    output_tokens: int = 0

    def generate_prompt(self, content: List) -> str:
        """Generate a processing prompt by combining section content and guidelines.
        
        Args:
            content: List of content items to process (can be dictionaries, strings, or lists)
            
        Returns:
            str: Generated prompt combining content and guidelines
        """
        processed_content = []  # List to hold processed content
        
        logging.info(f"Processing content from {len(content)} documents")
        for item_index, item in enumerate(content):
            if isinstance(item, dict):
                if 'content' in item:
                    processed_content.append(item['content'])
                else:
                    logging.warning(f"Key 'content' not found in dictionary")
            elif isinstance(item, str):
                processed_content.append(item)  # Add strings directly
            elif isinstance(item, list):
                # Track content from this document
                doc_content = []
                page_count = 0
                total_length = 0
                
                for sub_item in item:
                    if isinstance(sub_item, dict) and 'page_number' in sub_item and 'content' in sub_item:
                        content_str = f"\nPage No: {sub_item['page_number']} \n\n{sub_item['content']}"
                        doc_content.append(content_str)
                        page_count += 1
                        total_length += len(sub_item['content'])
                    else:
                        logging.warning(f"Skipping invalid page content")
                
                # Add separator between documents
                if doc_content:
                    if processed_content:  # If we already have content from another document
                        processed_content.append("\n\n------- Next Document -------\n\n")
                    processed_content.extend(doc_content)
                    logging.info(f"Processed {page_count} pages with total length: {total_length}")
            else:
                logging.warning(f"Skipping unsupported content type: {type(item)}")
        
        # Combine processed content into a single string 
        content_str = "\n".join(processed_content)
        full_prompt = self.section_prompt

        if self.user_guidelines:
            full_prompt += f"""\n\n**Additional Guidelines:**\n{self.user_guidelines}\n\n *VERY IMPORTANT NOTE: If any instruction in the Additional Guidelines, conflicts with the previous instructions, always follow/Prioritize the Additional Guidelines. *"""
        
        return full_prompt.replace("{content}", content_str)

    def set_response(self, response: str, input_tokens: int, output_tokens: int):
        """Set the processed response and token counts for the section.
        
        Args:
            response: Generated response text
            input_tokens: Number of input tokens used
            output_tokens: Number of output tokens generated
        """
        self.final_response = response
        self.input_tokens = input_tokens
        self.output_tokens = output_tokens

    def add_user_guidelines(self, guidelines: str):
        """Add or update user guidelines for the section.
        
        Args:
            guidelines: New guidelines to apply
        """
        self.user_guidelines = guidelines

    def remove_user_guidelines(self):
        """Remove all user guidelines from the section."""
        self.user_guidelines = None

class ProjectDocument:
    """Handles document file processing and content management for a project.
    
    This class manages document files, their content extraction, and special page handling.
    It supports multiple file formats (PDF, DOCX) and maintains document metadata.
    
    Attributes:
        file_name: Name of the document file
        tags: List of tags associated with the document
        sections: List of extracted document sections
        sf_pages: List of pages with special formatting
        image_pages: List of pages containing images
        metadata: Dictionary containing document metadata
    """
    def __init__(self, file_name: str, tags: List[str] = None, sections=None, sf_pages: List[int] = None, image_pages: List[int] = None):
        """Initialize a ProjectDocument instance.
        
        Args:
            file_name: Name of the document file
            tags: Optional list of document tags
            sections: Optional list of pre-processed sections
            sf_pages: Optional list of specially formatted page numbers
            image_pages: Optional list of page numbers containing images
        """
        self.file_name = file_name
        self.tags = tags or []
        self.sections = sections or []
        self.sf_pages = sf_pages or []
        self.image_pages = image_pages or []
        # Initialize metadata
        self.metadata = {
            "source": file_name,
            "tags": self.tags,
            "sf_pages": self.sf_pages,
            "image_pages": self.image_pages,
            "total_pages": len(self.sections) if self.sections else 0
        }

    @classmethod
    async def create(cls, file_content: bytes, file_name: str, tags: List[str] = None, sf_pages: List[int] = None, identified_tables: List[int] = None, identified_images: List[int] = None):
        """Create a new ProjectDocument instance from file content.
        
        Args:
            file_content: Binary content of the document file
            file_name: Name of the document file
            tags: Optional list of document tags
            sf_pages: Optional list of specially formatted pages
            identified_tables: Optional list of pre-identified table pages
            identified_images: Optional list of pre-identified image pages
            
        Returns:
            ProjectDocument: New instance with processed content
            
        Raises:
            ValueError: If file format is not supported
        """
        file_extension = file_name.split('.')[-1].lower()
        sections = None

        logging.info(f"Creating ProjectDocument for {file_name} with File format: {file_extension}")
        if file_extension in ['pdf']:
            logging.info(f"Entered PDF processing Block for {file_name}")
            doc = fitz.open(stream=file_content, filetype="pdf")
            # Use pre-identified pages if provided, otherwise detect them
            if identified_tables is None or identified_images is None:
                tables, images = await identify_special_pages(file_content)
            else:
                tables, images = identified_tables, identified_images
            
            # If sf_pages is not provided, use identified tables
            if sf_pages is None:
                sf_pages = tables
            sections = await extract_sections(doc, text_format='md', sf_pages=sf_pages, image_pages=images)
            doc.close()
        elif file_extension in ['docx']:
            logging.info(f"Entered DOCX processing Block for {file_name}")
            sf_pages = []
            images = []
            logging.info("The sf_pages and images are set to empty list as they are not needed in case if file is DOCX")
            sections = read_docx(file_content)
        elif file_extension in ['xlsx', 'xls']:
            logging.info(f"Entered Excel processing Block for {file_name}")
            sf_pages = []
            images = []
            logging.info("The sf_pages and images are set to empty list as they are not needed in case if file is Excel")
            sections = read_excel(file_content)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")

        instance = cls(file_name, tags, sections, sf_pages, images)
        # Update total pages in metadata after sections are created
        instance.metadata["total_pages"] = len(sections) if sections else 0
        return instance

    def get_content(self) -> str:
        """Get the complete content of the document.
        
        Returns:
            str: Combined content of all document sections
        """
        return "\n".join(page['content'] for page in self.sections)

    def get_page_content(self, page_number: int) -> Optional[str]:
        """Get content of a specific page.
        
        Args:
            page_number: Page number to retrieve
            
        Returns:
            Optional[str]: Content of the specified page, or None if not found
        """
        for page in self.sections:
            if page['page_number'] == page_number:
                return page['content']
        return None

    def is_sf_page(self, page_number: int) -> bool:
        """Check if a page has special formatting.
        
        Args:
            page_number: Page number to check
            
        Returns:
            bool: True if page has special formatting
        """
        return page_number in self.sf_pages

    def is_image_page(self, page_number: int) -> bool:
        """Check if a page contains images.
        
        Args:
            page_number: Page number to check
            
        Returns:
            bool: True if page contains images
        """
        return page_number in self.image_pages