"""File Processing Utilities Module

This module provides functionality for processing different file types, including:
- Special page identification in PDFs
- Reading and processing DOCX files
- Reading and processing Excel files
- Image analysis

Key Features:
- PDF page analysis
- Document content extraction
- File format handling
- Image validation
"""

import io
import asyncio
import pdfplumber
import logging
import tempfile
import os
import fitz
from PIL import Image, ImageStat
import pytesseract
from config import settings
import pandas as pd
from utils.pdf_utils import is_table_dominant_page, has_solicitation_content
from markitdown import MarkItDown

# Import RAG functions where they are used to avoid circular imports
extract_page_as_image = None
extract_text_with_vision = None

def _load_rag_functions():
    global extract_page_as_image, extract_text_with_vision
    if extract_page_as_image is None:
        from RAG.documents_loader import extract_page_as_image, extract_text_with_vision

async def identify_special_pages(file_content):
    pages_with_tables = []
    pages_with_images = []

    try:
        # Create temporary file to write PDF content
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(file_content)
            temp_path = temp_file.name

        # Open PDF with PyMuPDF for both table and image detection (single traversal)
        logging.info("OPTIMIZATION: Opening PDF once for both table and image detection")
        doc = fitz.open(temp_path)

        # Also open with pdfplumber for table extraction (but reuse the same temp file)
        with pdfplumber.open(temp_path) as pdf:
            total_pages = len(pdf.pages)

            # Process only first half of pages for table detection (maintaining original logic)
            for page_num in range(total_pages//2):
                # Extract tables from the page (pdfplumber uses 0-based indexing)
                page = pdf.pages[page_num]
                tables = page.extract_tables()

                # If tables are found and not empty
                if tables and any(table for table in tables if table):
                    logging.info(f"Tables found on page {page_num + 1}")
                    logging.info("------------------------------------------------TABLES------------------------------------------------")
                    for i, table in enumerate(tables[:3]):  # Show up to first 3 tables for logging
                        table_str = str(table)
                        table_preview = table_str[:200] + "..." if len(table_str) > 200 else table_str
                        logging.info(f"Table {i+1}: {table_preview}")
                    logging.info("------------------------------------------------TABLES------------------------------------------------")

                    # Check if tables dominate the page content or contain solicitation content
                    logging.info(f"Analyzing page {page_num + 1} for table dominance and solicitation content")
                    if is_table_dominant_page(page, tables) or has_solicitation_content(tables):
                        pages_with_tables.append(page_num + 1)  # Convert to 1-based indexing
                        logging.info(f"Page {page_num + 1} identified as table-dominant or containing solicitation content")

        # Process all pages for image detection using the already opened PyMuPDF document
        for page_num in range(doc.page_count):
            page = doc[page_num]
            image_list = page.get_images()

            # Check if page has any images
            if image_list:
                # Create temporary directory for image extraction
                with tempfile.TemporaryDirectory() as temp_img_dir:
                    valid_images = False
                    for img_index, img in enumerate(image_list, start=1):
                        try:
                            xref = img[0]
                            base_image = doc.extract_image(xref)
                            image_bytes = base_image["image"]
                            image_path = os.path.join(temp_img_dir, f"page_{page_num+1}_img_{img_index}.png")

                            # Save image temporarily
                            with open(image_path, "wb") as img_file:
                                img_file.write(image_bytes)

                            # Basic image validation
                            if os.path.getsize(image_path) > settings.MIN_IMAGE_SIZE:
                                # Load image and check if it's not blank
                                with Image.open(image_path) as img:
                                    if not is_blank_image(img):
                                        valid_images = True
                                        break
                        except Exception as e:
                            logging.warning(f"Error processing image on page {page_num+1}: {str(e)}")
                            continue

                    if valid_images:
                        pages_with_images.append(page_num + 1)  # Convert to 1-based indexing

        doc.close()

        # Clean up temporary file
        os.unlink(temp_path)

        # Log the findings
        logging.info(f"Found tables on pages: {sorted(pages_with_tables)}")
        logging.info(f"Found images on pages: {sorted(pages_with_images)}")

        # Identify pages with both tables and images
        pages_with_both = set(pages_with_tables).intersection(set(pages_with_images))
        if pages_with_both:
            logging.info(f"Pages containing both tables and images: {sorted(pages_with_both)}")

        return sorted(pages_with_tables), sorted(pages_with_images)

    except Exception as e:
        logging.error(f"Error identifying special pages: {str(e)}")
        return [], []

def read_docx(file_content):
    """
    Extract text from a DOCX file using markitdown.
    MarkItDown extracts all textual content, including the Table of Contents,
    main body, headers, footers, footnotes, and tables, and converts it to Markdown format.

    Args:
        file_content: Binary content of the DOCX file

    Returns:
        list: List containing a single dictionary with page content
    """
    logging.info("Processing DOCX file with markitdown")

    # Create a temporary file to store the DOCX content
    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
        temp_file.write(file_content)
        temp_file_path = temp_file.name

    try:
        logging.info(f"Attempting DOCX text extraction with markitdown for: {temp_file_path}")

        # Initialize markitdown converter
        md_converter = MarkItDown(enable_plugins=False)

        # Perform conversion
        result = md_converter.convert(temp_file_path)

        # Extract the markdown content
        extracted_text = result.text_content
        logging.info(f"Successfully extracted text from '{temp_file_path}' using markitdown.")

        page_contents = [{
            'page_number': 1,  # DOCX files are treated as single page
            'content': extracted_text
        }]
        logging.info(f"Successfully extracted {len(page_contents)} sections for DOCX file")
        return page_contents

    except Exception as e:
        logging.error(f"An unexpected error occurred with markitdown extraction for '{temp_file_path}': {e}", exc_info=True)
        return [{
            'page_number': 1,
            'content': ""
        }]
    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_file_path)
        except OSError:
            logging.warning(f"Could not delete temporary file: {temp_file_path}")

def read_excel(file_content):
    """Process Excel file content and convert it to text format.

    Args:
        file_content: Binary content of the Excel file

    Returns:
        list: List containing a single dictionary with page content

    Features:
        - Reads all sheets in the Excel file
        - Converts dataframes to string representation
        - Handles both .xlsx and .xls formats
    """
    logging.info("Processing Excel file")
    try:
        # Read Excel file into pandas DataFrame
        excel_file = pd.ExcelFile(io.BytesIO(file_content))

        # Process all sheets
        all_sheets_content = []
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(io.BytesIO(file_content), sheet_name=sheet_name)
            # Convert DataFrame to string representation
            sheet_content = f"\nSheet: {sheet_name}\n"
            sheet_content += df.to_string(index=True, header=True)
            all_sheets_content.append(sheet_content)

        # Combine all sheets into one content string
        combined_content = "\n\n".join(all_sheets_content)

        page_contents = [{
            'page_number': 1,  # Excel files are treated as single page
            'content': combined_content
        }]

        logging.info(f"Successfully extracted content from Excel file with {len(excel_file.sheet_names)} sheets")
        return page_contents

    except Exception as e:
        logging.error(f"Error processing Excel file: {str(e)}")
        return [{
            'page_number': 1,
            'content': f"Error processing Excel file: {str(e)}"
        }]

def is_blank_image(image):
    """Check if an image is blank by analyzing pixel intensity variation."""
    stat = ImageStat.Stat(image)
    return sum(stat.stddev) < 10
