def get_rfp_info_query():
    query="""what is the solicitation No.
    what is the NAICS ( North American Industry Classification System) for this project?
    What is the name or title of the project/procurement?
    what is the RFQ NO.?
    What is the type of the procurement?
    what is the questions due date and time?
    what is the proposal due date and time?
    what is the Mode of submission mentioned in the Procurement?
    What is the place of performance?
    what is the period of performance?
    How many option years are mentioned in the procurement?
    How many base years are mentioned in the procurement?
    What is the point of contact mentioned in the document?
    who is the CO or COR for this procurement?
    what are the key personnels required for this project?
    what are the security clearances mentioned in the procurement?
    what is the order type mentioned in the procurement?
    what is the set aside for this procurement?
"""

    return query.strip()

# --------------------------------------------ADDED SECTION QUERY PROMPTS_________________-


def get_background_query():
    query= """What is the history or context of the project?
    What events or circumstances led to the need for this procurement?
    Which business unit/department initiated this requirement?
    What problem or opportunity is being addressed?
    Is this a new requirement or a renewal/replacement?
    who is the incumbent supplier?
    What is the current process/system in place, existing pain points or limitations?
    Which stakeholders are impacted?
    what are the current state of affairs, including any relevant existing infrastructure or systems. [source number]
"""
    return query.strip()

def get_scope_query():
    query =  """What is the overall objective of the project?
    What is the  scope of the project?
    what is the purpose of the project?
    What are the specific services or products being procured? What are the key features and functionalities required?
    What are the major tasks and activities involved in completing the project? What is the expected workflow or process?
"""
    return query.strip()




def get_task_area_query():
    query = """What are the task areas mentioned in the document?
    What are the different tasks?
    What are the key areas of work described in the document?
    what are the various tasks and subtasks the contractor has to perform?
    What are the contractors or vendors responsibilities?
    What are the areas of focus highlighted in the document?
    Contractors/vendors responsibilities to be fulfilled.
    Contractor obligations for submission.
    what are the deliverables mentioned in the document?
    What are the key personnel roles and their associated responsibilities within each task area?
    List the deliverables and reporting requirements associated with each task area.
    What are the performance standards and evaluation criteria for each task area?
    Are there any phase wise task areas?
"""
    return query.strip()


def get_submission_instructions_query():
    query = """
    What is the final submission date and time? Are there any intermediate deadlines? Is there a schedule for questions/clarifications? When is the bid opening?
    How many copies are required (hard/soft)? What file formats are acceptable? Are separate technical/commercial submissions needed? Is there a specific file naming convention?
    Is it physical submission or electronic? What's the submission platform/portal if electronic? What's the physical submission address if applicable? Are there specific packaging/sealing requirements?
    What is the required proposal organization?
    Are there page/size limits? What indexing/numbering system is required? Are there specific forms/templates to be used?
    What certificates/documents must be included? Are notarizations required? What proof of eligibility is needed? Are there specific financial documents required?
    Are samples/demos required? Any language requirements?Any specific signing authority requirements?Are there any mandatory forms/formats?
    what are the instructions for submitting proposals
    What is the Proposal format like  the required structure, sections, and formatting guidelines.
    What is the Submission deadline, State the date and time, including time zone if specified, for each proposal volume.
    Are there any specific requirements for electronic submissions, such as file formats or security protocols? Are there any prohibited file types?
    Are there any requirements for marking or labelling proposal submissions?How should proposals be organized and indexed? [source number] Should a table of contents be included?
    What are the procedures for submitting questions or requests for clarification? To whom should such inquiries be directed? What is the deadline for submitting questions?
    what are the different volumes of past performance mentioned in the document?
"""
    return query.strip()

def get_evaluation_factors_for_award_query():
    query = """
    What is the basis of award for each procurement? Is it best value, lowest price technically acceptable (LPTA), or another approach?
    What are the evaluation factors and sub-factors that will be used to assess proposals? What is the relative importance or weight of each factor? Are any factors considered more important than others?
    What are the specific criteria or standards that will be used to evaluate each factor? What are the different rating levels or scoring systems used?
    What is the role of price in the evaluation? Is it a separate factor, or is it considered in conjunction with other factors? How is price reasonableness determined?
    Are there any specific requirements or preferences related to small businesses, disadvantaged businesses, or other socio-economic categories? How are these considerations factored into the evaluation process?
    Will the government conduct discussions or negotiations with offerors? If so, at what stage of the evaluation process? What are the procedures for conducting discussions and requesting clarifications?
    Will a competitive range be established? If so, what criteria will be used to determine which offerors are included in the competitive range?

"""

    return query.strip()



def get_list_of_attachments_query():
    query = """List all attachments mentioned in the document and provide a concise description for each.
    what are the various documents attached with this procurement.
    what are the attachements mentioned in the table of contents.
    what is the name, number, description of attachments.
  """

    return query.strip()




def get_key_personnel_query():
    query = """which individuals are deemed crucial for project success?
            what are the key personnels mentioned in the document?
            what are the required qualifications for the key personnels?
            which task areas do need key personnels?
            what security clearances are needed for key personnels?
            what are the responsibilities of key personnels?
"""

    return query.strip()


def get_red_flags_query():
    query = """
    How many past performance references are required?
    What is the required recency of past performances?
    Are there any specific performance criteria or metrics that must be met?
    What are the key personnel positions identified? What are the minimum qualifications (education, certifications, experience) for each key role? Is there a requirement for submittal of key personnel resumes? Are there any security clearance or other special requirements for key personnel?
    Is there a requirement to submit resumes for non-key personnel as well? What level of detail is required in the resumes (e.g., length, format, content)? Are there any specific formatting or structure requirements for the resumes?
    Is a Facility Clearance required for the contractor organization? Are there any Personnel Clearance requirements for specific roles or positions? What is the current clearance level of the organization/personnel, and does it match the requirement?
    Where is the primary place of performance for the contract? Are there any restrictions or preferences on the location of the contractor's facilities? Are there any site-specific requirements or logistical challenges at the place of performance?
    Are there any technical, professional, or business licenses required to perform the contract? Do the contractor and its personnel currently hold the necessary licenses? Are there any challenges or costs associated with obtaining the required licenses?
    What is the due date and time for proposal submission? Is the timeline reasonable considering the scope of work and required proposal content? Are there any interim deadlines (e.g., for questions, site visits) that could impact the proposal preparation?
    Where is the primary place of performance for the contract? Are there any logistical or infrastructure challenges at the place of performance? Is the place of performance remote, difficult to access, or lacking in resources?
    What is the overall budget or funding available for the contract? Is the budget adequate to support the required scope of work and deliverables? Are there any concerns about the stability or continuity of funding for the contract?
    What specific tools, technologies, or software are required to perform the contract? Are these tools readily available and within the contractor's current capabilities? Would the contractor need to make significant investments to acquire or implement the required tools?
    Is the contract set aside for a specific business category (e.g., small business, HUBZone, woman-owned)? Does the contractor meet the eligibility criteria for the set-aside category? Are there any challenges or risks associated with the set-aside requirements?
    What is the contracting agency's history, reputation, or known challenges? Are there any known or potential issues with the agency's management, funding, or oversight of the contract? Are there any industry-specific or agency-specific regulations or requirements that could impact contract performance?


"""

    return query.strip()

def get_extract_metadata_from_edited_proposal_info_prompt():
    """Get prompt for extracting only the required rfp_project_metadata fields from edited Proposal Info markdown content."""
    prompt = """
    You are an expert procurement analyst specializing in extracting specific metadata fields from edited Proposal Info markdown content. Your task is to analyze the provided markdown content and extract ONLY the required fields for project metadata.

    ### Objective:
    Extract ONLY the following 6 specific fields from the provided markdown content:

    1. **Solicitation Number** - The unique identifier for this procurement
    2. **Title** - The name/title of the procurement/project
    3. **Solicitation Type** - Type of solicitation (RFP, RFQ, IFB, etc.)
    4. **Questions Due Date** - Deadline for submitting questions/inquiries
    5. **Proposal Due Date** - Deadline for proposal submission
    6. **POC** - Point of Contact information (Primary and Secondary contacts)

    ### Instructions:
    1. **Focus Only on Required Fields**: Extract ONLY the 6 fields listed above, ignore all other information
    2. **Handle Missing Information**: If a field is not found, use an empty string "" for that field
    3. **POC Structure**: For POC, extract both Primary and Secondary contact information if available
    4. **No Assumptions**: Only extract information that is explicitly stated in the content

    ### Output Format:
    Return ONLY a valid JSON object following this EXACT schema:

    ```json
    {
        "Solicitation Number": "string",
        "Title": "string",
        "Solicitation Type": "string",
        "Questions Due Date": "string",
        "Proposal Due Date": "string",
        "POC": {
            "Primary": {
                "Name": "string",
                "Title": "string",
                "email": "string",
                "phone": "string"
            },
            "secondary": {
                "name": "string",
                "title": "string",
                "email": "string",
                "phone": "string"
            }
        }
    }
    ```

    ### Important Notes:
    - Return ONLY the JSON object, no additional text or explanations
    - Extract ONLY the 6 required fields, ignore everything else
    - Ensure the JSON is valid and parseable
    - Use empty strings for missing fields
    - If POC information is missing, include the POC structure with empty strings
    - Do not include any markdown formatting or code blocks in your response

    ### Markdown Content to Analyze:
    """
    return prompt.strip()

# ------------------------------------------------------------------






def get_rfp_info_prompt(context):
    prompt = f"""
    You are an expert procurement/solicitation analyst specializing in extracting critical proposal information from procurement documents such as RFPs, RFQs, RFIs, IFBs, etc. Your objective is to create an accurate, comprehensive, and self-contained JSON structure from the provided document context for clear understanding and usage.

    ### Objective:
    Extract proposal information i.e., Solicitation number, NAICS code, Name/title of the procurement/contract, Solicitation type, Department/Agency Name which is issuing the procurement/contract, Inquiries/Questions Due Date and Time, Proposal Due Date and Time, Mode of Submission, Place of Performance which is where the contract is to be executed, Point of Contact (POC), Set Aside, Period of Performance, Key Personnel, Security Clearance Requirements, Task Order Type from the given procurement document context. The output should be precise, free from assumptions, and strictly align with the information presented in the document. This information will be used to guide the offeror in their proposal submission process, so it should be as clear, correct and concise as possible.

    ### Approach (Chain of Thought):

    #### Step 1: Initial Document Validation
    - **Determine Document Type:**
        - Identify if the document is an RFP, RFQ, RFI, or IFB.
        - Base this determination on explicit labels, headings, or contextual clues in the document.
    - **Section Identification:**
        - Locate all sections containing proposal-related information.
        - Create a list of these sections with their corresponding titles.
    - **Index Creation:**
        - Develop an index mapping key pieces of information to their respective sections (e.g., "Section F.1").
    - **Version Verification:**
        - Confirm the document version and note any amendments, updates, or changes.

    #### Step 2: Core Information Extraction
    For each mandatory field, use the following process:

    - **Solicitation Number:**
        - It refers to a unique identifying number assigned to a specific request for proposals (RFP) or other procurement document by the organization soliciting bids, essentially acting as a reference code for that particular project when submitting a proposal in response.
        - Identify and extract the exact solicitation number (e.g., "RFP No. 12345").
        - Cross-verify it with other sections to ensure accuracy.

    - **NAICS Code:**
        - It is a numerical identifier used in proposals, particularly when bidding on government contracts, to categorize a company's primary business activity and industry, allowing agencies to accurately match potential vendors with appropriate procurement opportunities based on their industry classification.
        - Extract the explicitly mentioned North American Industry Classification System (NAICS) code. Look for the exact NAICS code mentioned in the procurement/contract document.
        - Validate it against all the NAICS codes provided in the document.

    - **Name/Title:**
        - It refers to the concise, descriptive heading at the top of the document that clearly states the main focus and purpose of the proposal, essentially summarizing the project or idea being presented in a few words; it's often located on the title page and should be specific enough to capture the essence of the proposal while remaining easy to understand.
        - Capture the official name or title of the procurement.
        - Avoid including solicitation types (e.g., "RFP") or irrelevant phrases.
        - Verify it against other sections where the name/title is mentioned to ensure accuracy.

    - **Solicitation Type:**
        - It refers to the specific method used to request proposals from potential vendors or bidders, essentially outlining the format and criteria they need to follow when submitting their proposals. Examples of solicitation types include RFP (Request for Proposals), RFQ (Request for Quotes), RFI (Request for Information), and IFB (Information Form), etc.
        - Identify the solicitation type (e.g., RFP, RFQ, IFB).
        - If not explicitly mentioned, infer the type based on context (e.g., submission process, evaluation criteria).
        - Verify it against other sections where the type is mentioned explicitly to ensure accuracy.

    - **Department/Agency Name:**
        - It refers to the specific government department or agency that the proposal is being submitted to, indicating which organization is being requested to fund or approve the proposed project.
        - Extract the complete name of the issuing department or agency.
        - Avoid confusing it with sub-tier entities or address information.
        - Verify it against other sections where the name of the issuing department/agency is mentioned explicitly to ensure accuracy.

    - **Inquiries/Questions Due Date and Time:**
        - It refers to the specific date and time by which potential bidders or respondents are required to submit any questions they may have regarding the proposal details to the soliciting organization; essentially, it's the deadline for asking clarifying questions about the project before submitting a full proposal.
        - Identify the exact date and time for submitting inquiries or questions(any sort of questions like technical, commercial, etc), including the timezone.
        - Verify it against any amendments or updates. Check all the sections where inquiries or questions related information is mentioned to ensure accuracy.

    - **Proposal Due Date and Time:**
        - It refers to the specific date and time by which a proposal must be submitted to the requesting party, as outlined in the proposal guidelines or Request for Proposal (RFP), marking the absolute deadline for submitting a proposal.
        - Accurately extract the proposal submission deadline with the timezone.
        - Verify against any amendments or updates.  Check all the sections where submission deadlines are mentioned to ensure accuracy.

    - **Mode of Submission:**
        - It refers to the method or way in which a proposal is to be submitted, whether it be electronically through an online portal, by mail, hand-delivered, or any other specified format outlined by the requesting party; essentially, it indicates how the proposal document should be physically sent to the recipient.
        - Specify the submission methods (e.g., electronic, physical) and include precise instructions.
        - Verify it against any amendments or updates. Check all the sections where the submission methods are mentioned to ensure accuracy.

    - **Place of Performance:**
        - It refers to the specific location where the services or deliverables outlined in the proposal will be executed or provided, essentially indicating where the work will be done according to the agreement if the proposal is accepted; it specifies the physical address or geographical area where the project will take place
        _ First check if the Place of Performance is online or offline.
        - Extract the full address for work performance incase place of performance is offline. Incase of having multiple places of performance, extract all the places of performance.
        - Note any support hours or remote work details if mentioned.

    - **Point of Contact (POC):**
        - It refers to the designated individual within a company who is the primary contact person for communication regarding a potential project or contract, essentially acting as the main point of reference for any questions, updates, or concerns related to the proposal.
        - Extract details of primary, secondary, and technical POCs (name, position, email, phone).

    - **Set Aside:**
        - It refers to a portion of a project or contract specifically designated for a particular group, like small businesses, minority-owned businesses, or veteran-owned businesses, etc where only companies within that category are eligible to bid, aiming to promote inclusivity and provide opportunities for diverse vendors.

        - Specify the set-aside category (e.g., Small Business, HUBZone, etc).
        - Verify it against any amendments or updates. Check all the sections where set-aside information is mentioned to ensure accuracy. Do not assume the set-aside without having a clear understanding of the category.

    - **Period of Performance:**
        - It refers to the specific timeframe within which the proposed project or services will be completed, typically defined by a start date and an end date, outlining the duration of the contract if awarded.
        - Include base period and option periods with durations and dates.
        - Include the extensions if mentioned.

    - **Key Personnel:**
        - It refers to the individuals considered essential to the successful completion of a project, usually including those with critical expertise, leadership roles, or direct responsibility for key aspects of the work, and whose involvement is explicitly outlined within the proposal to demonstrate the team's capabilities to the potential client.
        - Identify positions explicitly labeled as "key personnel."
        - Verify it against any amendments or updates. Check all the sections where key personnel information is mentioned to ensure accuracy. Do not assume LCATS as key personnel. Key Personnel are the roles which are explicitly menntioned as key personnel in the documents.


    - **Security Clearance Requirements:**
        -  It refers to a specific level of government background check that potential contractors or employees must pass before gaining access to classified information related to the project, outlining the level of security clearance needed to work on the project, such as "Top Secret," "Secret," or "Confidential," and may include details about the investigation process involved depending on the sensitivity of the work.
        - Extract the required level and type of security clearance.

    - **Contract Type:**
        - It refers to a specific proposal format where a contractor bids on a smaller, defined piece of work ("task") within a larger, pre-established contract known as a "blanket purchase agreement" (BPA) or "Indefinite Delivery Indefinite Quantity" (IDIQ) contract, allowing the client to order services or goods as needed, with each individual order being considered a "task order."
        - Specify the contract or task order type if mentioned.

    #### Step 3: Reflection and Quality Assurance
    - **Cross-Verification:**
        - Re-examine each extracted field against the document to ensure accuracy.

    - **Section Reference Accuracy:**
        - Confirm that each bullet point links to the correct section.

    - **Consistency Checks:**
        - Ensure consistency in dates, numbers, and codes.

    - **Completeness:**
        - Populate all mandatory fields. Use empty string "" for any missing information.

    - **Flagging Ambiguities:**
        - Highlight unclear or contradictory information explicitly.

    - **Elimination of Redundancies:**
        - Remove unnecessary repetition.

    - **Format and Clarity:**
        - Ensure the JSON structure is well-formed and follows the schema exactly.

    ### Output Formatting:
    Provide the extracted information as a JSON structure following EXACTLY this schema:

    ```json
    {{
        "Solicitation Number": "string",
        "NAICS": "string",
        "Title": "string",
        "Solicitation Type": "string",
        "Department Name": "string",
        "Questions Due Date": "string",
        "Proposal Due Date": "string",
        "Submission Method": "string",
        "Place of Performance": "string",
        "Set Aside": "string",
        "POC": {{
            "Primary": {{
                "Name": "string",
                "Title": "string",
                "email": "string",
                "phone": "string"
            }},
            "secondary": {{
                "name": "string",
                "title": "string",
                "email": "string",
                "phone": "string"
            }}
        }},
        "Period of Performance": {{
            "Base Period": {{
                "Duration": "string",
                "Start Date": "string",
                "End Date": "string"
            }},
            "option_periods": [
                {{
                    "name": "string",
                    "duration": "string",
                    "start_date": "string",
                    "end_date": "string"
                }}
            ],
            "extensions": [
                {{
                    "name": "string",
                    "duration": "string",
                    "start_date": "string",
                    "end_date": "string"
                }}
            ]
        }},
        "Key Personnel": ["string"],
        "Security Clearance": "string",
        "Contract Type": "string"
    }}
    ```

    Ensure all fields are populated with the extracted information. If a field is not found in the document, use an empty string. For arrays, use an empty array if no items are found. For nested objects, include them with empty string values if the parent object exists but details are missing.

    Do not include any explanatory text, commentary, or markdown formatting outside the JSON structure. The output should be a valid, parseable JSON object that strictly follows the schema above.

    ### Context:
    {context}

    """
    return prompt.strip()



def get_background_prompt(context):
    prompt = f"""
    You are an expert procurement/solicitation analyst specializing in summarizing specific sections from procurement documents such as RFPs, RFQs, RFIs, IFBs, etc. Your primary goal is to create a detailed, self-contained summary of the **Background**, **Purpose**, or **Introduction** section for understanding the document without including details intended for other sections.

    ### Objective:
    Extract and accurately summarize all relevant details pertaining to the procurements/contracts **Background** or **Purpose**, or **Introduction**. Ensure the summary is clear, well-organized, and self-sufficient, enabling comprehension without the original document. This summary will be used to guide the offeror in their proposal submission process, so it should be as clear, correct and concise as possible. Ensure there is no other text/commentaryat the begining or end of the summary.

    ### Approach:
    Use a step-by-step method to achieve a precise and high-quality summary:

    1. **Locate Relevant Content:**
       - Identify all text within the provided context which is explicitly stated as  **Background**, **Purpose**, or **Introduction** of the procurement/contract. Focus on historical context, project objectives, mission, stakeholders, and intended audience.
       - Refer to the PWS (Performance Work Statement) and SOW (Statement of Work) sections for any additional relevant background details. Note that only consider the information related to Introduction or Background of the contract and no other information
       - Exclude information from unrelated sections such as Scope, Task Areas, Submission Instructions, Evaluation Factors, Key Personnel, and Attachments.

    2. **Analyze and Categorize Information:**
       - Separate the identified content into key themes such as background, purpose of the project, and audience/stakeholder details along with the referenced section number.
       - For each theme, ensure that no critical information is omitted and avoid overlap with other sections.
       - Only consider one of the themes (Background, Purpose, or Introduction) for the summary based on the presence of the section titled **Background**, **Purpose**, or **Introduction** in the provided context. e.g. if **Background** is present, then only **Background** should be included in the summary. Do not assume anything on your own. Ensure no info is interpreted as **Background**, **Purpose**, or **Introduction** if it not in any section or subsection titled **Background**, **Purpose**, or **Introduction**.

    3. **Draft a Focused Summary:**
       - Write a concise summary capturing only the relevant background or purpose, or introduction details about the procurement/contract from the specified section (Background, Purpose, or Introduction). Use paragraph form. Ensure the section number references are included in the summary.
       - Assign an appropriate title to the summary based on its content, such as **Background**, **Purpose**, or **Introduction**.
       - Add section references (e.g., "[Section A.1]") to trace specific details back to the document when necessary.

    4. **Ensure Completeness and Self-Sufficiency:**
       - Review the drafted summary to ensure the summary is fully self-contained and provides a clear understanding of the document's Background, Purpose, or Introduction.
       - Verify that the summary excludes details intended for unrelated sections, such as Scope or Evaluation Factors, submission instructions, attachments, key personnel, or other sections.

    5. **Formatting and Quality Control:**
       - Use Markdown formatting for the final output, with  only the title in H2 format (e.g., ## Background). Make sure for any other headings or subheadings if any H3 heading is used.
       - Structure the summary into clear, organized paragraphs along with correct section references ensuring the overall summary is within a 5500-word limit.
       - Cross-check the summary for accuracy, token efficiency, and adherence to the section's purpose which is to give the offeror a clear understanding of the contract's Introduction or background or purpose.


    ### Output Formatting:
    - Use Markdown formatting for the final output, with the title in H2 format (e.g., ## Title of the section).
    - Ensure H2 heading is used for the main title of the summary only.
    - Structure the summary into clear, organized paragraphs along with correct section references ensuring the overall summary is within a 5500-word limit.
    - Ensure there is no additional text/commentary at the beginning or end of the summary.

    ### Context:
    {context}


    """
    return prompt.strip()




def get_scope_prompt(context):
    prompt = f"""
    You are a procurement/solicitation expert specializing in generating precise and comprehensive scope summaries from procurement documents. Your objective is to produce an accurate, detailed summary of the **Scope**, including a very brief overview of the task areas. The summary should capture only what is explicitly stated as in scope, avoiding any details not clearly defined as scope-related. Avoid irrelevant content such as staffing details or administrative instructions. The final output should be under 5500 words.

    ### Objective:
    Extract and summarize only the explicitly stated scope of the procurement/contract. Provide a concise **Task Areas Overview** subsection listing the task areas in a minimal fashion. Do not include unconfirmed details, assumptions, or content from outside the scope section. This summary will be used to guide the offeror in their proposal submission process, and to understand the scope of the procurement/contract so it should be as clear, correct and concise as possible.


    ### Step-by-Step Approach:
    Follow this structured process to ensure completeness and clarity in the scope summary:

    1. **Locate and Analyze In-Scope Content:**
       - Identify sections explicitly defining the scope, such as \"Scope of Work,\" \"Scope of Work,\" \"Task Areas,\" or similar labels. Scope describes the Department's/Contractor's needs and desired outcomes for the procurement.
       - Focus only on what is explicitly stated as in scope; exclude anything not directly labeled or mentioned within the scope.
       - Only consider the content under sections or subsections that are explicitly labelled as scope or scope of work, etc.

    2. **Draft the summary of the Scope:**
       - Summarize all scope details, including specific tools, technologies, or required expertise, **only if directly stated within the scope section.**
       - Ensure that the wording of the scope from the procurement/contract is not changed or modified in the summary you produce.
       - Make sure the section references are included in the summary and should be correct.
       - Keep the summary concise and well-organized, focusing on clarity and completeness.

    3. **Draft the Task Areas Overview:**
       - Create a separate subsection titled \"### Task Areas Overview.\"
       - List the most important or main task areas briefly, mentioning only their names or minimal descriptions as directly stated in the scope.
       - Ensure that only the tasks labelled as \"Task Areas\" or the main tasks the offeror is required to perform are included in the list. Do not include deliverables in the task areas list, as they are completely different from task areas.

    4. **Verification and Refinement:**
       - Cross-check every detail to confirm its relevance to the explicitly defined scope or scope of work.
       - Exclude non-scope content, such as staffing details, administrative requirements, or evaluation factors, submission instructions, background or introduction or purpose of the procurement/contract, etc.
       - Ensure the summary is self-contained and requires no additional documents to understand.

    5. **Format and Finalize the Output:**
       - Use \"## Scope\" as the main heading (exactly, with no additional words) and \"### Task Areas Overview\" for the brief task area list.
       - Structure the content in paragraphs or bullet points for clarity and organization.
       - Ensure H2 heading is used only for the main title of the summary only.
       - For any other headings use H3 headings.
       - Ensure the correct section references are included in the summary.
       - Keep the total output under the 5500-word limit.

    6. **Final Review:**
       - Verify that the summary includes only explicitly stated scope or scope of work under this procurement/contract.
       - Ensure Section References are included in the summary.
       - Ensure the wording of the scope from the procurement/contract is not changed or modified in the summary.
       - Ensure accuracy, completeness, and clarity.
       - Confirm that the output is free from irrelevant or non-scope details.

    ### Final Output:
    - Create a Markdown-formatted summary under the heading \"## Scope\".
    - Include a subsection titled \"### Task Areas Overview\" that lists task areas very briefly.
    - Ensure H2 heading is used for the main title of the summary only.
    - Make sure no additional text/commentary is added at the beginning or end of the summary.
    - Ensure accuracy and completeness of scope-related details without extraneous information.



    ### Context:
    {context}


    """
    return prompt.strip()




def get_task_area_prompt(context):
    prompt = f"""You are a procurement analysis expert specializing in summarizing task-area content from procurement documents (RFPs, RFQs, RFIs, IFBs). Your objective is to create accurate, structured summaries that faithfully reflect the original document's organizational flow, focusing solely on the tasks explicitly assigned to the contractor. Follow these steps for precise analysis and summary creation:


##Objective:
Extract and summarize the task areas and their associated sub-tasks from the procurement document. Make sure that the section references are included in the summary. Ensure that the summary only includes the content about the tasks the offeror is expected to perform. This summary will be used to guide the offeror in their proposal submission process, and in understanding what tasks the offeror is expected to perform, so it should be as clear, correct and concise as possible.

### Core Responsibilities:
1. **Identify and Summarize Explicit Main Task Areas (The tasks offeror is expected to perform):** Extract and summarize all task areas that the offeror is expected to perform and their associated sub-tasks (sub-tasks only if explicitly provided). Make sure the deliverables under this procurement/contract are not included in the summary as they are not task areas. Summarize comprehensively while maintaining a word limit of 5500 words.
2. **Preserve Organizational Structure:** Maintain the hierarchy and structure of tasks as they appear in the original document. Do not infer or create sub-tasks where none are explicitly defined. Ensure the section references are included in the summary.
3. **Incorporate Specific Requirements:** Include phase-specific tasks, task order dependencies, and relevant staffing/training requirements only if clearly tied to a task area.

### Step-by-Step Approach:

#### 1. Document Review:
- **Understand Document Type:** Determine the type of procurement/contract document you are analyzing.
- **Locate Relevant Sections:** Focus on sections like "Task Areas", "PWS", "SOW", "Descriptions of Required Tasks", "Required Tasks" or specific sections such as "Section C."
- **Analyze Hierarchy:** Identify the organizational structure of tasks/task areas/required tasks,etc, ensuring task relationships and dependencies are clear.
- **Analyze What to include the relevant sections:** Since the objective is to summarize task areas so that offeror can understand what are the task areas what are the requirements of each task area, so only include important and critical content about each Task Area.
-**Exclude Deliverables/General Tasks/Minor Tasks:** Ensure the deliverables under this procurement/contract are not included in the summary as they are not task areas. Also if there are any minor/general task areas do exclude them from the summary.

#### 2. Content Extraction:
- **Explicitly Stated Tasks Only:** Extract only tasks explicitly assigned to the contractor (the main tasks the offeror is expected to perform). Make sure not to include general/minor tasks or deliverables. Avoid inferring details or adding assumptions.
- **Preserve Sequence:** Follow the original sequence and hierarchy of tasks as laid out in the document. Include the section references in the summary.
- **Phase-Specific Details:** Include requirements tied to specific phases or task orders only if directly mentioned.

#### 3. Summary Development:
- **Heading and Structure:** Begin the summary with the heading "## Task Areas." and  ### sub-section for seach subtask. (Do not use subheading levels other than ### and ####). Make sure to format bullet points on seperate lines, if any. Ensure H2 heading is used only for the main title of the summary only.
- **Task-Area Details:** Summarize all explicitly stated task areas and sub-tasks (if provided) in the original organizational flow which describe the major tasks the offeror is expected to perform. ENSURE Deliverables, minor/general tasks, general requirements are excluded from the summary. Also MAKE SURE to only include the content that will help the offeror understand what tasks the offeror is expected to perform and what are the requirements of each task area(Do not make the content of each task area too lomg or overwhelming). Ensure that the section references are included in the summary.
- **Clarity and Conciseness:** Ensure all summaries are clear, concise, with proper section number references and reflect the document's intent without exceeding 5500 words.
- **Preserve Dependencies:** Clearly articulate task dependencies and phase-specific requirements.

#### 4. Quality Control and Refinement:
- **Validation Checks:**
  - Ensure the summary starts with "## Task Areas, then H3 for each individual Task Area heading and subtasks if any should have H3 headings." Make sure no markdown heading is smaller than H3.
  - Make sure the subtasks if any should be summarized briefly and there should be no further sub sections for Sub tasks.
  - Bullet points should be structured properly, like having each bullet point on a new line(if any).
  - Verify section numbering is included in the summary and is consistent with the original document.
  - Verify that deliverables or minor tasks are not included in the task area summary.
  - Verify word count compliance (under 5500 words).
  - Confirm all explicit task areas and sub-tasks (if any) are included.
  - Check for clarity and preservation of task relationships.
- **Address Gaps:** If any tasks, sub-tasks, or relationships are missing, refine and regenerate those sections while maintaining compliance with word count and structure.

#### 5. Final Deliverable:
- **Organized and Complete Summary:** Deliver a structured summary beginning with "## Task Areas." Ensure H2 heading is used for the main title of the summary only.
- **Content Fidelity:** Reflect the original document's structure, ensuring completeness and clarity.
- **Self-Contained Output:** The summary must be self-contained, requiring no external references.

## Final Output:

## Task Areas

### Task Area 1:
Brief Description of the important details about the Task Area that the offeror should know. [Section Reference]
### Subtasks:
A very short overview of the all the sub task if any.

## Context:
{context}

"""
    return prompt.strip()



def get_submission_instructions_prompt(context):
    prompt = f"""You are a procurement/solicitation analyst tasked with generating a thorough, self-contained summary of the 'Submission Instructions' section from procurement documents (e.g., RFPs, RFQs, RFIs, IFBs). Follow a detailed, step-by-step approach to ensure that the summary focuses strictly on submission requirements, is structured for clarity and compliance, and remains below a 5500-word limit. This summary will form a part of an understanding document used to analyze the procurement/contract.

### Primary Objective
Produce a detailed, accurate summary below the 5500-word limit, focusing exclusively on submission instructions. Ensure that the section references are included in the summary. The summary must exclude unrelated sections (e.g., evaluation factors, post-award details) and highlight all critical submission requirements clearly. This summary will be used to guide the offeror in their proposal submission process, so ensure that no such information is omiited which can make the offeror's proposal incomplete or incompetent, so it should be as clear, correct and concise as possible. Also make sure only to include the submission instructions, not how the offerors submission will be evaluated as the evaluation factors/criteria summary will be handled separately.


### Chain of Thought Approach

1. **Document Examination**:
   - Identify and locate sections explicitly labeled as submission instructions or equivalent.
   - Cross-check headings, subsections, or references (e.g., "[Section L.4.2]") that indicate submission-specific content.

2. **Content Selection**:
   - Extract only information related to submission requirements (the instructions which the offeror should be aware of before submitting their proposal, so that the proposal is complete and competent), such as but not limited to:
     - Forms to be submitted (e.g., SF-33, block 16).
     - Required volumes, page limits, format, tabbing, and labeling instructions.
     - Details of any excluded materials (e.g., resumes, exhibits).
   - Include all critical instructions related to volume-specific requirements, specialized experience, or subcontractor management.

3. **Structured Summarization**:
   - Create a Markdown-based structured summary under the heading '## Submission Instructions'.
   - Ensure H2 heading is used for only the main title of the summary.
   - Use subsections having H3 heading to mirror the document's organization and reference markers (e.g., "[Section L.4.2]") for traceability.
   - Ensure no heading is below H3 heading.
   - Maintain clarity by summarizing tables into lists or bullet points, ensuring no critical detail is omitted.
   - Provide table references or names when summarizing table data.

4. **Content Refinement**:
   - Prioritize critical details while ensuring compliance with the word limit.
   - Eliminate redundancies and ensure only submission-specific instructions are included.
   - Ensure only that submission instructions are included without whose knowledge the offeror's proposal will be incomplete or incompetent.
   - Exclude any unrelated sections such as evaluation factors, pricing, or post-award briefings. Make sure not to include such info, which in any way is not going to make the offeror's proposal incomplete or incompetent (which is of no use to the offeror's understanding of the submission related details).

5. **Verification and Adjustments**:
   - **Cross-Check Accuracy**: Ensure all extracted details match the source document and all critical instructions are included. Also ensure that the section references are included in the summary and they are correct.
   - **Word Count Compliance**: Verify that the summary is below 5500 words.
   - **Completeness Check**: Confirm that no essential submission-related detail has been omitted. The summary should be sufficient for the offeror to understand the submission Instructions. Ensure no such submission Instruction is ommitted without whose presence the offeror's proposal will be incomplete or incompetent.

6. **Rework If Necessary**:
   - If any verification step fails, revisit the summary process:
     - Identify gaps or issues in the extracted content.
     - Revise or regenerate the summary to address these gaps while maintaining the word limit.



### Output Requirements
Present the summary in Markdown under the heading '## Submission Instructions' with well-organized subsections containing section references as well. The summary should be comprehensive, self-contained, and understandable without requiring access to the original document.

---

### Context
{context}

Begin:
"""
    return prompt.strip()




def get_evaluation_metrics_prompt(context):
    prompt = f"""You are an expert procurement/solicitation analyst tasked with extracting and summarizing **only the evaluation criteria/factors and their related content** from procurement documents (e.g., RFPs, RFQs, RFIs, IFBs). The summary must stay within a strict 5500-word limit while capturing all critical evaluation factors, sub-factors, weights, scoring mechanisms, evaluation phases, and other criteria necessary for understanding the evaluation process. The final summary will be included as part of an understanding document for the procurement/contract.

### Primary Objective
Produce a detailed, accurate summary of evaluation metrics, keeping it below 5500 words, and focused exclusively on evaluation criteria/factors. Ensure that the section references are included in the summary. Exclude all unrelated sections (e.g., submission instructions, proposal preparation, post-award details). Ensure that no critical details related to evaluation factors/criteria are omitted. Make sure no such info related to evaluation of proposal is missed, without whose knowledge the offeror's proposal will not pass the evaluation process. Ensure that the summary only includes information about how the proposal submitted by the offeror will be evaluated, and no such evaluation criteria/factors are omitted without which the offeror's proposal will not pass the evaluation process. This summary will be used to guide the offeror in their proposal submission process, so it should be as clear, correct and concise as possible.

---

### Chain of Thought Approach

1. **In-Depth Review of Evaluation Sections**:
   - Thoroughly examine each section and subsection specifically related to evaluation criteria with the objective of capturing all the evaluation information. Ensure no such evaluation related info is ommited, without whose knowledge the offroor's proposal will not pass the evaluation process.:
     - Evaluation factors and sub-factors.
     - Weighting of each factor.
     - Scoring mechanism, including rating scales or scoring matrices.
     - Phases of evaluation, if applicable, and their order of importance.
     - Any other relevant evaluation criteria, such as technical qualifications, experience, or clearance requirements.
   - **Content Exclusion**: Ensure that sections related to submission instructions, post-award activities, or non-relevant content are excluded. Make sure not to include the content which will not help the offeror to understand the evaluation process, such as proposal preparation or post-award details, etc (whose presence in the summary, in any way, will not help the offeror to pass the evaluation process).



2. **Structured Summarization**:
   - Summarize each subsection of evaluation criteria separately, ensuring clear reference to specific sections (e.g., "[Section M.2.1]") for traceability.
   - Avoid redundancy by focusing on unique evaluation factors and omitting repetitive or non-essential details.

3. **Clear Formatting and Hierarchy**:
   - Use Markdown to replicate the original structure of the document, ensuring each evaluation factor and sub-factor is presented clearly.
   - Ensure H2 heading is used for the main title of the summary only.
   - Ensure section references are included in the summary.
   - Format tables, weights, and scoring matrices for readability.
   - **Efficient Table Formatting**:
     - Condense complex tables into lists or bullet points if they create excessive whitespace.
     - Mention that the data is derived from a table, and provide references such as table names or numbers (e.g., "Table 3-2").
     - Summarize key values from tables without omitting important information.

4. **Token Management and Prioritization**:
   - Ensure compliance with the 5500-word limit by prioritizing essential evaluation metrics, scoring mechanisms, and evaluation phases.
   - If nearing the word limit, focus on key factors and scoring details, reducing redundancies where possible.

5. **Quality and Completeness Check**:
   - **Cross-Verification**: Compare the generated summary with the original document to verify that no critical evaluation factors/criteria have been missed. Cross check if the section references are included in the summary and if they are accurate/correct.
   - **Accuracy**: Ensure all formatting, section references, and details align with the source document. The summary should be sufficient for the offeror to understand how his submissions will be evaluated. Ensure no such detail is missed without whose knowledge the offeror's proposal will not pass the evaluation process.
   - **5500-Word Limit**: Confirm that the final summary does not exceed 5500 words.

6. **Rework if Necessary**:
   - If any verification step fails, revisit and adjust the summary:
     - Identify gaps or issues in the extracted content.
     - Revise or regenerate the summary to address those issues while adhering to the word limit.


### Output Requirements
Present the summary in Markdown with a main heading of “## Evaluation Factors for Award.” Structure subsections clearly to detail each evaluation factor and scoring criteria. The summary should be self-contained and easy to understand, formatted for readability and clarity.


### Context
{context}

Begin:
"""
    return prompt.strip()





def get_list_of_attachments_prompt(context):
    prompt = f"""You are an expert procurement/solicitation analyst tasked with creating an accurate, concise summary of **explicitly mentioned attachments** in an RFP document, with a strict 5500-word limit. The summary should capture all crucial details of the attachments without including exhibits, FAR clauses, or any unrelated content.

### Primary Objective:
Ensure the summary is comprehensive yet concise, focusing only on attachments explicitly listed in the document. Do not infer or assume attachments that are not directly labeled as such. This summary will be used to guide the offeror in their proposal submission process, so it should be as clear, correct and concise as possible. If no attachments are explicitly mentioned, state, “No attachments are explicitly mentioned.”



### Chain of Thought Approach

1. **Attachment-Focused Identification**:
   - **Section-by-Section Review**: Carefully examine each section of the RFP document to identify attachments explicitly marked as such (e.g., "Attachment A"). **Do not assume or infer any attachments** that are not directly labeled.
   - **Exclude Non-Attachment Content**: Avoid including exhibits, FAR clauses, or any other supplementary items that are not clearly designated as attachments.
   - **Avoid Redundancy**: Summarize the details of each attachment without repeating any information unnecessarily.

2. **Detailed Attachment Summary**:
   - For each attachment explicitly mentioned, provide the following details:
     - **Attachment Number or Identifier** (e.g., "Attachment A")
     - **Title or Description** as written in the RFP.
     - **Special Instructions or Requirements**, such as file format or any specific instructions related to the attachment.
     - **Mandatory or Optional**: Indicate whether the attachment is mandatory or optional.
   - **Section Reference**: Include the section reference for each attachment (e.g., “[Section G.2]”) for improved traceability and context.

3. **Summary Completeness and Accuracy**:
   - **Avoid Omissions or Assumptions**: Ensure that no attachments are missed and avoid inferring attachments that are not explicitly listed.
   - **Preserve Structure**: Maintain the original order and numbering of the attachments as listed in the RFP.
   - **No Redundancies**: Make sure that each attachment is listed only once to prevent duplication.

4. **Token Management and Summarization Efficiency**:
   - **Adhere to 5500-Token Limit**: Summarize the attachments efficiently within the word constraint, focusing only on the critical attachment details.
   - **Condensation Strategy**: In case the document is lengthy, reduce extraneous details, but ensure that essential attachment information is preserved.

5. **Final Verification and Quality Control**:
   - **Cross-Check for Accuracy**: Compare the generated summary with the RFP to ensure all explicitly listed attachments are included. Make sure no attachment details are missed or misrepresented.
   - **Clarity and Relevance**: Ensure that the summary is clear, concise, and free from redundancy. Only relevant attachment details should be included.
   - **5500-Word Limit**: Ensure the number of words in the generated summary is under **5500 words**.
   - **Rework if Necessary**: If any of the final verification and quality control checks fail, rework the summary by starting over with the additional objective of rectifying the issue that caused the failure.



### Output Format:
Present the summary in Markdown with the main heading of “## List of Attachments.” If there are attachments, use the following table format to present them:

## List of Attachments

| Attachment ID | Title/Description | Additional Notes |
| --- | --- | --- |
| Attachment A | Sample Technical Capability Template | Mandatory |
| Attachment B | Past Performance Reference Form | Optional |
| Attachment C | Pricing Spreadsheet | Mandatory, Excel format |

If **no attachments** are explicitly mentioned:

## List of Attachments

No attachments explicitly mentioned.



### Context:
{context}

Begin:
"""
    return prompt.strip()



def get_key_personnel_prompt(context):
    prompt = f"""As an expert procurement analyst, your task is to document a complete summary of **key personnel requirements** from an RFP document. This summary should focus exclusively on roles explicitly designated as "key personnel," excluding other labor categories or general staffing requirements.


## Objective:

Extract and summarize the information about the key personnels whose involvement will be most required during performing the required task areas of the contract. Ensure that only those roles are identified as key personnel which are explicitly labelled as key personnel in the procurement/contract document. Ensure that section referencing is included in the summary and should be correct. This summary will be used to guide the offeror in their proposal submission process, the offeror will use this summary also to check if the key personnels required for the contract are available. so it should be as clear, correct and concise as possible.

### Chain of Thought Approach

1. **Identification of Key Personnel**:
   - **Targeted Review**: Carefully examine sections, tables, or specific paragraphs in the RFP that list required personnel, focusing on those explicitly marked as "key personnel." **Do not include non-designated roles** like SMEs or general staffing. Ensure that the key personnel whose involvement will be most required (explicitly labelled as key personnel) during performing the required task areas of the contract are identified, so that the offeror can easily check if the key personnels required for the contract are available.
   - **Strict Personnel Criteria**: Include only those positions that are **explicitly designated** as "key personnel," ensuring no confusion with other labor categories or staffing roles.
   - **Role-Specific Clarity**: For each key personnel role, focus on clearly documenting the **title** and **specific responsibilities** associated with it. These roles should be distinct from non-key positions in terms of scope and expectations.

2. **Detailed Key Personnel Summary**:
   For each identified key personnel position, provide the following details along with the section reference:
   - **Title/Role**: The exact name of the position (e.g., Project Manager, Lead Engineer).
   - **Responsibilities**: Summarize the **primary responsibilities** and scope of work for the role.
   - **Qualifications**: Detail the **required qualifications**, including education, certifications, and specific experience needed.
   - **Staffing Level**: Specify whether the position is full-time, part-time, or any other staffing level mentioned.
   - **Replacement Process**: If specified, document the process for **replacing key personnel** during the project.
   - **Resume Requirements**: Include **any specific resume requirements**, such as page limits, format, and content.
   - **Special Requirements**: Highlight any **special requirements**, such as **security clearances, unique skills, or certifications** that are essential for the role.



3. **Quality Assurance**:
   - **Verification**: Cross-reference the summary with the original RFP document to ensure only designated "key personnel" are included. Confirm that no non-designated roles are mistakenly categorized as key personnel.
   - **Accuracy of Qualifications and Responsibilities**: Ensure that qualifications, responsibilities, and other requirements for each key personnel role are accurately summarized.
   - **Section Referencing**: Include section references (e.g., “[Section H.5]”) for each key personnel position to improve traceability.
   - **No Key Personnel Listed**: If the RFP does not specify any key personnel, explicitly state this in the summary.
   - **5500-Word Limit**: Ensure that the generated summary remains below **5500 words** in total.

   **Important Note**: If any part of the Quality Assurance process fails, restart the summary generation process, ensuring that the issues causing failure are rectified and no errors are repeated.

4. **Output Requirements**:
   Present the key personnel summary in a clear, structured Markdown format with the main heading as `## Key Personnel`. Ensure H2 heading is used for the main title of the summary only. Ensure section referencing is included. Each role should be documented with the following structure, with no additional text, commentary, or notes:

## Key Personnel

### [Key Personnel Role/Title]
- **Responsibilities**: [Summarize the key responsibilities and scope of work] [Section Reference]
- **Qualifications**: [List required education, certifications, experience, etc.] [Section Reference]
- **Staffing Level**: [Specify if full-time, part-time, etc.] [Section Reference]
- **Resume Requirements**: [Document any requirements for resumes, including page limits and content] [Section Reference]
- **Special Requirements**: [Include security clearances, specialized skills, or other unique requirements] [Section Reference]

If No Key Personnels are explicitly mentioned in the RFP, then output should be:-

## Key Personnel

No Key Personnel mentioned.

Ensure that the final output is comprehensive, concise, and self-contained, with no reference required to the original RFP document. The summary must also remain under 5500 tokens.



### Context
{context}

Begin:
"""
    return prompt.strip()


def get_red_flags_prompt(context):
    prompt = f"""As an expert procurement analyst, you are tasked with thoroughly reviewing the RFP document to identify and summarize only the **most impactful and critical red flags** that may significantly affect proposal submission or contract performance.


## Objective:
Go through the whole procurement/contract content to look for potential red flags that may affect proposal submission or contract performance. These red flags you can look for are:
- **Proposal Submission Red Flags**: Focus on conditions or requirements that may be extremely challenging or problematic in submitting the proposal.
- **Contract Performance Red Flags**: Focus on conditions that may cause significant challenges during the execution of the contract.

This summary will be used to guide the offeror in their proposal submission process, and the offeror will be expected to address these red flags in their proposal. Also it should be as clear, correct and concise as possible.


### Chain of Thought Approach

1. **INITIAL RED FLAG ANALYSIS**:
   - **Proposal Submission Red Flags**: Focus on conditions or requirements that may be extremely challenging or problematic in submitting the proposal. The red flags related to submission which the offeror should be aware of:
     - **Past Performance Requirements**: Look for unusually stringent or challenging past performance requirements.
     - **Key Personnel Requirements**: Flag positions requiring rare certifications, highly specialized skills, or difficult-to-find experience.
     - **Resume Requirements**: Identify overly detailed or difficult-to-follow resume formats.
     - **Clearance Requirements**: Point out hard-to-obtain clearances (e.g., high-level security clearances).
     - **Place of Performance**: Note if performance requires working in a remote or restricted area that poses significant logistical challenges.
     - **License Requirements**: Flag the need for rare or hard-to-acquire licenses.
     - **Proposal Submission Deadline**: Identify if the timeline for submission is unreasonably tight or unrealistic.
   - **Contract Performance Red Flags**: Focus on conditions that may cause significant challenges during the execution of the contract. The red flags related to contract performance which the offeror should be aware of:
     - **Place of Performance**: Identify if remote or inaccessible locations with resource scarcity are involved.
     - **Budget and Funding**: Look for indications of unstable or insufficient funding for the project.
     - **Required Tools and Technologies**: Flag any requirement for significant new investments or proprietary tools that may introduce risk or cost.
     - **Set-Aside Requirements**: Identify restrictive set-aside eligibility that may limit competition.
     - **Contracting Agency Considerations**: Look for known challenges in dealing with the contracting agency, such as a poor track record or excessive administrative burdens.
   - **Additional Red Flags**: Highlight **only the most severe uncertainties** or burdens that may critically affect performance. Keep the focus on the major red flags.

2. **EVALUATION AND PRIORITIZATION**:
   - **Assess Impact**: Evaluate the severity of each identified red flag by considering its potential to:
     - Pose a **severe compliance risk**,
     - Involve **major resource implications**,
     - Lead to a **high likelihood of performance failure**.
   - **Prioritize**: Include only the red flags that pose the **greatest challenges** to successful proposal submission or contract performance. Ensure that the section references are included in the summary.

3. **QUALITY ASSURANCE**:
   - **Cross-Check Accuracy**: Review the identified major red flags against the original RFP document. Ensure they are accurately cited.
   - **Verify Section References**: Each red flag should be linked to the appropriate section of the RFP (e.g., Section E.5) for traceability.
   - **Word Limit**: Keep the summary under **5500 words**.
   - **Revisit if Necessary**: If any quality assurance checks fail, regenerate the summary, focusing on addressing the issues to ensure it meets the required standards.

4. **FINAL OUTPUT**:
   Present the red flags in a clear, structured Markdown format with the main heading as `## Red Flags`. Ensure H2 heading is used for the main title of the summary only. The sections should be organized as follows:

## Red Flags

### Proposal Submission Red Flags
- [Most impactful red flag 1, with section reference]
- [Most impactful red flag 2, with section reference]

### Contract Performance Red Flags
- [Most impactful red flag 1, with section reference]
- [Most impactful red flag 2, with section reference]

- If **no significant red flags** are identified, explicitly state:
  `"No specific Red Flags identified in the RFP document."`

Ensure the final output contains **only the most critical and impactful red flags**, with no additional commentary or text.



### Context
{context}

Begin:
"""
    return prompt.strip()


def get_outline_breakdown_prompt(outline):
    prompt = f"""Role: Intelligent Outline Breakdown Specialist

Objective:
- Transform the provided outline into a structured JSON representation with complete content
- Optimize section breakdown for proposal writing workflow
- Ensure each section is self-sufficient and independent
- Include the exact content from the outline under each section

Breakdown Principles:
1. Section Consolidation Strategy:
   - Identify and preserve any content before the Table of Contents (TOC) as a separate section
   - Include the Table of Contents (TOC) itself as a distinct section
   - Use the TOC as the primary guide for identifying major sections and their subsections
   - Combine small, related sections (3-4 consecutive sections with minimal subsections)
   - Break down large, complex sections with multiple substantive subsections
   - Maintain original outline structure and hierarchy
   - Create sections that can be written independently

2. JSON Output Requirements:
   - Capture exact section and subsection titles
   - Include the complete original content under each subsection
   - Number sections sequentially
   - Group related small sections
   - Ensure each section is comprehensive yet manageable
   - Include "Pre-TOC Content" section if content exists before the TOC
   - Include "Table of Contents" section with the original TOC content

3. Content Preservation Guidelines:
   - Copy the exact content from the outline verbatim under each subsection
   - Include all instructions, requirements, deliverables, and specifications
   - Preserve formatting, bullet points, and detailed descriptions
   - Do not summarize or paraphrase - copy the complete original text
   - Maintain all technical details and specific requirements

4. Processing Guidelines:
  - Identify and separate content that appears before the Table of Contents
   - Preserve the Table of Contents as its own section
   - Ignore administrative elements (page numbers, headers, footers)
   - Focus on substantive content sections
   - Preserve original section order and relationships
   - Create logical, coherent section groups

5. Writing Workflow Considerations:
   - Enable proposal writers to tackle sections in multiple writing sessions
   - Make each section a self-contained writing unit with complete content
   - Allow flexibility in writing sequence while maintaining document flow

Specific Handling Rules:
- Always check for and preserve content that appears before the Table of Contents
- Always include the Table of Contents itself as a separate section in the JSON output
- If a section has multiple complex subsections, break it into separate entries
- For sections with minimal content, combine them into logical groups
- Maintain the semantic integrity of the original outline
- Include ALL content that appears under each section/subsection in the original outline

Input Outline:
{outline}


Example JSON: REMEMBER THAT IT IS COMPULSORY TO FOLLOW THIS JSON LAYOUT ALWAYS.

{{
  "sections": [
    {{
      "section_number": "pre-toc",
      "section_title": "Content Before Table of Contents",
      "subsections": [
        {{
          "subsection_title": "Cover Page",
          "content": "Complete original content that appears before the TOC including all text exactly as written in the source outline."
        }}
      ]
    }},
    {{
      "section_number": "toc",
      "section_title": "Table of Contents",
      "subsections": [
        {{
          "subsection_title": "Table of Contents",
          "content": "Complete original TOC content exactly as written in the source outline."
        }}
      ]
    }},
    {{
      "section_number": "1-7",
      "section_title": "Sections 1-7: Administrative and Compliance Responses",
      "subsections": [
        {{
          "subsection_title": "1 Assumptions",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "  1.1 Technical Assumptions",
          "content": "Complete original content from the outline for this subsection including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "  1.2 Price Assumptions",
          "content": "Complete original content from the outline for this subsection including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "2 SF 1449",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "3 Acknowledgment of Solicitation Amendments",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "4 Completion of Representations",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "5 Attestation",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "6 Conflict of Interest Mitigation Plan",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }},
        {{
          "subsection_title": "7 GSA Federal Supply Schedule (FSS)",
          "content": "Complete original content from the outline for this section including all instructions, requirements, deliverables, and specifications exactly as written in the source outline."
        }}
      ]
    }}
  ]
}}

Additional Guidance:
- Prioritize clarity and writability
- Ensure comprehensive coverage
- Maintain the document's original intent and structure
- If there is no subsection available for a section, then include the section_title as a subsection_title
- CRITICAL: Include the complete, exact content from the original outline under each subsection's "content" field
- Do not summarize, paraphrase, or modify the original content - copy it verbatim
- Include all technical specifications, requirements, deliverables, and instructions exactly as they appear
- Do not add additional text/commentary at the beginning or end of the response e.g., Here is the response ... or Okay, I will now generate the content or other unwanted/unrelevant commentary.
"""
    return prompt


def get_response_content_prompt(full_outline, current_section_outline, past_performance, procurement_summary):
    prompt = f"""
Role: Proposal Writer & Compliance Strategist at TENANT_NAME  
Mode: You must act as both **validator and generator**.

You are drafting ONE specific RFP response section using the inputs provided.  
Before you generate anything, **analyze** each item in the `section_outline` to determine:

---

## Phase 1: Agentic Validation

For each required section component:
1. Check `knowledge_base`:  
   - Is there clear, detailed, directly relevant content?
   - If yes →  mark as usable.
   - If partially relevant or too short → use what exists only.
   - If not available →  skip it.

2. If the component is **mentioned in `rfp_summary`** but no full content is in `knowledge_base`:
   - Note:  
     `"No detailed content found. This was only referenced in high-level inputs."`
   - Then include **brief mention** with disclaimer.

3. Track:
   - Items with no data
   - Items mentioned only in summaries
   - Exact matches with usable content

---

## Phase 2: Final Response Generation

Now generate the full section **ONLY after validation** is complete.

**Rules for Response:**
- Follow `section_outline` exactly.
- Use only content validated in Phase 1.
- Use "we" or "TENANT_NAME" tone (professional + persuasive).
- Preserve order from `knowledge_base` unless chronology is stated.
- Add `// Source: [Exact content snippet]` after each subsection.
- If an item is skipped due to no data, clearly state:  
  `"Content not available in the provided knowledge base."`

---

## Output Format:

**Section Title:** {current_section_outline}  
**TOC Reference:** Appears in TOC: Yes/No  


**Generated Content:**  
[Structured, source-tagged content]

---

**HELP TEXT: Agentic Validation Summary (Pre-Generation Checks):**
-  Direct Matches from Knowledge Base: [List]
-  Only Mentioned in Summary, Not Detailed: [List]
-  No Data Found for These Items: [List]
- Duplication Check: Pass/Fail  
- Sequence Check: Pass/Fail

---

Inputs:
- RFP Outline: {full_outline}  
- Section Outline: {current_section_outline}  
- RFP Summary: {procurement_summary}  
- Knowledge Base: {past_performance}
"""
    return prompt

def get_outline_prompt(RFP_CONTEXT):
    """Generate a prompt for RFP outline generation.

    Args:
        RFP_CONTEXT: The content of the RFP document to analyze

    Returns:
        str: Formatted prompt for the LLM
    """

    outline_prompt = f"""
    You are a senior RFP compliance officer and proposal architect agent.
    I will provide you the raw text from an RFP (UCF-based or non-UCF-based).
    Your job is to extract and structure a proposal response outline in Markdown format, by identifying only the sections that are relevant and required for drafting the actual proposal response.
     Your Primary Objectives:
        1. Identify and include only those sections that directly impact proposal development:
            § Proposal instructions / Section L (if UCF)
            § Evaluation criteria / Section M (if UCF)
            § Technical/Management/Price volume structures
            § Compliance requirements (certifications, forms, appendices)
            § Matrices or tables like LCM, BOM, Staffing Plan, etc., that the proposal must include
            § Any mandatory formatting or submission protocols
            § Response-specific templates, outlines, or forms mentioned in the RFP
            § For each relevant section/volume identified, provide the corresponding evaluation criteria. If no specific evaluation criteria are stated for a particular section, explicitly state "No Evaluation Criteria."
        2. Automatically detect whether the RFP follows UCF (Uniform Contract Format) or non-UCF, and adjust the section detection accordingly.
        3. Ignore sections that are not relevant to the proposal structure (e.g., agency background, contract clauses, general instructions) — but log them under a structured tag so they can be removed in post-processing.
     Markdown Output Format:
    For each relevant section/volume:
    ## Volume 1: Technical Proposal
    ### Instructions:
- [PASTE full instruction text from RFP — **do not summarize, misinterpret, or derive understandings.** Specifically capture details like page limits, due dates, point of contact, place of performance, method of submission, and proposal title.]
 
    ### Evaluation Criteria:
- [Extract verbatim from RFP]
    ### Compliance Checklist:
- [List each required element, file, or format]
    <!--optional: This section is required but has optional elements.-->
<!--irrelevant: This section was present in the RFP but skipped from the outline.-->
     Reflection & Assurance:
    After generating the outline:
        ○ Re-scan and verify that all sections required to structure the proposal have been included.
        ○ If any section is borderline or unclear, include it and flag it as <!--optional-->.
        ○ Any skipped content must be listed with a <!--irrelevant--> tag so it can be filtered later.
     Rules:
        ○ Do not summarize or interpret — extract and structure exactly.
        ○ Be exhaustive with all proposal-building sections.
        ○ Maintain original RFP order where applicable.
        ○ Avoid all filler words and phrases in your response at the beginning or end of the response. Write with precision and concision.
        ○ Eliminate sentences and expressions that add no value.
        ○ Use direct, clear language without hedging or unnecessary qualifiers.
        ○ The section numbering or volume numbering should start from 1 and should be continuous, if not provided in the RFP.
        ○ The section numbering or volume numbering should be in the format of "Volume 1: Technical Proposal" or "Section 1: Technical Proposal" not "Volume 1/Section 1: Technical Proposal".
        ○ Do not use invalid markdown syntax. Instead, always use standard Markdown syntax. Failure to follow this may result in penalties. e.g do not use <br> tags instead use new line characters.

    I will now provide the full RFP text.
 
{RFP_CONTEXT}
"""
 
    return outline_prompt


def get_rfp_specific_review_prompt(content, evaluation_rules):
    return f"""
You are an expert RFP specific parameter creator.

You will create the evaluation parameters based on the provided **RFP content** using the **provided evaluation criteria**. Base all your parameters specifically **to the RFP content** with the main goal of making the evaluation criteria/parameters self contained and specific to the provided RFP content.

The evaluation criteria/parameters should be **self-contained**, meaning the evaluation criteria/parameters must be easily evaluable on its own without needing additional context.

**output format instructions**:
- The output format should match the format of the provided evaluation rules.
- Do not change the value of the name and type fields.
- Only change the value of the check field so that it is self contained and specific to the provided RFP content.

---
RFP Content:
{content}
---

Evaluation Rules:
{evaluation_rules}

---
""".strip()


def get_review_prompt(content, evaluation_rule):
   
   rule_name = evaluation_rule.get("name", "Unnamed Rule")
   rule_type = evaluation_rule.get("type", "Unknown Type")
    
   

   return f"""
You are a professional proposal reviewer.

You will evaluate the full proposal content using the following single evaluation rule.

Your job is to apply the selected rule to the **entire proposal document** and return one overall evaluation.

---


**Output Format (Strict JSON only)**:
All fields must be strings. Do **not** use list or array structures in the output. Format all tips or suggestions as a single paragraph or as numbered lines within a single string.

{{
  "rule_name": "{rule_name}",
  "rule_type": "{rule_type}",
  "score": "X/10 or N/A",
  "feedback": "Observation about how this rule is reflected across the proposal.",
  "suggestions": "Improvement suggestions, if any, or write 'N/A'."
}}

Only return the JSON. Do not write anything outside the JSON block.
ONLY return raw JSON. 
- Do NOT use markdown formatting (no ```json ... ```).
- Do NOT include explanatory text.
- Output must start with '{{' and end with '}}'

---

**Evaluation Rule**:
{evaluation_rule}

---

**Proposal Content**:
{content}
""".strip()

