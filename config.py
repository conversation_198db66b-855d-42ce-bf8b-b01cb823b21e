from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    APP_HOST: str = "https://ddbtuui4e9bya.cloudfront.net"
    MAIL_USERNAME: str
    MAIL_PASSWORD: str
    MAIL_FROM: str
    MAIL_PORT: int
    MAIL_SERVER: str
    MAIL_FROM_NAME: str
    GOOGLE_APPLICATION_CREDENTIALS: str
    MONGO_URI: str
    SECRET_KEY: str
    FORGET_PWD_SECRET_KEY: str

    # Vector DB Configuration
    create_vector_db: bool = True  # Controls whether to create/update vector DB during file uploads

    # RAG-related settings
    RAG_RETRIEVAL_MODE: str = "default"  # Options: "default", "advanced", "advanced-pro"
    RAG_USE_MULTIPLE_QUERIES: bool = True
    RAG_CHUNK_SIZE: int = 2500
    RAG_CHUNK_OVERLAP: int = 1200
    RAG_TOP_K: int = 50  

    # Image processing settings
    PROCESS_IMAGES: bool = True  # Whether to extract text from images
    IMAGE_PROCESSING_METHOD: str = "vision"  # Options: "vision" or "tesseract"
    # IMAGE_PROCESSING_METHOD: str = "tesseract"  # Options: "vision" or "tesseract"
    MIN_IMAGE_SIZE: int = 10000  # Minimum image size in bytes to consider valid
    MIN_IMAGE_VARIANCE: float = 10.0  # Minimum pixel variance to consider image non-blank
    
    # Vision API settings
    VISION_PROMPT: str = """Extract all the content from the image of the document, maintaining the structure and layout. Follow these rules:  

1. **Text Extraction**:  
   - Do not extract the text in the header or footer of the page.  
   - Present the extracted text in markdown format, ensuring readability.  

2. **Table Extraction**:  
   - If the document contains tables, reproduce the table structure in markdown format.  
   - Include only complete and unambiguous table content.  

3. **Grid Structure**:  
   - If the content is organized in a grid or sectioned layout, carefully analyze the content in each cell or block.  
   - If there are visual elements like checkboxes, thouroughly map the checkboxes to their corresponding cells without any errors.
   - Extract data by understanding its meaning, context, visual cues (e.g., Solicitation Method, Solicitation Number, Set Aside, Issued By). 
   - For Acquisition (Block that has info regarding Acquisition), extract the correct content taking into account the visual cues and layout (like checkboxes at the right side of each possible value in the grid). First check if acquisition is unrestricted or set aside (you can do this by checking if the checkbox at the left of UNRESTRICTED is checked or the checkbox at the left of SET ASIDE IS CHECKED). After that if the ACQUISITION is SET ASIDE, then what category of set aside and what is the percentage of set aside if mentioned (After SET ASIDE if any percentage is mentioned then do consider it as it shows the percentage of the ACQUSITION for the SET ASIDE, ALSO, we have to see what category of the SET ASIDE is it, For that check which categories left checkbox is checked). In your response, Do not include those values whose left checkboxes are unchecked.
   - For each cell/numbered section, extract the correct content taking into account the visual cues and layout.

4. **Comprehension of Layout**:  
   - Pay attention to visual cues like labels, headings, and alignments to extract accurate data.  
   - Ensure the extracted content matches its corresponding grid or labeled section.  

5. **Additional Notes**:  
   - Extract content holistically rather than line by line to preserve relationships and context.  
   - Focus on accuracy and completeness of the extracted content.  

Ensure the output is clean, structured, and reflects the original intent and layout of the document.
"""

    BUCKET_NAME: str = "mvp-development-431809_cloudbuild"

    #  style for pdf 
    CSS_FOR_PDF:str = """

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    color: #000;
}

h1 {
    font-size: 24px;
}

h2 {
    font-size: 22px;
}

h3 {
    font-size: 20px;
}

h4 {
    font-size: 18px;
}

h5 {
    font-size: 16px;
}

h6 {
    font-size: 14px;
}

p {
    line-height: 1.5;
    text-align: justify; /* Justify paragraphs */
}

/* Table styling */
table {
    width: 100%;
    border-collapse: collapse;
    font-family: Arial, sans-serif;
    font-size: 14px;
}

th, td {
    border: 1px solid #000; /* Solid borders for cells */
    padding: 8px;
    text-align: left; /* Align table cells to the left */
    text-align: justify; /* Justify table cell content */
}

th {
    background-color: #f4f4f4; /* Light gray for header background */
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f9f9f9; /* Alternating row colors */
}

"""

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()