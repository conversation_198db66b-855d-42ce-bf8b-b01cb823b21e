# Document Ingestion and Processing Pipeline - Technical Implementation

## Overview

This document provides a technical implementation guide for the ProposalPro document ingestion and processing pipeline based on the architectural diagram. The system processes documents through a structured workflow from upload to vector database creation, following the exact flow depicted in the architecture.

## Architecture Flow

1. **Stage 1: Document Upload Stack** → Document validation and routing
2. **Stage 2: PDF Processing** → Document structure analysis and page identification
3. **Stage 3: Page-wise Content Extraction** → Vision processing decision point and content extraction
4. **Stage 4: Pickle File Creation** → Serialization and storage of processed content
5. **Stage 5: Vector Database Creation** → Text chunking and embedding generation
6. **Stage 6: Vector DB Storage** → Final persistent storage and indexing

## Stage 1: Document Upload Stack

### Upload Stack Components 

The upload stack handles the initial document reception and validation:

**Input**: Processing Docs (PDF/DOCX/XLSX files)
**Process**: Upload validation and routing
**Output**: Validated document ready for processing

**Technical Implementation**:
- File format validation (PDF/DOCX support)
- Metadata extraction and tagging
- Routing to appropriate processing pipeline

## Stage 2: PDF Processing

### PDF Processing Decision Flow 

**Input**: PDF File from upload stack
**Decision Point**: "PDF Processing" Routes to page-wise content extraction
**Process**: Document structure analysis and page identification

**Technical Implementation**:
```
PDF Document → Page Analysis → Special Page Detection → Content Routing
```

**Key Features**:
- Page-by-page processing approach
- Special page identification (tables, images, complex layouts)
- Metadata preservation throughout processing
- Error handling for corrupted or complex documents

## Stage 3: Page-wise Content Extraction

### Content Extraction Flow 

#### 3.1 Initial Page Processing
**Input**: Individual PDF pages
**Decision Point**: "Page-wise Content Extraction"
**Process**: Determine extraction method per page

#### 3.2 Vision Processing Decision 
**Decision Point**: "Vision Processing?" (Yes/No)

**YES - Vision Processing**:
- **Input**: Complex pages (tables, images, special formatting)
- **Process**: Vision model processing
- **Output**: Structured text content
- **Implementation**: Vertex AI Vision API integration

**NO - Standard Processing**:
- **Input**: Standard text pages
- **Process**: Direct markdown format text extraction
- **Output**: Markdown text content
- **Implementation**: pymupdf4llm for markdown text extraction

#### 3.3 Content Consolidation
**Process**: Merge vision and standard processing outputs
**Output**: Unified page content with metadata

**Technical Implementation Details**:
- **Vision Processing**: Uses Vertex AI Gemini model for complex layout understanding
- **Standard Processing**: PyMuPDF4LLM for efficient text extraction
- **Content Merging**: Maintains page order and metadata consistency
- **Quality Control**: Fallback mechanisms for processing failures

**Code Reference**: Vision processing in `RAG/documents_loader.py` with `extract_text_with_vision()` and `load_pdf_or_doc()` functions.

## Stage 4: Pickle File Creation and Storage

**Input**: Processed document sections from Stage 3
**Process**: Serialize processed content into pickle format for fast retrieval
**Output**: Pickle file stored in GCS alongside original document

**Benefits**:
- Fast document retrieval without reprocessing
- Preserves all processing metadata and content sources
- Enables efficient content access for vector database creation
- Supports quick document preview and content review


## Stage 5: Vector Database Creation

### Vector DB Creation Flow

**Input**: Processed document content from Stage 4 (pickle files for fast access)
**Process**: Vector database creation and storage
**Output**: Vector DB stored and ready for retrieval

#### 5.1 Content Chunking
**Process**: Split processed content into manageable chunks
**Implementation**:
- Chunk size: 2500 characters
- Overlap: 1200 characters for context preservation
- Metadata preservation: Page numbers, source tracking, tags

#### 5.2 Vector Embedding Generation
**Process**: Convert text chunks to vector embeddings
**Implementation**:
- Embedding Model: Vertex AI Text Embedding
- Batch processing for efficiency
- Error handling for embedding failures

#### 5.3 Vector Database Storage
**Process**: Store embeddings in FAISS vector database
**Implementation**:
- FAISS index creation and optimization
- Metadata association with vectors
- Persistent storage preparation

## Stage 6: Vector DB Storage (Final Stage)

### Final Storage Implementation 

**Input**: Created vector database from Stage 5
**Process**: Persistent storage and indexing
**Output**: Vector DB available for retrieval operations

#### 6.1 Storage Architecture
**Primary Storage**: Google Cloud Storage (GCS)
**Structure**:
```
Projects/{project_name}_{project_id}/
├── documents/           # Original files (PDF/DOCX)
├── processed_documents/ # Pickle files with processed content
└── vector_db/          # FAISS vector indices
```

#### 6.2 Database Indexing
**Metadata Storage**: MongoDB collections
**Index Structure**:
- Project-level metadata
- Document-level metadata
- Vector database references
- Processing status tracking

#### 6.3 Retrieval Optimization
**Implementation**:
- FAISS index optimization for fast similarity search
- Pickle file caching for rapid document access
- Metadata filtering capabilities
- Hybrid search support (vector + keyword)
- Preferential loading from pickle files over original documents

### Configuration Parameters

#### Vector Database Configuration
```python
# From config.py - Vector DB settings
create_vector_db: bool = True
RAG_CHUNK_SIZE: int = 2500
RAG_CHUNK_OVERLAP: int = 1200
RAG_TOP_K: int = 50
```

#### Vision Processing Configuration
```python
# From config.py - Vision API settings
PROCESS_IMAGES: bool = True
IMAGE_PROCESSING_METHOD: str = "vision"
VISION_PROMPT: str = "Extract all content from the image..."
```

## Error Handling Strategy

### Stage-Specific Error Handling

#### Stage 1: Upload Stack Errors
**Error Types**: File format validation
**Handling**:
- Immediate validation and rejection of invalid files
- User feedback with specific error messages
- Logging for monitoring upload patterns

#### Stage 2: PDF Processing Errors
**Error Types**: Corrupted PDFs, unsupported formats, parsing failures
**Handling**:
- Fallback to alternative PDF processing libraries
- Graceful degradation to basic text extraction
- Error logging with document metadata

#### Stage 3: Vision Processing Errors
**Error Types**: Vision API failures, image extraction issues, quota limits
**Handling**:
- Automatic fallback to standard text extraction
- Retry logic with exponential backoff
- Quality assessment of vision output

#### Stage 4: Pickle File Creation Errors
**Error Types**: Serialization failures, GCS upload issues, storage quota limits
**Handling**:
- Retry pickle creation with error logging
- Fallback to processing without pickle storage
- Graceful degradation to direct vector processing
- Storage health checks and quota monitoring


#### Stage 5: Vector Database Creation Errors
**Error Types**: Embedding generation failures, FAISS index issues, memory constraints
**Handling**:
- Batch processing for large documents
- Memory optimization and cleanup
- Alternative embedding models as fallback

#### Stage 6: Storage Errors
**Error Types**: GCS connectivity issues, quota exceeded, permission errors
**Handling**:
- Retry mechanisms with exponential backoff
- Local temporary storage as backup
- Health checks for storage services


## Data Flow and Transformations

### Data Transformation at Each Stage

#### Stage 1: Document Upload Stack → Stage 2
**Input Format**: Raw file bytes (PDF/DOCX/XLSX)
**Output Format**: Validated document object
**Transformation**: File validation and metadata extraction

#### Stage 2: PDF Processing → Stage 3
**Input Format**: Document object
**Output Format**: Individual page objects
**Transformation**: Document decomposition into pages

#### Stage 3: Page-wise Content Extraction → Stage 4
**Input Format**: Individual page objects
**Output Format**: Structured content with metadata
**Transformation**:
- Vision processing: Image → Structured text
- Standard processing: Raw text → Cleaned text

#### Stage 4: Pickle File Creation → Stage 5
**Input Format**: Processed document sections
**Output Format**: Serialized pickle file
**Transformation**: Document sections → Pickle serialization → GCS storage

#### Stage 5: Vector Database Creation → Stage 6
**Input Format**: Structured content (from pickle files or direct processing)
**Output Format**: Vector embeddings with metadata
**Transformation**: Text chunking → Embeddings generation

#### Stage 6: Vector DB Storage (Final)
**Input Format**: Vector database object
**Output Format**: Persistent storage references
**Transformation**: In-memory index → Persistent storage

### Metadata Preservation Throughout Pipeline

**Document Metadata**:
```python
{
    "source": filename,
    "tags": document_tags,
    "sf_pages": special_formatting_pages,
    "total_pages": page_count,
    "processing_timestamp": datetime,
    "content_sources": ["text", "vision"]  # Per page
}
```

**Page Metadata**:
```python
{
    "page_number": int,
    "content_source": "text" | "vision",
    "processing_method": "standard" | "vision_api",
    "has_tables": boolean,
    "has_images": boolean
}
```

**Vector Metadata**:
```python
{
    "chunk_id": unique_identifier,
    "source_page": page_number,
    "document_source": filename,
    "tags": document_tags,
    "chunk_index": position_in_document
}
```

## Technology Stack

### Core Processing Technologies
- **PDF Processing**: PyMuPDF4LLM (fitz) for standard text extraction
- **Vision Processing**: Vertex AI Gemini 1.5 Flash for complex layouts
- **Document Loading**: LangChain document loaders for unified interface
- **Text Chunking**: LangChain RecursiveCharacterTextSplitter

### Vector Database Technologies
- **Vector Store**: FAISS for similarity search and indexing
- **Embeddings**: Vertex AI Text Embedding Gecko model
- **Storage**: Google Cloud Storage for persistent vector indices
- **Metadata**: MongoDB for document and processing metadata

### Infrastructure Technologies
- **API Framework**: FastAPI with async support
- **File Handling**: aiofiles for async file operations
- **Cloud Storage**: Google Cloud Storage with async client
- **Database**: MongoDB with Motor async driver
- **Serialization**: Python pickle for processed document storage
