"""Document Chunking Module for RAG Processing

This module provides functionality for splitting documents into smaller, overlapping chunks
suitable for vector database storage and retrieval. It handles the conversion of custom
ProjectDocument instances to LangChain Document format while preserving metadata.

Key Features:
- Recursive character-based text splitting
- Metadata preservation and enhancement
- Page-level content organization
- Configurable chunk size and overlap
- Special formatting page handling
"""

from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

def default_text_chunking(docs, chunk_size=2500, chunk_overlap=1200):
    """Split documents into overlapping chunks while preserving metadata.
    
    This function processes ProjectDocument instances, converting them into LangChain
    Document format and splitting them into smaller chunks suitable for vector storage.
    It maintains page-level organization and preserves document metadata.
    
    Args:
        docs: List of ProjectDocument instances to process
        chunk_size: Maximum size of each chunk in characters (default: 2500)
        chunk_overlap: Number of characters to overlap between chunks (default: 1200)
        
    Returns:
        List of LangChain Document instances representing the chunked content
        
    Features:
        - Recursive character splitting for optimal chunk boundaries
        - Metadata preservation from source documents
        - Page-specific metadata enhancement
        - Content source tracking
        - Special formatting page handling
    """
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=len,
        is_separator_regex=False,
    )

    # Convert ProjectDocument pages to Langchain Document format
    langchain_docs = []
    for doc in docs:
        # Create base metadata from ProjectDocument metadata
        base_metadata = doc.metadata.copy()
        
        # Create a separate document for each page
        for page in sorted(doc.sections, key=lambda x: x['page_number']):
            # Update metadata with page-specific info
            page_metadata = base_metadata.copy()
            page_metadata.update({
                'page_number': page['page_number'],
                'is_sf_page': page['page_number'] in doc.sf_pages,
                'content_source': page.get('content_source', 'text')
            })
            
            # Create document with page content
            page_content = f"Page {page['page_number']}:\n{page['content']}"
            
            # Split the page content into chunks
            chunks = text_splitter.create_documents(
                texts=[page_content],
                metadatas=[page_metadata]
            )
            langchain_docs.extend(chunks)

    return langchain_docs 