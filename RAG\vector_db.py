"""Vector Database Operations Module for RAG System

This module provides comprehensive functionality for managing vector databases in the RAG system,
including creation, storage, retrieval, and search operations. It supports multiple retrieval
methods, hybrid search, and document management operations.

Key Features:
- FAISS vector database integration
- Multiple retrieval strategies (default, advanced, hybrid)
- Vertex AI embeddings integration
- Document tag management
- Async operations support
- GCS storage integration
- Extensive logging and error handling
"""

from langchain_community.vectorstores import FAISS
from langchain_google_vertexai import VertexAIEmbeddings
from langchain.retrievers.multi_query import MultiQueryRetriever
from langchain.retrievers import BM25Retriever, EnsembleRetriever
from langchain_core.prompts.prompt import PromptTemplate
from typing import List, Optional, Sequence
import logging
import os
import datetime
import asyncio
import tempfile
import shutil
from config import settings

logger = logging.getLogger(__name__)

# Template for generating multiple search queries from a single user query
DEFAULT_QUERY_PROMPT = PromptTemplate(
    input_variables=["question"],
    template="""You are an AI language model assistant. Your task is 
    to break the given user question into 5 simple questions to retrieve 
    relevant documents from a vector  database. Make sure that the generated 5 questions cover all information 
    in the main user question. Do not leave out any info from the original question.
    You can club togeather relevant terms or sub queries.
    By generating multiple perspectives on the user question, 
    your goal is to help the user overcome some of the limitations 
    of distance-based similarity search. Provide these alternative 
    questions separated by newlines. Original question: {question}
    """,
)

async def retrieve_documents(query: str, db, k=20) -> List:
    """Retrieve documents from vector database with similarity scores.
    
    Args:
        query: Search query string
        db: Vector database instance
        k: Number of documents to retrieve (default: 20)
        
    Returns:
        List of documents with similarity scores in metadata
        
    Features:
        - Similarity search with scores
        - Score metadata addition
        - Async execution
        - Detailed logging
    """
    logging.info(f"Retrieving documents for query: {query} with k={k}")
    docs_and_scores = await asyncio.to_thread(db.similarity_search_with_score, query, k=k)
    docs, scores = zip(*docs_and_scores)
    logging.info(f"Retrieved {len(docs)} documents with scores")
    for doc, score in zip(docs, scores):
        doc.metadata["score"] = score
        logging.debug(f"Document score: {score}, metadata: {doc.metadata}")
    return docs

async def _unique_documents(documents: Sequence) -> List:
    """Remove duplicate documents while preserving order.
    
    Args:
        documents: Sequence of documents to deduplicate
        
    Returns:
        List of unique documents in original order
        
    Features:
        - Order preservation
        - Async execution
        - Logging of deduplication stats
    """
    logging.info(f"Deduplicating {len(documents)} documents")
    unique_docs = await asyncio.to_thread(
        lambda: [doc for i, doc in enumerate(documents) if doc not in documents[:i]]
    )
    logging.info(f"After deduplication: {len(unique_docs)} documents")
    return unique_docs

async def get_embeddings(choice='geko'):
    """Get embeddings model instance.
    
    Args:
        choice: Embeddings model choice (default: 'geko')
        
    Returns:
        VertexAIEmbeddings instance
        
    Features:
        - Model selection
        - Async initialization
        - Logging
    """
    logging.info(f"Getting embeddings model with choice: {choice}")
    if choice=='geko':
        embeddings = await asyncio.to_thread(
            lambda: VertexAIEmbeddings(model="text-embedding-004")
        )
        logging.info("Using text-embedding-004 model")
    return embeddings

async def create_vector_db(docs, embeddings, project_name=None):
    """Create a new FAISS vector database.
    
    Args:
        docs: List of documents to index
        embeddings: Embeddings model instance
        project_name: Optional project name for logging
        
    Returns:
        FAISS vector database instance
        
    Features:
        - Document indexing
        - Metadata logging
        - Async creation
        - Error handling
    """
    logging.info(f"Creating vector database with {len(docs)} documents")
    if project_name:
        logging.info(f"Project name: {project_name}")
    
    # Log document details
    for doc in docs:
        source = doc.metadata.get('source', 'unknown')
        page = doc.metadata.get('page', 'unknown')
        content_len = len(doc.page_content)
        logging.debug(f"Processing document: source={source}, page={page}, content_length={content_len}")
    
    try:
        db = await asyncio.to_thread(FAISS.from_documents, docs, embeddings)
        logging.info("Vector database created successfully")
        return db
    except Exception as e:
        logging.error(f"Error creating vector database: {str(e)}")
        raise

async def save_vector_db(vector_db, gcs_path: str):
    """Save vector database to Google Cloud Storage.
    
    Args:
        vector_db: FAISS vector database instance
        gcs_path: Path in GCS bucket for storage
        
    Features:
        - Temporary file management
        - GCS integration
        - Async file operations
        - Error handling
        - Detailed logging
    """
    try:
        logger.info(f"Saving vector DB to GCS path: {gcs_path}")
        
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = os.path.join(temp_dir, "temp_vectordb")
            
            # Save to temporary local path first
            await asyncio.to_thread(vector_db.save_local, temp_path)
            
            # Get bucket reference
            from google.cloud import storage
            client = storage.Client()
            bucket = client.bucket(settings.BUCKET_NAME)
            
            # Upload index.faiss file
            index_blob = bucket.blob(f"{gcs_path}/index.faiss")
            await asyncio.to_thread(index_blob.upload_from_filename, os.path.join(temp_path, "index.faiss"))
            
            # Upload index.pkl file
            pkl_blob = bucket.blob(f"{gcs_path}/index.pkl")
            await asyncio.to_thread(pkl_blob.upload_from_filename, os.path.join(temp_path, "index.pkl"))
            
        logger.info("Successfully saved vector DB to GCS")
        
    except Exception as e:
        logger.error(f"Error saving vector DB to GCS: {str(e)}")
        raise

async def load_vector_db(gcs_path: str):
    """Load vector database from Google Cloud Storage.
    
    Args:
        gcs_path: Path in GCS bucket where database is stored
        
    Returns:
        FAISS vector database instance
        
    Features:
        - GCS integration
        - Temporary file management
        - Embeddings initialization
        - Async operations
        - Error handling
    """
    try:
        logger.info(f"Loading vector database from GCS path: {gcs_path}")
        
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Get bucket reference
            from google.cloud import storage
            client = storage.Client()
            bucket = client.bucket(settings.BUCKET_NAME)
            
            # Download index.faiss file
            index_blob = bucket.blob(f"{gcs_path}/index.faiss")
            index_path = os.path.join(temp_dir, "index.faiss")
            await asyncio.to_thread(index_blob.download_to_filename, index_path)
            
            # Download index.pkl file
            pkl_blob = bucket.blob(f"{gcs_path}/index.pkl")
            pkl_path = os.path.join(temp_dir, "index.pkl")
            await asyncio.to_thread(pkl_blob.download_to_filename, pkl_path)
            
            # Get embeddings
            embeddings = await get_embeddings()
            
            # Load from temporary path
            db = await asyncio.to_thread(
                FAISS.load_local,
                temp_dir,
                embeddings,
                allow_dangerous_deserialization=True
            )
            
        logger.info("Vector database loaded successfully from GCS")
        return db
        
    except Exception as e:
        logger.error(f"Error loading vector database from GCS: {str(e)}")
        raise Exception(f"Failed to load vector database from GCS: {str(e)}")

async def search(query, db, k=20):
    """Perform basic similarity search on vector database.
    
    Args:
        query: Search query string
        db: Vector database instance
        k: Number of results to return (default: 20)
        
    Returns:
        List of similar documents
        
    Features:
        - Simple similarity search
        - Async execution
        - Result count control
        - Logging
    """
    logging.info(f"Performing similarity search for query: {query} with k={k}")
    res = await asyncio.to_thread(db.similarity_search, query, k)
    logging.info(f"Found {len(res)} results")
    return res

async def hybrid_search(retriever_vectordb, text_splits):
    """Create hybrid search retriever combining vector and keyword search.
    
    Args:
        retriever_vectordb: Vector database retriever
        text_splits: Text chunks for BM25 retriever
        
    Returns:
        EnsembleRetriever combining vector and keyword search
        
    Features:
        - Combined search methods
        - Equal weight distribution
        - BM25 keyword search
        - Vector similarity search
    """
    logging.info("Setting up hybrid search")
    keyword_retriever = await asyncio.to_thread(BM25Retriever.from_documents, text_splits)
    keyword_retriever.k = 5
    logging.info("Created BM25 retriever")
    
    ensemble_retriever = EnsembleRetriever(
        retrievers=[retriever_vectordb, keyword_retriever],
        weights=[0.5, 0.5]
    )
    logging.info("Created ensemble retriever with equal weights")
    return ensemble_retriever

async def retrieve(query, db, mode='default', k=20, llm=None, hybrid=False, text_chunks=None, sort=True):
    """Retrieve documents using configurable search strategies.
    
    Args:
        query: Search query string
        db: Vector database instance
        mode: Search mode ('default', 'advanced', 'advanced-pro')
        k: Number of results to return
        llm: Language model for advanced retrieval
        hybrid: Whether to use hybrid search
        text_chunks: Text chunks for hybrid search
        sort: Whether to sort results by page number
        
    Returns:
        List of retrieved documents
        
    Features:
        - Multiple retrieval modes
        - LLM integration for advanced search
        - Hybrid search support
        - Result sorting
        - Detailed logging
        - Error handling
    """
    logging.info(f"Retrieving documents with mode={mode}, k={k}, hybrid={hybrid}")
    logging.info(f"Query: {query}")
    
    if mode.lower()=='advanced' and llm is not None:
        logging.info("Using advanced retrieval with MultiQueryRetriever")
        retriever = MultiQueryRetriever.from_llm(
            retriever=db.as_retriever(search_kwargs={"k": k}), llm=llm, include_original=True
        )
    elif mode.lower()=='advanced-pro' and llm is not None:
        logging.info("Using advanced-pro retrieval with custom prompt")
        retriever = MultiQueryRetriever.from_llm(
            retriever=db.as_retriever(search_kwargs={"k": k}), llm=llm, prompt=DEFAULT_QUERY_PROMPT
        ) 
    else:
        logging.info("Using default retriever")
        retriever=db.as_retriever(search_kwargs={"k": k})
    
    if hybrid:
        if text_chunks is not None:
            logging.info("Using hybrid search with text chunks")
            retriever=hybrid_search(retriever, text_chunks)
        else:
            logging.warning("Text not available. Falling back to main retriever")
    
    # Run retrieval in thread pool
    logging.info("Executing retrieval")
    docs = await asyncio.to_thread(retriever.invoke, query)
    logging.info(f"Retrieved {len(docs)} documents")
    
    if sort:
        try:
            docs = sorted(docs, key=lambda doc: doc.metadata.get('page', 0))
            logging.info("Documents sorted by page number")
        except Exception as e:
            logging.warning(f"Could not sort documents by page: {e}. Returning unsorted.")
    
    # Log retrieved document details
    for i, doc in enumerate(docs):
        source = doc.metadata.get('source', 'unknown')
        page = doc.metadata.get('page', 'unknown')
        score = doc.metadata.get('score', 'N/A')
        content_len = len(doc.page_content)
        logging.debug(f"Retrieved doc {i+1}: source={source}, page={page}, score={score}, content_length={content_len}")
    
    final_docs = docs[:k]
    logging.info(f"Returning {len(final_docs)} documents")
    return final_docs

async def retrieve_multiples(queries, db, k=20, hybrid=False, text_chunks=None, sort=True):
    """Retrieve documents using multiple queries.
    
    Args:
        queries: List of search queries
        db: Vector database instance
        k: Number of results per query
        hybrid: Whether to use hybrid search
        text_chunks: Text chunks for hybrid search
        sort: Whether to sort results by page number
        
    Returns:
        List of unique documents from all queries
        
    Features:
        - Multi-query retrieval
        - Deduplication
        - Hybrid search support
        - Result sorting
        - Query validation
        - Error handling
    """
    logging.info(f"Retrieving multiple queries: {queries}")
    
    docs = []
    if isinstance(queries, list):
        retriever = db.as_retriever(search_kwargs={"k": k})
        logging.info(f"Created retriever with k={k}")
        
        try:
            for query in queries:
                logging.info(f"Processing query: {query}")
                query = query.strip()
                
                if hybrid:
                    if text_chunks is not None:
                        logging.info("Using hybrid search for query")
                        retriever = await hybrid_search(retriever, text_chunks)
                    else:
                        logging.warning("Text not available. Falling back to main retriever")
                
                if len(query) > 3:
                    # Run retrieval in thread pool
                    logging.info(f"Executing retrieval for query: {query}")
                    query_docs = await asyncio.to_thread(retriever.invoke, query)
                    logging.info(f"Retrieved {len(query_docs)} documents for query")
                    docs.extend(query_docs)
                else:
                    logging.warning(f"Skipping query as length <= 3: {query}")
            
        except Exception as e:
            logging.error(f"Error getting documents: {e}")
    else:
        logging.warning("Queries parameter is not a list")
    
    documents = await _unique_documents(docs)
    logging.info(f"Total unique documents after all queries: {len(documents)}")
    
    if sort:
        try:
            documents = sorted(documents, key=lambda doc: doc.metadata.get('page', 0))
            logging.info("Documents sorted by page number")
        except Exception as e:
            logging.warning(f"Could not sort documents by page: {e}. Returning unsorted.")
    
    # Log retrieved document details
    for i, doc in enumerate(documents):
        source = doc.metadata.get('source', 'unknown')
        page = doc.metadata.get('page', 'unknown')
        score = doc.metadata.get('score', 'N/A')
        content_len = len(doc.page_content)
        logging.debug(f"Final doc {i+1}: source={source}, page={page}, score={score}, content_length={content_len}")
    
    logging.info(f"Returning {len(documents)} documents")
    return documents

async def update_document_tags_in_vectordb(vector_db, file_path: str, new_tags: List[str]) -> bool:
    """Update tags for a document in the vector database.
    
    Args:
        vector_db: The vector database instance
        file_path: Path of the file whose tags need updating
        new_tags: New list of tags to apply
        
    Returns:
        bool: True if update successful, False otherwise
    """
    try:
        # Get documents that match the file path
        matching_docs = {
            idx: doc for idx, doc in vector_db.docstore._dict.items()
            if doc.metadata.get('source') == file_path
        }
        
        if not matching_docs:
            logging.warning(f"No documents found for {file_path} in vector DB")
            return False
            
        # Update tags for all matching documents
        for doc in matching_docs.values():
            doc.metadata['tags'] = new_tags
        
        logging.info(f"Updated tags for {len(matching_docs)} chunks of {file_path}")
        return True
    except Exception as e:
        logging.error(f"Error updating tags in vector DB: {str(e)}")
        return False

async def remove_document_from_vectordb(vector_db, file_path: str) -> bool:
    """Remove a document and its chunks from the vector database.
    
    Args:
        vector_db: The vector database instance
        file_path: Path of the file to remove
        
    Returns:
        bool: True if removal successful, False otherwise
    """
    try:
        # Get documents that match the file path
        matching_docs = {
            idx: doc for idx, doc in vector_db.docstore._dict.items()
            if doc.metadata.get('source') == file_path
        }
        
        if not matching_docs:
            logging.warning(f"No documents found for {file_path} in vector DB")
            return False
            
        # Remove documents and their vectors
        for idx in sorted(matching_docs.keys(), reverse=True):
            del vector_db.docstore._dict[idx]
            vector_db.index.remove_ids([idx])
        
        logging.info(f"Removed {len(matching_docs)} chunks for {file_path}")
        return True
    except Exception as e:
        logging.error(f"Error removing document from vector DB: {str(e)}")
        return False