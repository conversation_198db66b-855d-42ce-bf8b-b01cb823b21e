from crewai import Crew
from Agents_and_Tasks import *
import logging

logger = logging.getLogger(__name__)


def run_proposal_info_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[proposal_info_extraction_agent],
        tasks=[proposal_info_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            logging.info("----------------TOKEN USAGE--------------------------------------")
            logging.info(f"Token usage of the crew is: {result.token_usage}")
            logging.info("----------------TOKEN USAGE--------------------------------------")
            return str(result), result.token_usage
        else:
            return "No proposal info found in the document.", None
    except Exception as e:
        logging.error(f"Error in proposal info extraction: {str(e)}")
        return "Error extracting proposal info.", None

def run_background_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[background_extraction_agent],
        tasks=[background_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No background information found in the document.", None
    except Exception as e:
        logging.error(f"Error in background extraction: {str(e)}")
        return "Error extracting background information.", None

def run_scope_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[scope_extraction_agent],
        tasks=[scope_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No scope information found in the document.", None
    except Exception as e:
        logging.error(f"Error in scope extraction: {str(e)}")
        return "Error extracting scope information.", None

def run_task_areas_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[task_extraction_agent],
        tasks=[task_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No task areas found in the document.", None
    except Exception as e:
        logging.error(f"Error in task areas extraction: {str(e)}")
        return "Error extracting task areas.", None

def run_submission_instructions_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[submission_extraction_agent],
        tasks=[submission_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No submission instructions found in the document.", None
    except Exception as e:
        logging.error(f"Error in submission instructions extraction: {str(e)}")
        return "Error extracting submission instructions.", None

def run_evaluation_factors_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[evaluation_extraction_agent],
        tasks=[evaluation_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No evaluation factors found in the document.", None
    except Exception as e:
        logging.error(f"Error in evaluation factors extraction: {str(e)}")
        return "Error extracting evaluation factors.", None

def run_list_of_attachments_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[attachment_extraction_agent],
        tasks=[attachment_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No attachments found in the document.", None
    except Exception as e:
        logging.error(f"Error in attachments extraction: {str(e)}")
        return "Error extracting attachments.", None

def run_key_personnel_extraction_agent(content, user_guidelines):
    crew = Crew(
        agents=[key_personnel_extraction_agent],
        tasks=[key_personnel_extraction_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No key personnel information found in the document.", None
    except Exception as e:
        logging.error(f"Error in key personnel extraction: {str(e)}")
        return "Error extracting key personnel information.", None

def run_proposal_info_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[proposal_info_drafting_agent],
        tasks=[proposal_info_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No proposal info draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in proposal info drafting: {str(e)}")
        return "Error drafting proposal info.", None

def run_background_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[background_summary_drafting_agent],
        tasks=[background_summary_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No background draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in background drafting: {str(e)}")
        return "Error drafting background.", None

def run_scope_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[scope_summary_drafting_agent],
        tasks=[scope_summary_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No scope draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in scope drafting: {str(e)}")
        return "Error drafting scope.", None

def run_task_areas_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[task_area_summary_drafting_agent],
        tasks=[task_area_summary_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No task areas draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in task areas drafting: {str(e)}")
        return "Error drafting task areas.", None

def run_submission_instructions_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[submission_summary_drafting_agent],
        tasks=[submission_summary_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No submission instructions draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in submission instructions drafting: {str(e)}")
        return "Error drafting submission instructions.", None

def run_evaluation_factors_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[evaluation_summary_drafting_agent],
        tasks=[evaluation_summary_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No evaluation factors draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in evaluation factors drafting: {str(e)}")
        return "Error drafting evaluation factors.", None

def run_list_of_attachments_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[attachments_summary_drafting_agent],
        tasks=[attachments_summary_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No attachments draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in attachments drafting: {str(e)}")
        return "Error drafting attachments.", None

def run_key_personnel_drafting_agent(content, user_guidelines):
    crew = Crew(
        agents=[key_personnel_summary_drafting_agent],
        tasks=[key_personnel_summary_drafting_task],
        verbose=False
    )

    try:
        result = crew.kickoff(inputs={"content": content, "user_guidelines": user_guidelines})
        if result and hasattr(result, 'token_usage'):
            return str(result), result.token_usage
        else:
            return "No key personnel draft could be generated.", None
    except Exception as e:
        logging.error(f"Error in key personnel drafting: {str(e)}")
        return "Error drafting key personnel.", None


