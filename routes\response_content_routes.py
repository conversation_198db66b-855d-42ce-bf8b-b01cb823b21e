"""Response content generation and management routes module.

This module provides FastAPI routes for handling response content generation including:
- Fetching available outlines for response generation
- Generating response content from different outline types
- Retrieving existing response content
All endpoints handle proper authentication, logging, and storage operations.

Available Endpoints:
1. GET /projects/{project_id}/response/outlines
   - Fetch all available outlines (system, user_uploaded)

2. POST /projects/{project_id}/response/generate
   - Generate response content using specified outline_type
   - Request requires outline_type and optional data_library_files

3. GET /projects/{project_id}/response-content
   - Retrieve current response content for the project
   - Returns content, token counts, outline_type, and data_library_files

Schema Definitions:
- outline_type: system_generated, user_uploaded, or custom_outline
- custom_outline: Custom outline text (required when outline_type is "custom_outline")
- data_library_files: Array of strings (currently dummy implementation)
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, File, UploadFile, Form, Query
import logging
import json
import pickle
from typing import List, Dict, Optional
from pydantic import BaseModel
from google.cloud.exceptions import NotFound
from utils.auth import get_current_active_user, User
from project_manager import ProjectManager
from utils import strip_markdown_delimiters
from utils.constants import HTTPStatus, StoragePaths, DocumentTags
from utils.database import projects_collection
from schemas import (
    ResponseContentOutline,
    OutlinesResponse,
    ResponseContent,
    ResponseGenerationInput,
    ResponseGenerationRequest
)
from response_content_generation import (
    generate_response_from_outline,
    generate_response_content_with_breakdown,
    get_available_outlines
)
import asyncio


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get(
    "/projects/{project_id}/response/outlines",
    response_model=OutlinesResponse,
    summary="Get Available Outlines",
    description="Fetch available outlines for response generation with optional filtering by outline type",
    responses={
        200: {
            "description": "Successfully retrieved available outlines",
            "content": {
                "application/json": {
                    "example": {
                        "outlines": [
                            {
                                "name": "System Generated Outline",
                                "type": "system_generated",
                                "content": "# Project Overview\n\n## Section 1\nContent here..."
                            },
                            {
                                "name": "User Uploaded Outline",
                                "type": "user_uploaded",
                                "content": "# Custom Outline\n\n## Requirements\nUser requirements..."
                            }
                        ]
                    }
                }
            }
        },
        400: {
            "description": "Invalid outline_type parameter",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Invalid outline_type. Must be one of: system_generated, user_uploaded"
                    }
                }
            }
        },
        403: {
            "description": "Unauthorized access to project",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "You don't have permission to access this project"
                    }
                }
            }
        },
        404: {
            "description": "Project not found or requested outline type not available",
            "content": {
                "application/json": {
                    "examples": {
                        "project_not_found": {
                            "summary": "Project not found",
                            "value": {"detail": "Project not found"}
                        },
                        "system_outline_missing": {
                            "summary": "System outline not available",
                            "value": {"detail": "No system generated outline present, please generate the outline first."}
                        },
                        "user_outline_missing": {
                            "summary": "User outline not available",
                            "value": {"detail": "No file available with outline tag."}
                        }
                    }
                }
            }
        }
    }
)
async def get_project_outlines(
    project_id: str,
    outline_type: Optional[str] = Query(
        None,
        description="Filter by specific outline type",
        enum=["system_generated", "user_uploaded"],
        example="system_generated"
    ),
    current_user: User = Depends(get_current_active_user)
):
    """Fetch available outlines for response generation.

    Returns only outlines that are actually present and have content.
    Does not return empty or missing outlines.

    Args:
        project_id (str): The ID of the project
        outline_type (str, optional): Filter by specific outline type ("system_generated" or "user_uploaded")
        current_user (User): The authenticated user making the request

    Returns:
        dict: List of available outlines with metadata including name, type, and content

    Query Parameters:
        - outline_type: Optional filter to return only specific outline type
          * "system_generated": Returns only system-generated outline (if available)
          * "user_uploaded": Returns only user-uploaded outline (if available)
          * If not provided: Returns all available outlines

    Outline Types (only returned if present):
        - system_generated: System-generated outline from AI (if generated and has content)
        - user_uploaded: Combined content from all files tagged with "Outline" (if files exist)

    Response format:
        {
            "outlines": [
                {
                    "name": "System Generated Outline",
                    "type": "system_generated",
                    "content": "actual outline content..."
                },
                {
                    "name": "User Uploaded Outline",
                    "type": "user_uploaded",
                    "content": "combined content from all outline-tagged files..."
                }
            ]
        }

    Note:
        - If no outlines are available, returns empty array: {"outlines": []}
        - User-uploaded outline combines content from ALL files tagged with "Outline"
        - Only outlines with actual content are included in the response

    Error Responses:
        - 404 Not Found: When requested outline_type is not available
          * "No system generated outline present, please generate the outline first"
          * "No file available with outline tag"

    Raises:
        HTTPException: 400 for invalid outline_type parameter
                      403 for unauthorized access
                      404 if project not found or requested outline type not available
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} requesting available outlines for project {project_id}")

    try:
        # Validate outline_type parameter if provided
        if outline_type and outline_type not in ["system_generated", "user_uploaded"]:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail=f"Invalid outline_type. Must be one of: system_generated, user_uploaded"
            )

        project = await ProjectManager.load_project(project_id)
        if not project:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

        # Check if the current user owns the project
        if project.project_data.get('user_id') != str(current_user.id):
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

        # Use the utility function to get available outlines
        outlines = await get_available_outlines(project_id, project, projects_collection)

        # Filter outlines based on outline_type parameter if provided
        if outline_type:
            # Filter outlines to only include the requested type
            filtered_outlines = [outline for outline in outlines if outline.type == outline_type]

            # If no outline of the requested type is found, return appropriate error
            if not filtered_outlines:
                if outline_type == "system_generated":
                    raise HTTPException(
                        status_code=HTTPStatus.NOT_FOUND,
                        detail="No system generated outline present, please generate the outline first."
                    )
                elif outline_type == "user_uploaded":
                    raise HTTPException(
                        status_code=HTTPStatus.NOT_FOUND,
                        detail="No file available with outline tag."
                    )

            outlines = filtered_outlines

        # Update project visit timestamp
        await project.update_timestamps(update_content=False, update_visit=True)

        logger.info(f"Retrieved {len(outlines)} outlines for project {project_id}")
        return {"outlines": outlines}

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error retrieving outlines for project {project_id}: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=str(e))



@router.get("/projects/{project_id}/response/data-library")
async def get_data_library_files(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Retrieve files available in the data library.

    Args:
        project_id (str): The ID of the project
        current_user (User): The authenticated user making the request

    Returns:
        dict: List of files with metadata

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project not found
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} requesting data library files for project {project_id}")

    try:
        project = await ProjectManager.load_project(project_id)
        if not project:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

        # Check if the current user owns the project
        if project.project_data.get('user_id') != str(current_user.id):
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

        # Return dummy data structure
        base_data = [
            {
                "id": "1",
                "name": "Marketing Outline Response",
                "type": "folder",
                "children": [
                    {
                        "id": "1-1",
                        "name": "Campaign Document",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                    {
                        "id": "1-2",
                        "name": "Strategy Document",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                ],
                "isSelected": False,
                "isIndeterminate": False,
                "parentPath": "",
            },
            {
                "id": "2",
                "name": "Sales Outline Response",
                "type": "folder",
                "children": [
                    {
                        "id": "2-1",
                        "name": "Proposal Document",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                ],
                "isSelected": False,
                "isIndeterminate": False,
                "parentPath": "",
            },
            {
                "id": "3",
                "name": "Technical Outline Response",
                "type": "folder",
                "children": [
                    {
                        "id": "3-1",
                        "name": "Architecture Document",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                    {
                        "id": "3-2",
                        "name": "Implementation Guide",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                ],
                "isSelected": False,
                "isIndeterminate": False,
                "parentPath": "",
            },
            {
                "id": "4",
                "name": "Legal Outline Response",
                "type": "folder",
                "children": [
                    {
                        "id": "4-1",
                        "name": "Compliance Document",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                ],
                "isSelected": False,
                "isIndeterminate": False,
                "parentPath": "",
            },
            {
                "id": "5",
                "name": "Financial Outline Response",
                "type": "folder",
                "children": [
                    {
                        "id": "5-1",
                        "name": "Budget Document",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                    {
                        "id": "5-2",
                        "name": "Cost Analysis",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                ],
                "isSelected": False,
                "isIndeterminate": False,
                "parentPath": "",
            },
            {
                "id": "6",
                "name": "Project Outline Response",
                "type": "folder",
                "children": [
                    {
                        "id": "6-1",
                        "name": "Timeline Document",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                ],
                "isSelected": False,
                "isIndeterminate": False,
                "parentPath": "",
            },
            {
                "id": "7",
                "name": "Xyz Outline Response",
                "type": "folder",
                "children": [
                    {
                        "id": "7-1",
                        "name": "Old Outline Response",
                        "type": "file",
                        "children": [],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                    {
                        "id": "7-2",
                        "name": "Xyz Nested Response",
                        "type": "folder",
                        "children": [
                            {
                                "id": "7-2-1",
                                "name": "Xyz Deep Response",
                                "type": "folder",
                                "children": [
                                    {
                                        "id": "7-2-1-1",
                                        "name": "Xyz company Response document",
                                        "type": "file",
                                        "children": [],
                                        "isSelected": False,
                                        "isIndeterminate": False,
                                        "parentPath": "",
                                    },
                                    {
                                        "id": "7-2-1-2",
                                        "name": "Xyz New company Response document",
                                        "type": "file",
                                        "children": [],
                                        "isSelected": False,
                                        "isIndeterminate": False,
                                        "parentPath": "",
                                    },
                                    {
                                        "id": "7-2-1-3",
                                        "name": "Xyz Old company Response document",
                                        "type": "file",
                                        "children": [],
                                        "isSelected": False,
                                        "isIndeterminate": False,
                                        "parentPath": "",
                                    },
                                ],
                                "isSelected": False,
                                "isIndeterminate": False,
                                "parentPath": "",
                            },
                        ],
                        "isSelected": False,
                        "isIndeterminate": False,
                        "parentPath": "",
                    },
                ],
                "isSelected": False,
                "isIndeterminate": False,
                "parentPath": "",
            },
        ]

        # Update project visit timestamp
        await project.update_timestamps(update_content=False, update_visit=True)

        logger.info(f"Retrieved {len(base_data)} data library files for project {project_id}")
        return {"files": base_data}

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error retrieving data library files for project {project_id}: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/projects/{project_id}/response-content")
async def get_response_content(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Retrieve the current response content for a project.

    Args:
        project_id (str): The ID of the project
        current_user (User): The authenticated user making the request

    Returns:
        dict: The response content with metadata

    Response includes:
        - content: The actual generated response text content
        - input_tokens: Number of tokens used as input to the LLM
        - output_tokens: Number of tokens generated by the LLM
        - outline_type: Type of outline used (system_generated or user_uploaded)
        - data_library_files: List of data library files used (currently empty for dummy implementation)

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or response content not found
                      500 for other errors
    """
    logger.info(f"User {current_user.full_name} requesting response content for project {project_id}")

    try:
        project = await ProjectManager.load_project(project_id)
        if not project:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

        # Check user permission
        if project.project_data.get('user_id') != str(current_user.id):
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

        # Get the response content from storage
        response_content_path = f"{project.project_prefix}{StoragePaths.RESPONSE_CONTENT_FILE}"
        response_content_blob = project.bucket.blob(response_content_path)

        try:
            loop = asyncio.get_event_loop()
            response_content_text = await loop.run_in_executor(None, response_content_blob.download_as_text)
            response_content_data = json.loads(response_content_text)
        except NotFound:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND,
                detail="Response content not found. Please generate response content first."
            )

        # Update project visit timestamp
        await project.update_timestamps(update_content=False, update_visit=True)

        logger.info(f"Retrieved response content for project {project_id}")
        return response_content_data

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error retrieving response content for project {project_id}: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=str(e))


def format_response_content(response_data: dict) -> dict:
    """Format response content data consistently for API responses.

    This ensures that both newly generated content and existing content
    return the same response structure, preventing API inconsistencies.

    Args:
        response_data: The response data dictionary from storage or generation

    Returns:
        dict: Consistently formatted response with only the required fields
    """
    return {
        "content": response_data["content"],
        "input_tokens": response_data["input_tokens"],
        "output_tokens": response_data["output_tokens"],
        "outline_type": response_data["outline_type"],
        "data_library_files": response_data["data_library_files"]
    }


@router.post("/projects/{project_id}/response/generate")
async def generate_response_content(
    project_id: str,
    response_request: ResponseGenerationRequest,
    regenerate: bool = False,
    current_user: User = Depends(get_current_active_user)
):
    """Generate or retrieve response content using the selected outline type and data files.

    Args:
        project_id (str): The ID of the project
        response_request (ResponseGenerationRequest): Request body containing outline_type and data_library_files
        regenerate (bool, optional): Whether to force regeneration of response content. Defaults to False.
        current_user (User): The authenticated user making the request

    Request Body:
        - outline_type: Type of outline to use (required) - system_generated, user_uploaded, or custom_outline
        - custom_outline: Custom outline text (required when outline_type is "custom_outline")
        - data_library_files: List of data library files (optional)

    Outline Types:
        - system_generated: Uses the AI-generated outline from the project
        - user_uploaded: Processes each uploaded file tagged with "Outline" individually, then combines responses
        - custom_outline: Uses the custom outline provided in custom_outline field

    Error Responses:
        - 400 Bad Request: Invalid outline_type or missing custom_outline when required
        - 404 Not Found: Selected outline type not available
          * "No system generated outline present, please generate the outline first"
          * "No file available with outline tag"

    Returns:
        dict: The generated response content with metadata

    Response includes:
        - content: The LLM-generated response text (cleaned of markdown delimiters)
        - input_tokens: Total accumulated tokens used as input to the LLM (across all generations)
        - output_tokens: Total accumulated tokens generated as output by the LLM (across all generations)
        - outline_type: Type of outline used for generation
        - data_library_files: List of data library files used

    Regeneration Behavior:
        - If regenerate=False (default) and response content exists with same parameters: Returns existing content
        - If regenerate=False but parameters differ (outline_type, custom_outline): Logs warning and regenerates with new parameters
        - If regenerate=True or no existing content: Generates new content
        - Token counts are accumulated across regenerations (like outline generation)

    Parameter Validation:
        - When regenerate=False and parameters differ from existing content: Logs warning and proceeds with regeneration
        - For custom_outline type: also checks if custom_outline content differs
        - Automatic regeneration ensures users get content with their requested parameters

    Raises:
        HTTPException: 400 if outline_type is invalid or outline content is empty
                      403 for unauthorized access
                      404 if project or specified outline not found
                      500 for generation failures
    """
    logger.info(f"User {current_user.full_name} requesting response generation for project {project_id}")

    try:
        project = await ProjectManager.load_project(project_id)
        if not project:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

        # Check user permission
        if project.project_data.get('user_id') != str(current_user.id):
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

        # Check if we need to regenerate the response content
        response_content_path = f"{project.project_prefix}{StoragePaths.RESPONSE_CONTENT_FILE}"
        response_content_blob = project.bucket.blob(response_content_path)
        loop = asyncio.get_event_loop()
        try:
            # Try to load existing response content from storage
            existing_response_text = await loop.run_in_executor(None, response_content_blob.download_as_text)
            existing_response_data = json.loads(existing_response_text)
            existing_content = existing_response_data.get("content", "")
            prev_input_tokens = existing_response_data.get("input_tokens", 0)
            prev_output_tokens = existing_response_data.get("output_tokens", 0)
        except NotFound:
            existing_content = ""
            prev_input_tokens = 0
            prev_output_tokens = 0

        logger.info(f"Previous token counts for input and output are: {prev_input_tokens} {prev_output_tokens} respectively.")

        if not regenerate and existing_content:
            # Check if request parameters match existing content
            outline_type_mismatch = existing_response_data.get("outline_type") != response_request.outline_type
            custom_outline_mismatch = False

            # For custom_outline type, also check if the outline content changed
            # Support backward compatibility with old response_prompt field name
            if response_request.outline_type == "custom_outline":
                existing_custom_outline = existing_response_data.get("custom_outline") or existing_response_data.get("response_prompt")
                custom_outline_mismatch = existing_custom_outline != response_request.custom_outline

            # If there's a mismatch, log warning and proceed with regeneration
            if outline_type_mismatch or custom_outline_mismatch:
                if outline_type_mismatch:
                    logger.warning(f"Response content exists with outline_type '{existing_response_data.get('outline_type')}' but request specifies '{response_request.outline_type}'. Regenerating with requested outline type.")
                if custom_outline_mismatch:
                    logger.warning(f"Response content exists with different custom_outline content. Regenerating with new custom outline.")
                # Continue to regeneration logic below instead of returning existing content
            else:
                # If not regenerating and content exists with matching parameters, return the existing response content
                logger.info(f"Using existing response content for project {project_id}")
                return format_response_content(existing_response_data)

        # Validate outline_type and custom_outline
        valid_outline_types = ["system_generated", "user_uploaded", "custom_outline"]
        if response_request.outline_type not in valid_outline_types:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail=f"Invalid outline_type. Must be one of: {', '.join(valid_outline_types)}"
            )

        if response_request.outline_type == "custom_outline" and not response_request.custom_outline:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="custom_outline is required when outline_type is 'custom_outline'"
            )

        # Get the outline content based on the outline_type
        outline_content = ""

        if response_request.outline_type == "system_generated":
            # Get system-generated outline from storage
            outline_blob = project.bucket.blob(f"{project.project_prefix}{StoragePaths.OUTLINE_FILE}")
            try:
                loop = asyncio.get_event_loop()
                outline_text = await loop.run_in_executor(None, outline_blob.download_as_text)
                outline_data = json.loads(outline_text)
                outline_content = strip_markdown_delimiters(outline_data.get("content", outline_data.get("outline", "")))  # Support both new and old key for backward compatibility
                logger.info(f"Retrieved system-generated outline for project {project_id}")
            except NotFound:
                raise HTTPException(
                    status_code=HTTPStatus.NOT_FOUND,
                    detail="No system generated outline present, please generate the outline first."
                )

        elif response_request.outline_type == "user_uploaded":
            # Check if user-uploaded outline files exist before proceeding
            project_doc = await projects_collection.find_one({"project_id": project_id})
            outline_files_found = False

            if project_doc and "files_metadata" in project_doc:
                for file_metadata in project_doc.get("files_metadata", []):
                    if DocumentTags.OUTLINE in file_metadata.get("tags", []):
                        outline_files_found = True
                        break

            if not outline_files_found:
                raise HTTPException(
                    status_code=HTTPStatus.NOT_FOUND,
                    detail="No file available with outline tag."
                )

            # For user_uploaded, we'll pass a placeholder since the function will handle multiple files internally
            outline_content = "user_uploaded_placeholder"  # This will be ignored by the function
            logger.info(f"Will process multiple user-uploaded outline files for project {project_id}")

        elif response_request.outline_type == "custom_outline":
            # Use the custom outline as outline content
            outline_content = response_request.custom_outline
            logger.info(f"Using custom outline for project {project_id}")

        if not outline_content:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="No outline content available for the selected outline type."
            )

        # Generate response content using the new breakdown approach
        logger.info(f"Generating response content for outline_type: {response_request.outline_type}")

        # Prepare input data for response generation
        generation_input = ResponseGenerationInput(
            outline_type=response_request.outline_type,
            outline_content=outline_content,
            data_library_files=response_request.data_library_files or [],
            project_id=project_id,
            model_name=project.model_name  # Use the project's configured model
        )

        # Generate the response content
        response_content = await generate_response_content_with_breakdown(generation_input)

        logger.info(f"Token counts for input and output for the new response content are: {response_content.input_tokens} {response_content.output_tokens} respectively.")

        # Create response data with accumulated token counts
        response_json = {
            "content": response_content.content,
            "input_tokens": response_content.input_tokens + prev_input_tokens,
            "output_tokens": response_content.output_tokens + prev_output_tokens,
            "outline_type": response_content.outline_type,
            "data_library_files": response_content.data_library_files
        }

        # Store custom_outline if it was used for comparison in future requests
        if response_request.outline_type == "custom_outline":
            response_json["custom_outline"] = response_request.custom_outline

        # Save the response content to GCP
        response_content_path = f"{project.project_prefix}{StoragePaths.RESPONSE_CONTENT_FILE}"
        response_content_blob = project.bucket.blob(response_content_path)

        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            None,
            lambda: response_content_blob.upload_from_string(
                json.dumps(response_json, indent=2),
                content_type="application/json"
            )
        )

        logger.info(f"Saved response content to GCP: {response_content_path}")

        # Update timestamps
        await project.update_timestamps(update_content=True, update_visit=True)

        logger.info(f"Successfully generated response content for project {project_id} using {response_request.outline_type} outline type")
        return format_response_content(response_json)

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error generating response content for project {project_id}: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=str(e))