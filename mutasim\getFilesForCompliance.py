import asyncio
import pickle
import json
import logging
from typing import Dict, Any, List, Optional

# Import required modules
from utils.database import projects_collection
from utils.constants import DocumentTags
from utils.llm_config import get_llm_instance
from utils import clean_json_code_block, count_tokens
from content_review import get_content_review_rulebook, get_rfp_specific_review_prompt

# Set up logger
logger = logging.getLogger(__name__)

async def generate_proposal_specific_criteria(project_manager) -> Dict[str, Any]:
    """Generate proposal-specific criteria using LLM based on Main Document content.

    Args:
        project_manager: ProjectManager instance for the project

    Returns:
        Dict[str, Any]: Generated criteria with content, input_tokens, and output_tokens

    Raises:
        Exception: If no Main Document found, content retrieval fails, or LLM generation fails
    """
    try:
        # Get project document metadata from MongoDB to find Main Document
        project_doc = await projects_collection.find_one({"project_id": project_manager.project_id})

        if not project_doc or "files_metadata" not in project_doc:
            raise Exception("No project document or files metadata found")

        # Step 1: Find Main Document (REQUIRED - minimum requirement)
        main_document = None
        for file_metadata in project_doc.get("files_metadata", []):
            if DocumentTags.MAIN_DOCUMENT in file_metadata.get("tags", []):
                main_document = {
                    "filename": file_metadata.get("filename"),
                    "tags": file_metadata.get("tags", [])
                }
                break

        if not main_document:
            logger.error("No Main Document found - cannot generate proposal criteria")
            logger.info("Main Document is required as the foundation for proposal-specific criteria generation")
            return {
                "content": [],
                "input_tokens": 0,
                "output_tokens": 0
            }

        logger.info(f"Found Main Document: {main_document['filename']}")

        # Step 2: Find supplementary documents (OPTIONAL but IMPORTANT)
        supplementary_tags = [
            # DocumentTags.TASK_AREA,
            DocumentTags.SUBMISSION_INSTRUCTIONS,
            DocumentTags.EVALUATION_FACTORS
        ]

        supplementary_files = {}
        for file_metadata in project_doc.get("files_metadata", []):
            file_tags = file_metadata.get("tags", [])
            for tag in supplementary_tags:
                if tag in file_tags:
                    supplementary_files[tag] = {
                        "filename": file_metadata.get("filename"),
                        "tags": file_tags
                    }

        # Step 3: Log warnings for missing important files and track found files
        missing_files = []
        found_supplementary = []

        for tag in supplementary_tags:
            if tag in supplementary_files:
                found_supplementary.append(f"{tag}: {supplementary_files[tag]['filename']}")
            else:
                missing_files.append(tag)

        if missing_files:
            logger.warning(f"Missing important documents: {', '.join(missing_files)} - criteria may be incomplete")
            logger.info("For comprehensive criteria generation, consider uploading documents with these tags")

        if found_supplementary:
            logger.info(f"Found supplementary documents: {'; '.join(found_supplementary)}")

        if not missing_files:
            logger.info("Found all required documents for comprehensive criteria generation")

        # Step 4: Prepare files to process (Main Document + available supplementary files)
        files_to_process = [main_document]
        files_to_process.extend(supplementary_files.values())

        logger.info(f"Processing {len(files_to_process)} files: {[f['filename'] for f in files_to_process]}")

        # Process all available files and combine their content
        combined_content = []
        processed_files = []

        for file_info in files_to_process:
            filename = file_info["filename"]
            try:
                # Get the pickled content for each relevant file
                pickled_data = await project_manager.get_pickled_content(filename)
                if not pickled_data:
                    logger.warning(f"No pickled content found for {filename}, skipping")
                    continue

                logger.info(f"Reading pickled content for {filename} (tags: {file_info['tags']})")

                # Unpickle the content and process using the same logic as get_outline_generation_context()
                sections = pickle.loads(pickled_data)
                processed_files.append(filename)

                # Process sections for this file
                file_content = []
                if isinstance(sections, list):
                    for section in sections:
                        if isinstance(section, dict):
                            # Handle different dictionary structures
                            if 'text' in section:
                                file_content.append(section['text'])
                            elif 'content' in section:
                                file_content.append(section['content'])
                            # Handle PDF page structure
                            elif 'page_number' in section and 'content' in section:
                                file_content.append(f"Page {section['page_number']}: {section['content']}")
                        elif isinstance(section, str):
                            file_content.append(section)
                # If sections is a string, add it directly
                elif isinstance(sections, str):
                    file_content.append(sections)
                # Handle ProjectDocument object
                elif hasattr(sections, 'sections') and isinstance(sections.sections, list):
                    for page in sections.sections:
                        if isinstance(page, dict) and 'content' in page:
                            file_content.append(page['content'])

                # Add this file's content to the combined content
                if file_content:
                    combined_content.extend(file_content)
                    logger.info(f"Added {len(file_content)} content sections from {filename}")

            except Exception as e:
                logger.error(f"Error processing pickled content for {filename}: {str(e)}")
                continue

        if not processed_files:
            logger.warning("No files were successfully processed, returning empty proposal criteria")
            return {
                "content": [],
                "input_tokens": 0,
                "output_tokens": 0
            }

        # Check if we have any valid content (same filtering as outline_manager)
        valid_content = []
        for content in combined_content:
            # Skip error messages from PDF extraction
            if not content.strip().startswith("Error: No content could be extracted from this page"):
                valid_content.append(content)

        if not valid_content:
            raise Exception(f"No content found in processed files: {processed_files}")

        # Join the content
        combined_content_text = "\n\n".join(valid_content)

        logger.info(f"Retrieved {len(combined_content_text)} characters of content from {len(processed_files)} files: {processed_files}")

        # Get proposal criteria from rulebook
        proposal_criterias = get_content_review_rulebook("proposal_criterias")

        # Generate prompt using the RFP-specific review prompt
        prompt = get_rfp_specific_review_prompt(combined_content_text, proposal_criterias)

        # Configure LLM settings for content review generation (same as outline generation)
        generation_config = {
            "max_output_tokens": 8000,  # Allow for detailed criteria
        }

        # Initialize the LLM with project's model and configuration (same as outline generation)
        llm = get_llm_instance(
            model_name=project_manager.model_name,
            custom_config=generation_config
        )

        if llm is None:
            raise ValueError("LLM must be provided to generate proposal criteria")

        logger.info("Generating proposal-specific criteria...")
        logger.info("Model Name: " + project_manager.model_name)

        # Count input tokens
        input_tokens = count_tokens(prompt)
        logger.info(f"The input token count for the proposal criteria is: {input_tokens}")

        try:
            # Run the LLM operation in a thread pool to prevent blocking (same as outline generation)
            loop = asyncio.get_event_loop()

            # Import the required classes for type checking
            from langchain_google_genai import GoogleGenerativeAI

            if isinstance(llm, GoogleGenerativeAI):
                # For experimental models, invoke and get the response directly
                response = await loop.run_in_executor(
                    None,
                    lambda: llm.invoke(prompt)
                )
                output_tokens = count_tokens(response)
                logger.info(f"The output token count of the proposal criteria is: {output_tokens}")
                response_content = response
            else:
                # For standard models, invoke and get the structured response
                response = await loop.run_in_executor(
                    None,
                    lambda: llm.invoke(prompt, generation_config=generation_config)
                )
                output_tokens = count_tokens(response.content)
                logger.info(f"The output token count of the proposal criteria is: {output_tokens}")
                response_content = response.content

        except Exception as e:
            logger.error(f"Error during LLM generation: {str(e)}")
            raise Exception(f"Failed to generate proposal criteria: {str(e)}")

        logger.info(f"Generated proposal-specific criteria. Input tokens: {input_tokens}, Output tokens: {output_tokens}")

        # Clean and parse the response content to extract the JSON array
        try:
            # Use the existing clean_json_code_block function for consistency
            from utils import clean_json_code_block
            parsed_criteria = clean_json_code_block(response_content)

            # Ensure it's a list
            if not isinstance(parsed_criteria, list):
                logger.warning("LLM response is not a list, using raw content")
                parsed_criteria = response_content

            logger.info(f"Successfully parsed {len(parsed_criteria) if isinstance(parsed_criteria, list) else 'raw'} proposal criteria")

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse LLM response as JSON: {str(e)}, using raw content")
            parsed_criteria = response_content
        except Exception as e:
            logger.warning(f"Error processing LLM response: {str(e)}, using raw content")
            parsed_criteria = response_content

        return {
            "content": parsed_criteria,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens
        }

    except Exception as e:
        logger.error(f"Error generating proposal-specific criteria: {str(e)}")
        raise Exception(f"Error generating proposal-specific criteria: {str(e)}")
