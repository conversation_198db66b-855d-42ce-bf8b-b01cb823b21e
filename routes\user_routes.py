"""User management routes module.

This module provides FastAPI routes for user account operations including:
- User registration with email verification
- Profile management and updates
- User listing and retrieval
- Password management
All endpoints handle proper authentication, logging, and email notifications.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import JSONResponse
from bson import ObjectId
import logging

from utils.auth import get_current_active_user, get_password_hash, User, create_verification_token
from utils.database import users_collection
from schemas import UserCreate, UserUpdate, UserDetails
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from config import settings
from typing import List
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)

async def send_verification_email(email: str, verification_link: str):
    """Send an email verification link to a newly registered user.
    
    Args:
        email (str): The recipient's email address
        verification_link (str): The verification link to be included in the email
        
    Note:
        Uses FastMail with configuration from settings to send the email
    """
    conf = ConnectionConfig(
        MAIL_USERNAME=settings.MAIL_USERNAME,
        MAIL_PASSWORD=settings.MAIL_PASSWORD,
        MAIL_FROM=settings.MAIL_FROM,
        MAIL_PORT=settings.MAIL_PORT,
        MAIL_SERVER=settings.MAIL_SERVER,
        MAIL_FROM_NAME=settings.MAIL_FROM_NAME,
        MAIL_STARTTLS=True,
        MAIL_SSL_TLS=False,
        USE_CREDENTIALS=True,
        VALIDATE_CERTS=True,
    )

    message = MessageSchema(
        subject="Verify Your Email",
        recipients=[email],
        body=f"Click the following link to verify your email: {verification_link}",
        subtype="html",
    )

    fm = FastMail(conf)
    await fm.send_message(message)

@router.get("/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    """Get the current authenticated user's profile.
    
    Args:
        current_user (User): The authenticated user making the request
        
    Returns:
        User: The current user's profile data
    """
    logger.info(f"User {current_user.full_name} accessed their profile")
    return current_user

@router.get("/users/all", response_model=List[UserDetails])
async def get_all_users(current_user: User = Depends(get_current_active_user)):
    """Get a list of all users with basic details.
    
    Args:
        current_user (User): The authenticated user making the request
        
    Returns:
        List[UserDetails]: List of all users with their email and full name
    """
    all_users_cursor = users_collection.find({}, {"email": 1, "full_name": 1})
    all_users = await all_users_cursor.to_list(length=None)

    user_details = [
        UserDetails(email=user["email"], full_name=user["full_name"]) for user in all_users
    ]

    logger.info(f"User {current_user.full_name} retrieved details of {len(user_details)} users")
    return user_details

@router.post("/users/", response_model=User, status_code=status.HTTP_201_CREATED)
async def create_user(user: UserCreate, background_tasks: BackgroundTasks):
    """Register a new user with email verification.
    
    Args:
        user (UserCreate): User registration data
        background_tasks (BackgroundTasks): FastAPI background tasks handler
        
    Returns:
        User: The created user object
        
    Raises:
        HTTPException: 400 if email or phone already registered
                      500 if user creation fails
    """
    logger.info(f"Attempting to create user with email: {user.email}")

    # Check for existing email
    if await users_collection.find_one({"email": user.email}):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered",
        )

    # Check for existing phone number
    if await users_collection.find_one({"phone_number": user.phone_number}):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Phone number already registered",
        )

    # Prepare user data for database
    hashed_password = get_password_hash(user.password)
    user_dict = user.dict()
    user_dict["hashed_password"] = hashed_password
    user_dict["is_verified"] = False
    user_dict["is_approved"] = False  # New users start as unapproved
    user_dict["is_admin"] = False     # New users are not admins by default
    user_dict["created_at"] = datetime.utcnow()  # Add creation timestamp
    del user_dict["password"]

    # Insert user into database
    result = await users_collection.insert_one(user_dict)

    if not result.inserted_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )

    # Send verification email in background
    verification_token = await create_verification_token(user.email)
    verification_link = f"{settings.APP_HOST}/verify-email?token={verification_token}"
    background_tasks.add_task(send_verification_email, user.email, verification_link)

    # Return created user data
    created_user = await users_collection.find_one({"_id": result.inserted_id})
    if created_user:
        created_user["id"] = str(created_user["_id"])
        del created_user["_id"]
        logger.info(f"User created successfully with ID: {result.inserted_id}")

        return User(**created_user)
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve created user",
        )

@router.put("/users/me", response_model=dict)
async def update_user(user_update: UserUpdate, current_user: User = Depends(get_current_active_user)):
    """Update the current user's profile information.
    
    Args:
        user_update (UserUpdate): Updated user data
        current_user (User): The authenticated user making the request
        
    Returns:
        dict: Success message and updated user data
        
    Raises:
        HTTPException: 400 for invalid data
                      404 if user not found
                      500 for update failures
    """
    logger.info(f"Update request received - Current name: '{current_user.full_name}', New name: '{user_update.full_name}'")

    try:
        # Verify user exists
        user = await users_collection.find_one({"_id": ObjectId(current_user.id)})
        if not user:
            logger.error(f"User not found in database: {current_user.id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        update_data = {}

        # Handle name update
        if user_update.full_name is not None:
            new_name = user_update.full_name.strip()
            current_name = user.get('full_name', '').strip()

            logger.info(f"Comparing names - Current: '{current_name}', New: '{new_name}'")

            if not new_name:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Full name cannot be empty"
                )

            if new_name != current_name:
                update_data["full_name"] = new_name
                logger.info(f"Name change detected: '{current_name}' -> '{new_name}'")

        # Handle password update
        if user_update.new_password:
            if len(user_update.new_password) < 6:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Password must be at least 6 characters long"
                )
            update_data["hashed_password"] = get_password_hash(user_update.new_password)
            logger.info("Password update requested")

        # Check if any updates are needed
        if not update_data:
            logger.info("No changes detected in update request")
            return {
                "message": "No changes detected in the update request",
                "user": current_user
            }

        # Update user in database
        logger.info(f"Attempting to update user with data: {update_data.keys()}")
        result = await users_collection.update_one(
            {"_id": ObjectId(current_user.id)},
            {"$set": update_data}
        )

        if result.modified_count == 0:
            logger.error("Database update failed - no documents modified")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user profile"
            )

        # Get updated user data
        updated_user = await users_collection.find_one({"_id": ObjectId(current_user.id)})
        if not updated_user:
            logger.error("Failed to fetch updated user data")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve updated user data"
            )

        updated_user["id"] = str(updated_user["_id"])
        del updated_user["_id"]

        updated_user_obj = User(**updated_user)

        logger.info(f"Update successful - New name: '{updated_user_obj.full_name}'")
        return {
            "message": "Profile updated successfully",
            "user": updated_user_obj
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Unexpected error during user update: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        ) 