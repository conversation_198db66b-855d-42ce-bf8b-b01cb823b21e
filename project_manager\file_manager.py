"""File management module for project management.

This module handles all file-related operations including:
- File upload and storage
- File renaming and deletion
- File metadata handling
- Pickled file operations
"""

import asyncio
import json
import logging
import pickle
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Any, Union

from fastapi import H<PERSON><PERSON>Exception
from google.cloud import storage
from google.cloud.exceptions import NotFound
from uuid import uuid4

from utils.database import projects_collection
from models import ProjectDocument
from project_manager.utils import get_time_ago
from utils.constants import StoragePaths, HTTPStatus, DocumentTags


class FileManager:
    """Manages file operations for projects.

    This class handles all aspects of file management including:
    - File upload and storage
    - File renaming and deletion
    - File metadata handling
    - Pickled file operations

    Attributes:
        project_id: Unique identifier for the project
        project_prefix: Project-specific path prefix in storage
        bucket: GCS bucket for project storage
    """

    def __init__(self, project_id: str, project_prefix: str, bucket: Any):
        """Initialize FileManager instance.

        Args:
            project_id: Unique identifier for the project
            project_prefix: Project-specific path prefix in storage
            bucket: GCS bucket for project storage
        """
        self.project_id = project_id
        self.project_prefix = project_prefix
        self.bucket = bucket

    async def save_uploaded_file(
        self,
        file_content: bytes,
        filename: str,
        tags: List[str] = None,
        sf_pages: List[int] = None,
        pickled_content: bytes = None,
        processed_doc: 'ProjectDocument' = None,
        create_vector_db: bool = False,
        update_vector_db_func = None
    ) -> str:
        """Save an uploaded file with all necessary processing.

        Args:
            file_content: Binary content of the file
            filename: Name of the file
            tags: Optional list of tags for the file
            sf_pages: Optional list of specially formatted pages
            pickled_content: Optional pre-processed pickled content
            processed_doc: Optional pre-processed ProjectDocument
            create_vector_db: Whether to update the vector database
            update_vector_db_func: Function to update vector database

        Returns:
            str: Generated file ID

        Features:
            - File content storage in GCS
            - Pickle file creation and storage
            - Vector DB updates
            - MongoDB metadata updates
        """
        try:
            # Generate unique file ID
            file_id = str(uuid.uuid4())

            # Save original file content
            file_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}{filename}")
            await asyncio.to_thread(
                lambda: file_blob.upload_from_string(file_content)
            )

            # Process document if not provided
            if not processed_doc:
                processed_doc = await ProjectDocument.create(
                    file_content=file_content,
                    file_name=filename,
                    tags=tags,
                    sf_pages=sf_pages
                )

            # Create and save pickle file for all document types
            if not pickled_content:
                pickled_content = pickle.dumps(processed_doc.sections)

            pickle_path = f"{self.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}{filename}.pickle"
            pickle_blob = self.bucket.blob(pickle_path)
            await asyncio.to_thread(
                lambda: pickle_blob.upload_from_string(pickled_content)
            )

            # Update vector DB only if enabled
            if create_vector_db and update_vector_db_func:
                await update_vector_db_func(processed_doc)
                logging.info(f"Vector DB updated for file {filename}")
            else:
                logging.info(f"Skipping vector DB update for file {filename} (disabled in settings)")

            # Use timezone-aware datetime
            current_time = datetime.now(timezone.utc)

            # Update MongoDB with file metadata
            file_info = {
                "file_id": file_id,
                "filename": filename,
                "tags": tags or [],
                "uploaded_at": current_time,
                "sf_pages": sf_pages or [],
                "pickle_path": pickle_path
            }

            # Update project files in MongoDB
            await projects_collection.update_one(
                {"project_id": self.project_id},
                {
                    "$push": {"files_metadata": file_info},
                    "$set": {"updated_at": current_time}
                }
            )

            logging.info(f"Successfully saved file '{filename}' (ID: {file_id}) with all processing steps completed")
            return file_id

        except Exception as e:
            logging.error(f"Error saving file {filename}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to save file: {str(e)}"
            )

    async def get_document_content(self, filename: str):
        """Retrieve document content, preferring pickled version if available.

        Args:
            filename: Name of the file to retrieve

        Returns:
            bytes or object: Document content, either raw or unpickled
        """
        pickle_filename = f"{filename}.pickle"
        pickle_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}{pickle_filename}")

        loop = asyncio.get_event_loop()
        try:
            if pickle_blob.exists():
                logging.info(f"Using pickled file for {filename}.")
                pickled_data = await loop.run_in_executor(None, pickle_blob.download_as_bytes)
                return pickle.loads(pickled_data)
            else:
                logging.warning(f"No pickled file found for {filename}. Defaulting to original file.")
                file_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}{filename}")
                file_bytes = await loop.run_in_executor(None, file_blob.download_as_bytes)
                return file_bytes
        except NotFound:
            logging.error(f"File '{filename}' or its pickled version not found.")
            return None
        except Exception as e:
            logging.error(f"Error retrieving content for {filename}: {str(e)}")
            return None

    async def list_uploaded_files(self):
        """List all uploaded files in the project.

        Returns:
            List[str]: List of filenames
        """
        files_prefix = f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}"
        loop = asyncio.get_event_loop()
        blobs = await loop.run_in_executor(None, lambda: list(self.bucket.list_blobs(prefix=files_prefix)))
        return [blob.name.split('/')[-1] for blob in blobs]

    async def get_uploaded_file(self, filename: str):
        """Get the raw content of an uploaded file.

        Args:
            filename: Name of the file to retrieve

        Returns:
            bytes: File content or None if not found
        """
        file_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}{filename}")
        loop = asyncio.get_event_loop()
        try:
            return await loop.run_in_executor(None, file_blob.download_as_bytes)
        except NotFound:
            logging.warning(f"File '{filename}' not found for project {self.project_id}")
            return None

    async def get_document_tags(self, file_id: str) -> List[str]:
        """Get tags for a document by file ID.

        Args:
            file_id: ID of the file

        Returns:
            List[str]: List of tags
        """
        project = await projects_collection.find_one({"project_id": self.project_id})
        if not project:
            return []

        file_metadata = next(
            (f for f in project.get('files_metadata', []) if f['file_id'] == file_id),
            None
        )
        return file_metadata.get('tags', []) if file_metadata else []

    async def update_document_tags(self, file_id: str, tags: List[str]):
        """Update tags for a document.

        Args:
            file_id: ID of the file
            tags: New list of tags
        """
        await projects_collection.update_one(
            {
                "project_id": self.project_id,
                "files_metadata.file_id": file_id
            },
            {
                "$set": {
                    "files_metadata.$.tags": tags
                }
            }
        )
        logging.info(f"Updated tags for file ID '{file_id}': {tags}")

    async def get_document_tags_by_filename(self, filename: str) -> List[str]:
        """Retrieve the tags for a document based on its filename.

        Args:
            filename: The name of the file to retrieve tags for

        Returns:
            List[str]: A list of tags associated with the file
        """
        project = await projects_collection.find_one({"project_id": self.project_id})
        if not project:
            logging.warning(f"Project {self.project_id} not found")
            return []

        file_metadata = next(
            (f for f in project.get('files_metadata', []) if f['filename'] == filename),
            None
        )
        if not file_metadata:
            logging.warning(f"File '{filename}' not found in project {self.project_id}")
            return []

        return file_metadata.get('tags', [])

    async def get_pickled_content(self, filename: str) -> Optional[bytes]:
        """Retrieve only the pickled content for a file.

        Args:
            filename: Name of the file to retrieve pickled content for

        Returns:
            Optional[bytes]: Pickled content if available, None otherwise
        """
        pickle_filename = f"{filename}.pickle"
        pickle_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}{pickle_filename}")

        loop = asyncio.get_event_loop()
        try:
            if await loop.run_in_executor(None, pickle_blob.exists):
                logging.info(f"Reading pickled content for {filename}")
                return await loop.run_in_executor(None, pickle_blob.download_as_bytes)
            else:
                logging.warning(f"No pickle file found for {filename}")
                return None
        except Exception as e:
            logging.error(f"Error reading pickle file for {filename}: {str(e)}")
            return None

    async def list_uploaded_files_with_metadata(self, skip: int = 0, limit: int = 10, sort_on: str = "uploaded_at", ascending: bool = False, all_files: bool = False):
        """List all uploaded files with their metadata with pagination and sorting.

        Args:
            skip (int, optional): Number of records to skip. Defaults to 0.
            limit (int, optional): Maximum number of records to return. Defaults to 10.
            sort_on (str, optional): Field to sort on. Defaults to "uploaded_at".
            ascending (bool, optional): Sort order. Defaults to False (descending).
            all_files (bool, optional): If True, returns all files without pagination. Defaults to False.

        Returns:
            Dict: Dictionary containing:
                - total: Total count of files in the project
                - files: Paginated list of file metadata dictionaries (or all files if all_files=True)
                - is_main_document_tag_assigned: Boolean indicating if any file in the project has "Main Document" tag
        """
        files_prefix = f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}"
        loop = asyncio.get_event_loop()
        blobs = await loop.run_in_executor(None, lambda: list(self.bucket.list_blobs(prefix=files_prefix)))
        filenames = [blob.name.split('/')[-1] for blob in blobs]

        project = await projects_collection.find_one({"project_id": self.project_id})
        files_metadata = project.get('files_metadata', [])

        # Check if any file has Main Document tag (across ALL files, not just paginated results)
        is_main_document_tag_assigned = any(
            DocumentTags.MAIN_DOCUMENT in file_meta.get("tags", [])
            for file_meta in files_metadata
        )

        metadata_dict = {item['filename']: item for item in files_metadata}

        # Use timezone-aware datetime
        current_time = datetime.now(timezone.utc)

        files_with_metadata = []
        for filename in filenames:
            metadata = metadata_dict.get(filename, {})
            uploaded_at = metadata.get('uploaded_at', current_time)
            files_with_metadata.append({
                'file_id': metadata.get('file_id', str(uuid4())),
                'filename': filename,
                'uploaded_at': uploaded_at.isoformat(),
                'time_ago': get_time_ago(uploaded_at),
                'tags': metadata.get('tags', [])
            })

        # Sort the files based on the specified field and order
        reverse_sort = not ascending
        if sort_on == "uploaded_at":
            files_with_metadata.sort(key=lambda x: x['uploaded_at'], reverse=reverse_sort)
        elif sort_on == "filename":
            files_with_metadata.sort(key=lambda x: x['filename'].lower(), reverse=reverse_sort)

        # Get total count before pagination
        total_files = len(files_with_metadata)

        # Apply pagination only if all_files is False
        if all_files:
            result_files = files_with_metadata
        else:
            result_files = files_with_metadata[skip:skip + limit]

        return {
            "total": total_files,
            "files": result_files,
            "is_main_document_tag_assigned": is_main_document_tag_assigned
        }

    async def rename_file(self, file_id: str, new_name: str):
        """Rename a file in the project.

        Args:
            file_id: ID of the file to rename
            new_name: New name for the file

        Returns:
            Tuple[bool, str]: Success status and message
        """
        project = await projects_collection.find_one({"project_id": self.project_id})
        if not project:
            return False, "Project not found"

        file_metadata = next(
            (f for f in project.get('files_metadata', []) if f['file_id'] == file_id),
            None
        )
        if not file_metadata:
            return False, "File not found"

        old_name = file_metadata['filename']

        existing_files = await self.list_uploaded_files()
        if new_name in existing_files:
            return False, "A file with this name already exists"

        try:
            # Rename original file
            old_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}{old_name}")
            new_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}{new_name}")
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.bucket.copy_blob, old_blob, self.bucket, new_blob.name)
            await loop.run_in_executor(None, old_blob.delete)

            # Rename pickle file if it exists
            if file_metadata.get('pickle_path'):
                old_pickle_blob = self.bucket.blob(file_metadata['pickle_path'])
                new_pickle_path = f"{self.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}{new_name}.pickle"
                new_pickle_blob = self.bucket.blob(new_pickle_path)

                try:
                    await loop.run_in_executor(None, self.bucket.copy_blob, old_pickle_blob, self.bucket, new_pickle_blob.name)
                    await loop.run_in_executor(None, old_pickle_blob.delete)

                    # Update pickle path in metadata
                    file_metadata['pickle_path'] = new_pickle_path
                except NotFound:
                    logging.warning(f"Pickle file not found for renaming: {file_metadata['pickle_path']}")

            # Update MongoDB
            await projects_collection.update_one(
                {
                    "project_id": self.project_id,
                    "files_metadata.file_id": file_id
                },
                {
                    "$set": {
                        "files_metadata.$.filename": new_name,
                        "files_metadata.$.pickle_path": file_metadata.get('pickle_path')
                    }
                }
            )

            logging.info(f"Renamed file '{old_name}' to '{new_name}' (ID: {file_id})")
            return True, f"File renamed successfully from '{old_name}' to '{new_name}'"
        except Exception as e:
            logging.error(f"Error renaming file: {str(e)}")
            return False, str(e)

    async def delete_uploaded_file_by_id(self, file_id: str, user_id: str):
        """Delete a file from the project.

        Args:
            file_id: ID of the file to delete
            user_id: ID of the user requesting deletion

        Returns:
            Tuple[bool, str, Optional[str]]: Success status, message, and deleted filename
        """
        project = await projects_collection.find_one({"project_id": self.project_id})
        if not project:
            return False, "Project not found", None

        if project.get('user_id') != user_id:
            logging.warning(f"User {user_id} not authorized to delete file from project {self.project_id}")
            return False, "Unauthorized", None

        file_metadata = next((f for f in project.get('files_metadata', []) if f.get('file_id') == file_id), None)
        if not file_metadata:
            return False, "File not found", None

        filename = file_metadata['filename']
        file_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.DOCUMENTS_FOLDER}{filename}")
        loop = asyncio.get_event_loop()
        try:
            # Delete original file
            await loop.run_in_executor(None, file_blob.delete)

            # Delete pickle file if it exists
            if file_metadata.get('pickle_path'):
                pickle_blob = self.bucket.blob(file_metadata['pickle_path'])
                try:
                    await loop.run_in_executor(None, pickle_blob.delete)
                    logging.info(f"Deleted pickle file for '{filename}'")
                except NotFound:
                    logging.warning(f"Pickle file not found for deletion: {file_metadata['pickle_path']}")

            await projects_collection.update_one(
                {"project_id": self.project_id},
                {
                    "$pull": {"files_metadata": {"file_id": file_id}}
                }
            )

            logging.info(f"Deleted file '{filename}' (ID: {file_id}) from project {self.project_id}")
            return True, "File deleted successfully", filename
        except NotFound:
            logging.warning(f"File '{filename}' (ID: {file_id}) not found in GCP bucket for project {self.project_id}")
            return False, "File not found in storage", None
        except Exception as e:
            logging.error(f"Error deleting file '{filename}' (ID: {file_id}): {str(e)}")
            return False, f"Error deleting file: {str(e)}", None
