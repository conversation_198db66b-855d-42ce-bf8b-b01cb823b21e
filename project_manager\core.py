"""Core project management module.

This module provides the main ProjectManager class that coordinates all project operations
by delegating to specialized manager classes for different aspects of functionality.
"""

import asyncio
import json
import logging
import pickle
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Any

from fastapi import HTT<PERSON>Exception
from google.cloud import storage
from google.cloud.exceptions import NotFound

from utils.database import projects_collection
from config import settings
from models import ProjectDocument
from utils.constants import StoragePaths, AIModels, Collections

from project_manager.file_manager import FileManager
from project_manager.vector_db_manager import VectorDBManager
from project_manager.response_manager import ResponseManager
from project_manager.outline_manager import OutlineManager


class ProjectManager:
    """Manages document processing projects and their resources.

    This class handles all aspects of project management including:
    - Project lifecycle (creation, loading, deletion)
    - File management and processing
    - Cloud storage operations
    - Database operations
    - Vector database management
    - Response generation and PDF creation

    Attributes:
        project_id: Unique identifier for the project
        project_name: Human-readable name for the project
        bucket_name: GCS bucket for project storage
        project_prefix: Project-specific path prefix in storage
        project_data: Dictionary containing project configuration
        responses: Dictionary containing generated responses
        model_name: Name of the LLM model to use
        vector_db: Vector database instance for document search
    """

    def __init__(self, project_name: str = "Untitled Project", project_id: str = None):
        """Initialize ProjectManager instance.

        Args:
            project_name: Name of the project
            project_id: Optional project ID (generated if not provided)
        """
        self.project_name = project_name
        self.project_id = project_id or str(uuid.uuid4())
        self.project_prefix = StoragePaths.PROJECT_PREFIX_TEMPLATE.format(
            project_name=self.project_name,
            project_id=self.project_id
        )
        self.bucket = storage.Client().bucket(settings.BUCKET_NAME)
        self.project_data = {}
        self.responses = {}
        self.vector_db = None
        self.outline_data = {}
        self.vector_db_path = f"{self.project_prefix}{StoragePaths.VECTOR_DB_FOLDER}"
        self.model_name = AIModels.DEFAULT_MODEL  # Default model

        # Initialize specialized managers
        self.file_manager = FileManager(self.project_id, self.project_prefix, self.bucket)
        self.vector_db_manager = VectorDBManager(self.project_id, self.project_prefix, self.vector_db_path)
        self.response_manager = ResponseManager(self.project_id, self.project_prefix, self.bucket, self.responses)
        self.outline_manager = OutlineManager(self.project_id, self.project_prefix, self.bucket, self.outline_data)

        logging.info(f"Initialized ProjectManager with name: {project_name} and ID: {self.project_id}")

    async def load_template(self, template_file: str):
        """Load a project template from cloud storage.

        Args:
            template_file: Path to the template file in cloud storage

        Raises:
            HTTPException: If template file is not found or invalid
        """
        template_blob = self.bucket.blob(template_file)
        loop = asyncio.get_event_loop()
        try:
            template_text = await loop.run_in_executor(None, template_blob.download_as_text)
            template = json.loads(template_text)
        except NotFound:
            logging.error(f"Template file {template_file} not found")
            raise HTTPException(status_code=404, detail="Template file not found")
        except json.JSONDecodeError:
            logging.error(f"Invalid JSON in template file {template_file}")
            raise HTTPException(status_code=500, detail="Invalid template file format")
        self.project_data = template

        self.project_data['title'] = self.project_name
        self.project_data['project_id'] = self.project_id

        for section in self.project_data['sections']:
            section['user_guidelines'] = ""

        await self.save_project()
        logging.info(f"Loaded template from {template_file}")

    async def update_section(self, section_title: str, updates: Dict):
        """Update a section's configuration.

        Args:
            section_title: Title of the section to update
            updates: Dictionary of updates to apply
        """
        for section in self.project_data['sections']:
            if section['section_title'] == section_title:
                changes_made = False
                for key, value in updates.items():
                    if key in section and section[key] != value:
                        section[key] = value
                        changes_made = True

                if changes_made:
                    logging.info(f"Updated section {section_title} with: {updates}")
                    await self.save_project()
                break
        else:
            logging.warning(f"Section {section_title} not found in project data")

    async def add_response(self, section_title: str, response_data: Dict):
        """Add a response for a specific section.

        Args:
            section_title: Title of the section
            response_data: Response data to add
        """
        self.responses[section_title] = response_data
        # Update the response manager's responses
        self.response_manager.responses = self.responses
        logging.info(f"Added response data for section '{section_title}'")
        await self.save_project()

    async def save_project(self):
        """Save project data to cloud storage and update database.

        Saves both project configuration and responses to GCS and updates
        MongoDB with the latest file paths.
        """
        project_data_path = f"{self.project_prefix}project_data.json"
        summaries_path = f"{self.project_prefix}{StoragePaths.SUMMARIES_FILE}"

        project_blob = self.bucket.blob(project_data_path)
        summaries_blob = self.bucket.blob(summaries_path)

        # Run GCS operations in thread pool
        await asyncio.gather(
            asyncio.to_thread(project_blob.upload_from_string, json.dumps(self.project_data, indent=2)),
            asyncio.to_thread(summaries_blob.upload_from_string, json.dumps(self.responses, indent=2))
        )

        await projects_collection.update_one(
            {"project_id": self.project_id},
            {
                "$set": {
                    "project_data_path": project_data_path,
                    "summaries_path": summaries_path
                }
            }
        )

        logging.info(f"Saved project data to GCP bucket and updated MongoDB for project {self.project_id}")

    @classmethod
    async def create_new_project(cls, project_id: str, project_name: str, user_id: str):
        """Create a new project with initial setup.

        Args:
            project_id: Unique identifier for the new project
            project_name: Name of the new project
            user_id: ID of the user creating the project

        Returns:
            ProjectManager: Initialized project instance
        """
        project = cls(project_name, project_id)

        # Create pickled_files directory
        pickle_dir_blob = project.bucket.blob(f"{project.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}")
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, pickle_dir_blob.upload_from_string, '')

        template_file = 'INPUT_TEMPLATTE/Summarization_Template_V1.json'
        await project.load_template(template_file)

        project.project_data['user_id'] = user_id

        # Get default model from MongoDB configuration
        from utils.project_utils import get_default_model
        project.model_name = await get_default_model()

        # Use timezone-aware datetime
        current_time = datetime.now(timezone.utc)

        # Project metadata for MongoDB
        project_metadata = {
            "project_id": project_id,
            "project_name": project_name,
            "user_id": user_id,
            "created_at": current_time,
            "updated_at": current_time,
            "last_visited_time": current_time,
            "is_deleted": False,
            "files_metadata": [],
            "project_data_path": f"{project.project_prefix}project_data.json",
            "summaries_path": f"{project.project_prefix}{StoragePaths.SUMMARIES_FILE}",
            "outline_path": f"{project.project_prefix}{StoragePaths.OUTLINE_FILE}",
            "model_name": project.model_name,
        }
        await projects_collection.insert_one(project_metadata)

        # Save outline data
        outline_blob = project.bucket.blob(f"{project.project_prefix}{StoragePaths.OUTLINE_FILE}")
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, outline_blob.upload_from_string, json.dumps(project.outline_data, indent=2))

        await project.save_project()

        logging.info(f"Created new project with ID: {project.project_id}, name: {project_name}, and user ID: {user_id}")
        return project

    @classmethod
    async def load_project(cls, project_id: str, project_name: str = "", user_id: str = None):
        """Load an existing project from storage.

        Args:
            project_id: ID of the project to load
            project_name: Optional project name
            user_id: Optional user ID for verification (not currently used)

        Returns:
            ProjectManager: Loaded project instance or None if not found
        """
        # Check if project exists and is not soft-deleted
        project_metadata = await projects_collection.find_one({
            "project_id": project_id,
            "$or": [
                {"is_deleted": False},
                {"is_deleted": {"$exists": False}}
            ]
        })

        if not project_metadata:
            logging.warning(f"Project {project_id} not found or is deleted")
            return None

        project_name = project_metadata.get('project_name', project_name)
        project = cls(project_name, project_id)

        # Load the model name from MongoDB, with fallback to current default
        from utils.project_utils import get_default_model
        default_model = await get_default_model()
        project.model_name = project_metadata.get('model_name', default_model)

        try:
            # Run GCP operations in thread pool
            loop = asyncio.get_event_loop()
            project_data_path = f"{project.project_prefix}project_data.json"
            summaries_path = f"{project.project_prefix}{StoragePaths.SUMMARIES_FILE}"

            project_blob = project.bucket.blob(project_data_path)
            summaries_blob = project.bucket.blob(summaries_path)

            project_data_future = loop.run_in_executor(None, lambda: json.loads(project_blob.download_as_text()))
            summaries_future = loop.run_in_executor(None, lambda: json.loads(summaries_blob.download_as_text()))

            project_data, responses = await asyncio.gather(project_data_future, summaries_future)

            project.project_data = project_data
            project.responses = responses
            project.project_name = project_data.get('title', project_name)

            # Update the response manager's responses
            project.response_manager.responses = project.responses

            # Check if sections are missing or empty
            if not project.project_data.get('sections'):
                logging.info(f"No sections found in project {project_id}, loading from template")
                template_file = 'INPUT_TEMPLATTE/Summarization_Template_V1.json'
                template_blob = project.bucket.blob(template_file)

                # Load template data
                template_text = await loop.run_in_executor(None, template_blob.download_as_text)
                template_data = json.loads(template_text)

                # Update project data with template sections while preserving other project data
                project.project_data['sections'] = template_data.get('sections', [])

                # Initialize user guidelines if not present
                for section in project.project_data['sections']:
                    if 'user_guidelines' not in section:
                        section['user_guidelines'] = ""

                # Save the updated project data
                await loop.run_in_executor(
                    None,
                    lambda: project_blob.upload_from_string(
                        json.dumps(project.project_data, indent=2)
                    )
                )

                logging.info(f"Loaded and saved template sections for project {project_id}")

            logging.info(f"Loaded project {project_id} from GCP bucket with {len(project.project_data.get('sections', []))} sections")
            return project

        except Exception as e:
            logging.error(f"Error loading project {project_id}: {str(e)}")
            return None

    async def get_document_content(self, filename: str):
        """Retrieve document content, preferring pickled version if available.

        Args:
            filename: Name of the file to retrieve

        Returns:
            bytes or object: Document content, either raw or unpickled
        """
        return await self.file_manager.get_document_content(filename)



    async def save_uploaded_file(
        self,
        file_content: bytes,
        filename: str,
        tags: List[str] = None,
        sf_pages: List[int] = None,
        pickled_content: bytes = None,
        processed_doc: 'ProjectDocument' = None
    ) -> str:
        """Save an uploaded file with all necessary processing.

        Args:
            file_content: Binary content of the file
            filename: Name of the file
            tags: Optional list of tags for the file
            sf_pages: Optional list of specially formatted pages
            pickled_content: Optional pre-processed pickled content
            processed_doc: Optional pre-processed ProjectDocument

        Returns:
            str: Generated file ID
        """
        return await self.file_manager.save_uploaded_file(
            file_content=file_content,
            filename=filename,
            tags=tags,
            sf_pages=sf_pages,
            pickled_content=pickled_content,
            processed_doc=processed_doc,
            create_vector_db=settings.create_vector_db,
            update_vector_db_func=self.vector_db_manager.update_vector_db
        )

    async def update_vector_db(self, document: ProjectDocument):
        """Update project's vector database with a new document.

        Args:
            document: ProjectDocument instance to add to the vector database

        Raises:
            Exception: If vector database update fails
        """
        await self.vector_db_manager.update_vector_db(document)

    async def list_uploaded_files(self):
        """List all uploaded files in the project.

        Returns:
            List[str]: List of filenames
        """
        return await self.file_manager.list_uploaded_files()

    async def get_uploaded_file(self, filename: str):
        """Get the raw content of an uploaded file.

        Args:
            filename: Name of the file to retrieve

        Returns:
            bytes: File content or None if not found
        """
        return await self.file_manager.get_uploaded_file(filename)

    async def get_document_tags(self, file_id: str) -> List[str]:
        """Get tags for a document by file ID.

        Args:
            file_id: ID of the file

        Returns:
            List[str]: List of tags
        """
        return await self.file_manager.get_document_tags(file_id)

    async def update_document_tags(self, file_id: str, tags: List[str]):
        """Update tags for a document.

        Args:
            file_id: ID of the file
            tags: New list of tags
        """
        await self.file_manager.update_document_tags(file_id, tags)
        await self.save_project()

    async def get_section_guidelines(self, section_title: str) -> str:
        """Get guidelines for a specific section.

        Args:
            section_title: Title of the section

        Returns:
            str: Section guidelines or empty string if not found
        """
        return await self.response_manager.get_section_guidelines(self.project_data, section_title)

    async def delete_project(self):
        """Delete all project files from cloud storage."""
        blobs = self.bucket.list_blobs(prefix=self.project_prefix)
        loop = asyncio.get_event_loop()
        for blob in blobs:
            await loop.run_in_executor(None, blob.delete)
        logging.info(f"Deleted project {self.project_id} from GCP bucket")

    @classmethod
    async def delete_project_by_id(cls, project_id: str):
        """Delete a project by ID.

        Args:
            project_id: ID of the project to delete

        Returns:
            bool: Success status
        """
        project = await cls.load_project(project_id)
        if project:
            pass

        storage_client = storage.Client()
        bucket = storage_client.bucket("mvp-development-431809_cloudbuild")

        blobs = list(bucket.list_blobs(prefix="Projects/"))

        project_folder = next((blob.name.split('/')[1] for blob in blobs if project_id in blob.name), None)

        if not project_folder:
            logging.warning(f"Project {project_id} not found in GCP bucket")
            return False

        project_prefix = f"Projects/{project_folder}/"
        blobs_to_delete = bucket.list_blobs(prefix=project_prefix)
        loop = asyncio.get_event_loop()
        for blob in blobs_to_delete:
            await loop.run_in_executor(None, blob.delete)

        await projects_collection.delete_one({"project_id": project_id})
        logging.info(f"Deleted project {project_id} from GCP bucket and MongoDB")
        return True



    async def rename_project(self, new_name: str):
        """Rename the project.

        Args:
            new_name: New name for the project

        Returns:
            bool: Success status
        """
        old_prefix = self.project_prefix
        new_prefix = f"Projects/{new_name}_{self.project_id}/"

        self.project_name = new_name
        self.project_data['title'] = new_name
        self.project_prefix = new_prefix

        # Update the prefixes in all managers
        self.file_manager.project_prefix = new_prefix
        self.vector_db_manager.project_prefix = new_prefix
        self.vector_db_manager.vector_db_path = f"{new_prefix}{StoragePaths.VECTOR_DB_FOLDER}"
        self.response_manager.project_prefix = new_prefix
        self.outline_manager.project_prefix = new_prefix

        # Get all blobs including those in the pickled_files directory
        blobs = list(self.bucket.list_blobs(prefix=old_prefix))
        loop = asyncio.get_event_loop()

        # Update all blob paths including pickle files
        for blob in blobs:
            new_blob_name = blob.name.replace(old_prefix, new_prefix)
            await loop.run_in_executor(None, self.bucket.rename_blob, blob, new_blob_name)

        # Update MongoDB with new paths
        await projects_collection.update_one(
            {"project_id": self.project_id},
            {
                "$set": {
                    "project_name": new_name,
                    "project_data_path": f"{self.project_prefix}project_data.json",
                    "summaries_path": f"{self.project_prefix}{StoragePaths.SUMMARIES_FILE}",
                    "files_metadata.$[].pickle_path": {
                        "$replaceAll": {
                            "input": "$files_metadata.pickle_path",
                            "find": old_prefix,
                            "replacement": new_prefix
                        }
                    }
                }
            }
        )

        await self.save_project()

        logging.info(f"Renamed project {self.project_id} from '{old_prefix}' to '{new_prefix}' and updated MongoDB and GCP")
        return True

    async def rename_file(self, file_id: str, new_name: str):
        """Rename a file in the project.

        Args:
            file_id: ID of the file to rename
            new_name: New name for the file

        Returns:
            Tuple[bool, str]: Success status and message
        """
        return await self.file_manager.rename_file(file_id, new_name)

    async def delete_uploaded_file_by_id(self, file_id: str, user_id: str):
        """Delete a file from the project.

        Args:
            file_id: ID of the file to delete
            user_id: ID of the user requesting deletion

        Returns:
            Tuple[bool, str, Optional[str]]: Success status, message, and deleted filename
        """
        return await self.file_manager.delete_uploaded_file_by_id(file_id, user_id)

    @classmethod
    async def soft_delete_project(cls, project_id: str):
        """Soft delete a project by marking it as deleted in the database.

        Args:
            project_id: ID of the project to soft delete

        Returns:
            bool: Success status
        """
        try:
            project = await cls.load_project(project_id)
            if project:
                # Clean up pickle files before soft delete
                project_data = await projects_collection.find_one({"project_id": project_id})
                if project_data and 'files_metadata' in project_data:
                    for file_metadata in project_data['files_metadata']:
                        if file_metadata.get('pickle_path'):
                            try:
                                pickle_blob = project.bucket.blob(file_metadata['pickle_path'])
                                await asyncio.get_event_loop().run_in_executor(None, pickle_blob.delete)
                                logging.info(f"Deleted pickle file: {file_metadata['pickle_path']}")
                            except NotFound:
                                logging.warning(f"Pickle file not found: {file_metadata['pickle_path']}")
                            except Exception as e:
                                logging.error(f"Error deleting pickle file: {str(e)}")

            # Use timezone-aware datetime
            result = await projects_collection.update_one(
                {"project_id": project_id},
                {
                    "$set": {
                        "is_deleted": True,
                        "deleted_at": datetime.now(timezone.utc),
                    }
                }
            )

            if result.modified_count > 0:
                logging.info(f"Soft deleted project {project_id}")
                return True
            else:
                logging.warning(f"Project {project_id} not found for soft deletion")
                return False
        except Exception as e:
            logging.error(f"Error soft deleting project {project_id}: {str(e)}")
            return False

    async def update_timestamps(self, update_content: bool = False, update_visit: bool = True):
        """Update project timestamps in the database.

        Args:
            update_content: Whether to update the content timestamp
            update_visit: Whether to update the visit timestamp
        """
        # Use timezone-aware datetime
        current_time = datetime.now(timezone.utc)
        update_fields = {}

        if update_content:
            update_fields["updated_at"] = current_time
        if update_visit:
            update_fields["last_visited_time"] = current_time

        if update_fields:
            await projects_collection.update_one(
                {"project_id": self.project_id},
                {"$set": update_fields}
            )
            logging.info(f"Updated timestamps for project {self.project_id}: {update_fields.keys()}")

    async def get_outline_generation_context(self) -> str:
        """Get the context needed for outline generation.

        Returns:
            str: Combined content from all pickled uploaded files or error message
        """
        return await self.outline_manager.get_outline_generation_context(self.file_manager.get_pickled_content)

    async def save_outline(self, outline_data: dict):
        """Save outline data to cloud storage.

        Args:
            outline_data: Outline data to save

        Raises:
            Exception: If saving fails
        """
        await self.outline_manager.save_outline(outline_data)

    async def get_document_tags_by_filename(self, filename: str) -> List[str]:
        """Retrieve the tags for a document based on its filename.

        Args:
            filename: The name of the file to retrieve tags for

        Returns:
            List[str]: A list of tags associated with the file
        """
        return await self.file_manager.get_document_tags_by_filename(filename)

    async def get_pickled_content(self, filename: str) -> Optional[bytes]:
        """Retrieve only the pickled content for a file.

        Args:
            filename: Name of the file to retrieve pickled content for

        Returns:
            Optional[bytes]: Pickled content if available, None otherwise
        """
        return await self.file_manager.get_pickled_content(filename)

    async def list_uploaded_files_with_metadata(self, skip: int = 0, limit: int = 10, sort_on: str = "uploaded_at", ascending: bool = False, all_files: bool = False):
        """List all uploaded files with their metadata with pagination and sorting.

        Args:
            skip (int, optional): Number of records to skip. Defaults to 0.
            limit (int, optional): Maximum number of records to return. Defaults to 10.
            sort_on (str, optional): Field to sort on. Defaults to "uploaded_at".
            ascending (bool, optional): Sort order. Defaults to False (descending).
            all_files (bool, optional): If True, returns all files without pagination. Defaults to False.

        Returns:
            Dict: Dictionary containing:
                - total: Total count of files in the project
                - files: Paginated list of file metadata dictionaries (or all files if all_files=True)
                - is_main_document_tag_assigned: Boolean indicating if any file in the project has "Main Document" tag
        """
        return await self.file_manager.list_uploaded_files_with_metadata(skip, limit, sort_on, ascending, all_files)

    async def get_pickled_sections_content(self):
        """Read and unpickle the extracted sections content from GCS.

        Returns:
            The unpickled sections content, or None if file doesn't exist

        Raises:
            Exception: If there's an error reading or unpickling the content
        """
        try:
            # Get the blob reference
            sections_blob = self.bucket.blob(f"{self.project_prefix}{StoragePaths.PROCESSED_DOCUMENTS_FOLDER}sections.pkl")

            # Download and unpickle the content
            loop = asyncio.get_event_loop()
            pickled_content = await loop.run_in_executor(None, sections_blob.download_as_bytes)

            # Unpickle the content
            sections_content = pickle.loads(pickled_content)
            logging.info(f"Successfully loaded pickled sections content for project {self.project_id}")
            return sections_content

        except Exception as e:
            logging.error(f"Error loading pickled sections content: {str(e)}")
            return None
