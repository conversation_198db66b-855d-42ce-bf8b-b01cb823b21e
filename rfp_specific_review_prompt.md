# RFP Specific Review Prompt Workflow

This document explains the workflow for the RFP specific parameter creator that transforms generic evaluation criteria into self-contained, RFP-specific evaluation parameters.

## Workflow Overview

The RFP specific parameter creator is an expert system that takes generic evaluation rules and customizes them to be specific to a particular RFP's content. The goal is to make evaluation criteria self-contained and directly applicable to the provided RFP content.

## Data Transformations and Flow

### Input Data
- **RFP Content**: The specific procurement document content that needs evaluation parameters
- **Evaluation Rules**: Generic evaluation criteria with name, type, and check fields

### Output Data
- **Customized Evaluation Parameters**: RFP-specific evaluation criteria that are self-contained and directly applicable

### Transformation Process
The system preserves the structure of evaluation rules while customizing the "check" field to be specific to the RFP content.

## Workflow

```mermaid
flowchart TD
    A[Start: RFP Specific Parameter Creator] --> B[Receive Inputs]
    B --> C{Analyze RFP Content}
    B --> D{Analyze Evaluation Rules}
    
    C --> E[Extract Key RFP Requirements<br/>- Scope details<br/>- Technical requirements<br/>- Performance criteria<br/>- Submission requirements]
    
    D --> F[Parse Evaluation Rules Structure<br/>- Name field<br/>- Type field<br/>- Check field]
    
    E --> G[Content Analysis Complete]
    F --> H[Rules Structure Analysis Complete]
    
    G --> I[Map RFP Content to Evaluation Criteria]
    H --> I
    
    I --> J[Transform Generic Check Fields]
    J --> K[Create Self-Contained Parameters<br/>- Embed specific RFP context<br/>- Make evaluable without external context<br/>- Maintain original structure]
    
    K --> L{Validation Check}
    L -->|Parameters not self-contained| M[Refine Parameters<br/>- Add more RFP-specific context<br/>- Ensure standalone evaluation]
    M --> L
    
    L -->|Parameters are self-contained| N[Format Output]
    N --> O[Preserve Original Format<br/>- Keep name field unchanged<br/>- Keep type field unchanged<br/>- Update only check field]
    
    O --> P[Output: RFP-Specific Evaluation Parameters]
    P --> Q[End]

    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style Q fill:#ffcdd2
    style L fill:#fff3e0
    style K fill:#f3e5f5
```

## Process Details

### 1. Input Analysis Phase
- **RFP Content Analysis**: The system examines the provided RFP content to understand specific requirements, scope, technical details, and evaluation contexts
- **Evaluation Rules Parsing**: Generic evaluation rules are analyzed to understand their structure (name, type, check fields)

### 2. Content Mapping Phase
- The system identifies how generic evaluation criteria can be made specific to the RFP content
- Key RFP elements are mapped to relevant evaluation parameters

### 3. Parameter Transformation Phase
- **Self-Contained Creation**: The "check" field is rewritten to include specific RFP context
- **Context Embedding**: RFP-specific details are embedded directly into the evaluation criteria
- **Standalone Evaluation**: Parameters are crafted to be evaluable without needing additional context

### 4. Validation and Output Phase
- **Self-Containment Check**: Ensures parameters can be evaluated independently
- **Format Preservation**: Maintains the original structure while updating only the check field
- **Quality Assurance**: Verifies that parameters are specific to the RFP content

## Key Principles

1. **Self-Contained Parameters**: Evaluation criteria must be easily evaluable on their own without needing additional context
2. **RFP Specificity**: All parameters must be specifically tailored to the provided RFP content
3. **Structure Preservation**: Only the "check" field is modified; name and type fields remain unchanged
4. **Context Embedding**: RFP-specific requirements and details are embedded directly into the evaluation parameters

## Benefits

- **Improved Accuracy**: Evaluation parameters are directly relevant to the specific RFP
- **Reduced Ambiguity**: Self-contained parameters eliminate the need for external context
- **Consistent Evaluation**: Standardized format ensures consistent evaluation processes
- **Efficiency**: Evaluators can assess proposals without referring to multiple documents
