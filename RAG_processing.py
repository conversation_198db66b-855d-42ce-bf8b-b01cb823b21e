"""Retrieval-Augmented Generation (RAG) Processing Module

This module provides comprehensive functionality for document processing and retrieval-augmented
generation using language models. It handles document loading, chunking, vector database operations,
and summary generation for various document sections.

Key Features:
- Document loading and preprocessing for multiple file formats (PDF, Word)
- Vision-based text extraction for special formatting
- Chunking of documents for efficient processing
- Vector database integration for semantic search
- Single and multi-query summary generation
- Token counting and management
- Async operations support
- Extensive error handling and logging

The module integrates with external services and models to provide a complete
RAG pipeline for document processing and summarization.
"""

from langchain_community.document_loaders import PyMuPDFLoader
from langchain_community.document_loaders.word_document import UnstructuredWordDocumentLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
import os
import logging
from RAG.documents_loader import extract_text_with_vision, extract_page_as_image
from utils.constants import StoragePaths, FileProcessing
from RAG.vector_db import get_embeddings
from config import settings
import datetime
from typing import List, Dict
from RAG.vector_db import get_embeddings, create_vector_db, retrieve, retrieve_multiples
from RAG.rfp_prompts import get_rfp_info_query, get_task_area_query, get_submission_instructions_query, get_evaluation_factors_for_award_query, get_background_query, get_scope_query, get_key_personnel_query, get_red_flags_query, get_list_of_attachments_query, get_rfp_info_prompt, get_task_area_prompt, get_submission_instructions_prompt, get_evaluation_metrics_prompt, get_background_prompt, get_scope_prompt, get_key_personnel_prompt, get_red_flags_prompt, get_list_of_attachments_prompt
import tempfile
import shutil
from document_processing_app import DocumentSection, count_tokens
from project_manager import ProjectManager
import asyncio
import tiktoken
from utils.constants import Sections

# System prompt for section summarization
SECTION_SUMMARISATION_SYSTEM_PROMPT = """You are an experienced procurement developer who can extensively summarize the specific content of procurements below  5500 words limit. You will be tasked with drafting the summary of certain sections using the provided context and the summary you will draft will have all the critical details neccessary for making an understanding document of the procurement. The summary has to be extensive and self-contained covering all the critical details neccessary for making the mentioned section of the understanding document of the procurement. Remember that the section summary you will produce has to be self contained and you are constrained in having a output word limit of 5500 words. It is very important to note that no extra commentary/text about what you did or word count, etc which is not required in the understanding document should be added to the output, neither at the begiining nor at the end."""

async def load_documents(file_paths, vision_files=None):
    """Load and preprocess documents from multiple file paths.

    Args:
        file_paths: List of paths to documents to load
        vision_files: Optional dictionary mapping filenames to pages requiring vision processing

    Returns:
        List of loaded and processed documents

    The function handles:
    - PDF and Word document loading
    - Vision-based text extraction for special formatting
    - Metadata addition for each document
    - Error handling and logging
    """
    logging.info(f"Starting document loading process for {len(file_paths)} files")
    if vision_files is None:
        vision_files = {}
        logging.info("No vision files specified")
    else:
        logging.info(f"Vision processing requested for {len(vision_files)} files")

    docs = []
    with tempfile.TemporaryDirectory() as temp_dir:
        logging.info(f"Created temporary directory for image extraction: {temp_dir}")
        for path in file_paths:
            filename = os.path.basename(path)
            logging.info(f"Processing file: {filename}")

            try:
                if path.endswith('.pdf'):
                    logging.info(f"Loading PDF file: {filename}")
                    loader = PyMuPDFLoader(path)
                    loaded_docs = await asyncio.to_thread(loader.load)
                    logging.info(f"Successfully loaded PDF with {len(loaded_docs)} pages")
                elif path.endswith('.docx') or path.endswith('.doc'):
                    logging.info(f"Loading Word document: {filename}")
                    loader = UnstructuredWordDocumentLoader(path)
                    loaded_docs = await asyncio.to_thread(loader.load)
                    logging.info(f"Successfully loaded Word document with {len(loaded_docs)} pages")
                else:
                    logging.warning(f"Unsupported file type for {path}")
                    continue

                for doc in loaded_docs:
                    page_number = doc.metadata.get("page", 0)
                    logging.debug(f"Processing page {page_number} of {filename}")

                    if filename in vision_files and page_number in vision_files[filename]:
                        logging.info(f"Applying vision processing for page {page_number} of {filename}")
                        image_path = extract_page_as_image(path, page_number, temp_dir)
                        logging.debug(f"Page image saved to: {image_path}")
                        extracted_text = extract_text_with_vision(image_path)

                        if not extracted_text.startswith("Error:"):
                            logging.info(f"Successfully extracted text using {FileProcessing.DEFAULT_IMAGE_PROCESSING_METHOD} for page {page_number}")
                            doc.page_content = extracted_text
                        else:
                            logging.error(f"{FileProcessing.DEFAULT_IMAGE_PROCESSING_METHOD} processing failed for page {page_number}: {extracted_text[:100]}..." if len(extracted_text) > 100 else extracted_text)

                    doc.metadata.update({
                        "source": filename,
                        "file_path": path,
                        "page": page_number,
                        "total_pages": len(loaded_docs)
                    })
                    docs.append(doc)
                    logging.debug(f"Added document for page {page_number} with {len(doc.page_content)} characters")

            except Exception as e:
                logging.error(f"Error processing file {filename}: {str(e)}")
                continue

    logging.info(f"Document loading completed. Processed {len(docs)} total pages across all documents")
    return docs

async def chunk_documents(docs, chunk_size=settings.RAG_CHUNK_SIZE, chunk_overlap=settings.RAG_CHUNK_OVERLAP):
    """Split documents into overlapping chunks for processing.

    Args:
        docs: List of documents to chunk
        chunk_size: Maximum size of each chunk in characters
        chunk_overlap: Number of characters to overlap between chunks

    Returns:
        List of document chunks

    Features:
    - Recursive character splitting
    - Metadata preservation
    - Detailed statistics logging
    - Error handling
    """
    logging.info(f"Starting document chunking with size={chunk_size}, overlap={chunk_overlap}")
    logging.info(f"Input document count: {len(docs)}")

    # Log input document details
    total_input_chars = sum(len(doc.page_content) for doc in docs)
    logging.info(f"Total input content size: {total_input_chars} characters")

    for doc in docs:
        logging.debug(f"Document from {doc.metadata.get('source', 'unknown')}, "
                     f"page {doc.metadata.get('page', 'unknown')}: "
                     f"{len(doc.page_content)} characters")

    try:
        logging.info("Initializing RecursiveCharacterTextSplitter")
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            is_separator_regex=False,
        )

        logging.info("Starting document splitting process")
        chunks = await asyncio.to_thread(text_splitter.split_documents, docs)

        # Log detailed chunk statistics
        total_chars = sum(len(chunk.page_content) for chunk in chunks)
        avg_chunk_size = total_chars / len(chunks) if chunks else 0
        min_chunk_size = min(len(chunk.page_content) for chunk in chunks) if chunks else 0
        max_chunk_size = max(len(chunk.page_content) for chunk in chunks) if chunks else 0

        logging.info(f"Successfully created {len(chunks)} chunks from {len(docs)} documents")
        logging.info(f"Chunk statistics:")
        logging.info(f"- Total content size: {total_chars} characters")
        logging.info(f"- Average chunk size: {avg_chunk_size:.2f} characters")
        logging.info(f"- Minimum chunk size: {min_chunk_size} characters")
        logging.info(f"- Maximum chunk size: {max_chunk_size} characters")
        logging.info(f"- Compression ratio: {total_chars/total_input_chars:.2f}x")

        # Log individual chunk details at debug level
        for i, chunk in enumerate(chunks):
            source = chunk.metadata.get('source', 'unknown')
            page = chunk.metadata.get('page', 'unknown')
            chunk_len = len(chunk.page_content)
            logging.debug(f"Chunk {i+1}: source={source}, page={page}, size={chunk_len} chars")

        return chunks
    except Exception as e:
        logging.error(f"Error during document chunking: {str(e)}")
        logging.error(f"Stack trace:", exc_info=True)
        raise

async def create_vector_db(docs, embeddings_model='geko'):
    """Create a FAISS vector database from document chunks.

    Args:
        docs: List of document chunks to index
        embeddings_model: Name of the embeddings model to use

    Returns:
        FAISS vector store instance
    """
    logging.info(f"Creating vector database with embeddings model {embeddings_model}")
    embeddings = await get_embeddings(embeddings_model)
    # Run DB creation in thread pool
    return await asyncio.to_thread(FAISS.from_documents, docs, embeddings)

async def save_vector_db(db, path):
    """Save vector database to disk.

    Args:
        db: Vector database instance to save
        path: Path to save the database to
    """
    logging.info(f"Saving vector database to {path}")
    await asyncio.to_thread(db.save_local, path)

async def generate_summary(section: str) -> str:
    """Generate summary for a section using configured query approach.

    Args:
        section: Name of the section to summarize

    Returns:
        Generated summary text
    """
    if settings.RAG_USE_MULTIPLE_QUERIES:
        logging.info(f"Using multiple queries for {section}")
        return await generate_summary_multiple_queries(section)
    else:
        logging.info(f"Using single query for {section}")
        return await generate_summary_single_query(section)

async def generate_summary_single_query(section: str, db, llm, project_id: str = None, user_guidelines: str = "") -> Dict:
    """Generate section summary using a single query approach.

    Args:
        section: Section name to summarize
        db: Vector database instance
        llm: Language model instance
        project_id: Optional project identifier
        user_guidelines: Optional user-provided guidelines

    Returns:
        Dictionary containing:
        - final_response: Generated summary
        - section_title: Section name
        - input_tokens: Number of input tokens used
        - output_tokens: Number of output tokens used
        - user_guidelines: Applied guidelines

    Features:
    - Context retrieval from vector DB
    - Token counting and management
    - Error handling
    - Logging of process steps
    """
    logging.info(f"Starting single query summary generation for section: {section}")
    logging.info(f"Project ID: {project_id}, Has user guidelines: {bool(user_guidelines)}")

    try:
        # Generate query
        query = get_query_for_section(section)
        logging.info(f"Generated query for section {section}: {query}")

        # Get relevant documents
        logging.info(f"Retrieving documents with mode={settings.RAG_RETRIEVAL_MODE}, k={settings.RAG_TOP_K}")
        retrieved_docs = await retrieve(query, db, mode=settings.RAG_RETRIEVAL_MODE, k=settings.RAG_TOP_K, llm=llm)
        logging.info(f"Retrieved {len(retrieved_docs)} documents")

        context = "\n".join([doc.page_content for doc in retrieved_docs])
        logging.debug(f"Combined context length: {len(context)} characters")

        # Get previous response if it exists
        # previous_response = ""
        # if project_id:
        #     logging.info(f"Loading previous response for project {project_id}")
        #     project_manager = await ProjectManager.load_project(project_id)
        #     if project_manager and section in project_manager.responses:
        #         previous_response = project_manager.responses[section].get('content', '')
        #         logging.info("Found previous response")

        # Generate prompt
        # prompt = get_prompt_for_section(section, context, user_guidelines, previous_response)
        prompt = get_prompt_for_section(section, context, user_guidelines)
        if not prompt:
            logging.error(f"No prompt template found for section: {section}")
            return {
                "final_response": f"Error: No prompt template found for section '{section}'",
                "section_title": section,
                "input_tokens": 0,
                "output_tokens": 0,
                "user_guidelines": user_guidelines
            }

        # Count input tokens
        input_tokens = count_tokens(SECTION_SUMMARISATION_SYSTEM_PROMPT + prompt)
        logging.info(f"Input tokens for section {section}: {input_tokens}")

        logging.info("Generating response with LLM")
        loop = asyncio.get_event_loop()
        messages = [
            ("system", SECTION_SUMMARISATION_SYSTEM_PROMPT),
            ("human", prompt),
        ]
        response = await loop.run_in_executor(None, lambda: llm.invoke(messages))
        logging.info("Successfully generated response")

        # Get response content and count output tokens
        response_content = response.content if hasattr(response, 'content') else response
        output_tokens = count_tokens(response_content)
        logging.info(f"Output tokens for section {section}: {output_tokens}")

        # Add token limit note if output tokens exceed 7500
        if output_tokens > 7500:
            response_content += "\n\n## Output Token Limit Note:- \nThe response may be incomplete because of the output token limit being hit"
            logging.warning(f"Output tokens ({output_tokens}) exceeded 7500 for section {section}")

        result = {
            "final_response": response_content,
            "section_title": section,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "user_guidelines": user_guidelines
        }
        logging.info(f"Summary generation complete. Tokens used - Input: {input_tokens}, Output: {output_tokens}")
        return result

    except Exception as e:
        logging.error(f"Error in generate_summary_single_query for section {section}: {str(e)}")
        raise f"Error in generate_summary_single_query for section {section}: {str(e)}"
        # return {
        #     "final_response": f"Error generating summary: {str(e)}",
        #     "section_title": section,
        #     "input_tokens": 0,
        #     "output_tokens": 0,
        #     "user_guidelines": user_guidelines
        # }

async def generate_summary_multiple_queries(section: str, db, llm, project_id: str = None, user_guidelines: str = "") -> Dict:
    """Generate section summary using multiple queries for better coverage.

    Args:
        section: Section name to summarize
        db: Vector database instance
        llm: Language model instance
        project_id: Optional project identifier
        user_guidelines: Optional user-provided guidelines

    Returns:
        Dictionary containing summary and metadata (same as single_query version)

    Features:
    - Multiple context queries for better coverage
    - Context combination and deduplication
    - Token management
    - Detailed logging
    """
    logging.info(f"Starting multiple query summary generation for section: {section}")
    logging.info(f"Project ID: {project_id}, Has user guidelines: {bool(user_guidelines)}")

    try:
        # Generate queries
        queries = get_queries_for_section(section)
        if not queries:
            logging.error(f"No queries generated for section: {section}")
            return {
                "final_response": "Error: Failed to generate queries for the section",
                "section_title": section,
                "input_tokens": 0,
                "output_tokens": 0,
                "user_guidelines": user_guidelines
            }
        logging.info(f"Generated {len(queries)} queries for section {section}")
        logging.debug(f"Queries: {queries}")

        # Retrieve documents
        logging.info(f"Retrieving documents with k={settings.RAG_TOP_K}")
        retrieved_docs = await retrieve_multiples(queries, db, k=settings.RAG_TOP_K)
        logging.info(f"Retrieved {len(retrieved_docs)} total documents")

        context = "\n".join([doc.page_content for doc in retrieved_docs])
        logging.debug(f"Combined context length: {len(context)} characters")

        # Get previous response if it exists
        # previous_response = ""
        # if project_id:
        #     logging.info(f"Loading previous response for project {project_id}")
        #     project_manager = await ProjectManager.load_project(project_id)
        #     if project_manager and section in project_manager.responses:
        #         previous_response = project_manager.responses[section].get('content', '')
        #         logging.info("Found previous response")

        # Generate prompt
        # prompt = get_prompt_for_section(section, context, user_guidelines, previous_response)
        prompt = get_prompt_for_section(section, context, user_guidelines)
        logging.info(f"Generated prompt with length: {len(prompt)}")

        # Count input tokens
        input_tokens = count_tokens(SECTION_SUMMARISATION_SYSTEM_PROMPT + prompt)
        logging.info(f"Input tokens for section {section}: {input_tokens}")

        # Generate response
        logging.info("Generating response with LLM")
        loop = asyncio.get_event_loop()
        messages = [
            ("system", SECTION_SUMMARISATION_SYSTEM_PROMPT),
            ("human", prompt),
        ]
        logging.info(f"Invoking LLM: {llm}")
        response = await loop.run_in_executor(None, lambda: llm.invoke(messages))
        logging.info("Successfully generated response")

        # Get response content and count output tokens
        response_content = response.content if hasattr(response, 'content') else response
        output_tokens = count_tokens(response_content)
        logging.info(f"Output tokens for section {section}: {output_tokens}")

        # Add token limit note if output tokens exceed 7500
        if output_tokens > 7500:
            response_content += "\n\n### **Output Token Limit Note:-** \nThe response may be incomplete because of the output token limit being hit"
            logging.warning(f"Output tokens ({output_tokens}) exceeded 7500 for section {section}")

        result = {
            "final_response": response_content,
            "section_title": section,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "user_guidelines": user_guidelines
        }
        logging.info(f"Summary generation complete. Tokens used - Input: {input_tokens}, Output: {output_tokens}")
        return result

    except Exception as e:
        logging.error(f"Error in generate_summary_multiple_queries for section {section}: {str(e)}")
        raise f"Error in generate_summary_single_query for section {section}: {str(e)}"

        # return {
        #     "final_response": f"Error generating summary: {str(e)}",
        #     "section_title": section,
        #     "input_tokens": 0,
        #     "output_tokens": 0,
        #     "user_guidelines": user_guidelines
        # }

def get_queries_for_section(section: str) -> List[str]:
    """Get predefined queries for a specific section.

    Args:
        section: Section name to get queries for

    Returns:
        List of query strings for the section
    """
    logging.info(f"Getting queries for section: {section}")
    if section == Sections.PROPOSAL_INFO:
        return get_rfp_info_query().split('\n')
    elif section == Sections.TASK_AREA:
        return get_task_area_query().split('\n')
    elif section == Sections.SUBMISSION_INSTRUCTIONS:
        return get_submission_instructions_query().split('\n')
    elif section == Sections.EVALUATION_FACTORS:
        return get_evaluation_factors_for_award_query().split('\n')
    elif section == Sections.BACKGROUND:
        return get_background_query().split('\n')
    elif section == Sections.SCOPE:
        return get_scope_query().split('\n')
    elif section == Sections.KEY_PERSONNEL:
        return get_key_personnel_query().split('\n')
    elif section == "Red Flags":
        return get_red_flags_query().split('\n')
    elif section == Sections.LIST_OF_ATTACHMENTS:
        return get_list_of_attachments_query().split('\n')
    logging.warning(f"No specific queries defined for section: {section}")
    return []

def get_query_for_section(section: str) -> str:
    """Get single query for a specific section.

    Args:
        section: Section name to get query for

    Returns:
        Query string for the section
    """
    logging.info(f"Getting single query for section: {section}")
    if section == Sections.PROPOSAL_INFO:
        return get_rfp_info_query()
    elif section == Sections.BACKGROUND:
        return get_background_query()
    elif section == Sections.SCOPE:
        return get_scope_query()
    elif section == Sections.TASK_AREA:
        return get_task_area_query()
    elif section == Sections.SUBMISSION_INSTRUCTIONS:
        return get_submission_instructions_query()
    elif section == Sections.EVALUATION_FACTORS:
        return get_evaluation_factors_for_award_query()
    elif section == Sections.LIST_OF_ATTACHMENTS:
        return get_list_of_attachments_query()
    elif section == Sections.KEY_PERSONNEL:
        return get_key_personnel_query()
    elif section == "Red Flags":
        return get_red_flags_query()
    else:
        logging.warning(f"No specific query defined for section: {section}")
        return f"Summarize the information related to {section}"

# def get_prompt_for_section(section: str, context: str, user_guidelines: str = "", previous_response: str = "") -> str:
def get_prompt_for_section(section: str, context: str, user_guidelines: str = "") -> str:
    """Generate prompt for section summarization.

    Args:
        section: Section name
        context: Retrieved context for summarization
        user_guidelines: Optional user-provided guidelines

    Returns:
        Complete prompt for the language model

    Features:
    - Section-specific prompt templates
    - User guidelines integration
    - Logging of prompt generation
    """
    logging.info(f"Generating prompt for section: {section}")
    base_prompt = ""

    if section == Sections.PROPOSAL_INFO:
        base_prompt = get_rfp_info_prompt(context)
    elif section == Sections.TASK_AREA:
        base_prompt = get_task_area_prompt(context)
    elif section == Sections.SUBMISSION_INSTRUCTIONS:
        base_prompt = get_submission_instructions_prompt(context)
    elif section == Sections.EVALUATION_FACTORS:
        base_prompt = get_evaluation_metrics_prompt(context)
    elif section == Sections.BACKGROUND:
        base_prompt = get_background_prompt(context)
    elif section == Sections.SCOPE:
        base_prompt = get_scope_prompt(context)
    elif section == Sections.KEY_PERSONNEL:
        base_prompt = get_key_personnel_prompt(context)
    elif section == "Red Flags":
        base_prompt = get_red_flags_prompt(context)
    elif section == Sections.LIST_OF_ATTACHMENTS:
        base_prompt = get_list_of_attachments_prompt(context)

    # Add previous response if available
    # if previous_response:
    #     logging.info(f"Adding previous response")
    #     base_prompt += f"\n\n**Previous Response:**\n{previous_response}"

    # Add user guidelines if provided
    if user_guidelines:
        logging.info(f"Adding user guidelines")
        base_prompt += f"\n\n**Additional Guidelines:**\n{user_guidelines}\n\n *VERY IMPORTANT NOTE: If any instruction in the Additional Guidelines conflicts with the previous instructions, always follow/Prioritize the Additional Guidelines.*"

    return base_prompt

async def load_vector_db(path):
    """Load vector database from disk.

    Args:
        path: Path to load the database from

    Returns:
        Loaded FAISS vector store instance
    """
    logging.info(f"Loading vector database from {path}")
    embeddings = await get_embeddings()
    return await asyncio.to_thread(
        FAISS.load_local,
        path,
        embeddings,
        allow_dangerous_deserialization=True
    )

async def generate_summary_with_rag(vector_db, section_title: str, llm, project_id: str = None, user_guidelines: str = "") -> DocumentSection:
    """Generate section summary using RAG approach.

    Args:
        vector_db: Vector database instance
        section_title: Name of section to summarize
        llm: Language model instance
        project_id: Optional project identifier
        user_guidelines: Optional user-provided guidelines

    Returns:
        DocumentSection instance containing the generated summary

    Features:
    - Configurable query approach (single/multiple)
    - Token tracking
    - Error handling
    - Detailed logging
    """
    logging.info(f"Starting RAG summary generation for section: {section_title}")
    logging.info(f"Using {'multiple' if settings.RAG_USE_MULTIPLE_QUERIES else 'single'} query approach")

    try:
        if settings.RAG_USE_MULTIPLE_QUERIES:
            summary = await generate_summary_multiple_queries(section_title, vector_db, llm, project_id=project_id, user_guidelines=user_guidelines)
        else:
            summary = await generate_summary_single_query(section_title, vector_db, llm, project_id=project_id, user_guidelines=user_guidelines)

        logging.info(f"Successfully generated summary for section {section_title}")
        logging.debug(f"Summary length: {len(summary['final_response'])} characters")
        logging.info(f"Token usage - Input: {summary['input_tokens']}, Output: {summary['output_tokens']}")

        return DocumentSection(
            section_title=section_title,
            section_prompt="",  # Not needed for RAG
            source="RAG",
            model_name="gemini-1.5-flash-001",
            config_settings={},
            final_response=summary["final_response"],
            input_tokens=summary["input_tokens"],
            output_tokens=summary["output_tokens"],
            user_guidelines=user_guidelines
        )
    except Exception as e:
        logging.error(f"Error in generate_summary_with_rag for section {section_title}: {str(e)}")
        raise

async def generate_summaries_with_rag(vector_db, section_titles, llm, project_id: str = None):
    """Generate summaries for multiple sections using RAG.

    Args:
        vector_db: Vector database instance
        section_titles: List of sections to summarize
        llm: Language model instance
        project_id: Optional project identifier

    Returns:
        List of summaries with metadata

    Features:
    - Batch processing of sections
    - Error handling per section
    - Token usage tracking
    - Progress logging
    """
    logging.info(f"Starting batch RAG summary generation for {len(section_titles)} sections")
    logging.info(f"Sections to process: {section_titles}")
    logging.info(f"Using {'multiple' if settings.RAG_USE_MULTIPLE_QUERIES else 'single'} query approach")

    summaries = []
    for section_title in section_titles:
        try:
            logging.info(f"Processing section: {section_title}")
            if settings.RAG_USE_MULTIPLE_QUERIES:
                summary = await generate_summary_multiple_queries(section_title, vector_db, llm, project_id=project_id)
            else:
                summary = await generate_summary_single_query(section_title, vector_db, llm, project_id=project_id)

            summaries.append({
                "section_title": section_title,
                "final_response": summary["final_response"],
                "input_tokens": summary["input_tokens"],
                "output_tokens": summary["output_tokens"]
            })
            logging.info(f"Successfully generated summary for section {section_title}")
            logging.debug(f"Summary length: {len(summary['final_response'])} characters")
            logging.info(f"Token usage - Input: {summary['input_tokens']}, Output: {summary['output_tokens']}")

        except Exception as e:
            logging.error(f"Error generating summary for section {section_title}: {str(e)}")
            summaries.append({
                "section_title": section_title,
                "final_response": f"Error generating summary: {str(e)}",
                "input_tokens": 0,
                "output_tokens": 0
            })

    logging.info(f"Batch summary generation complete. Generated {len(summaries)} summaries")
    total_input_tokens = sum(s["input_tokens"] for s in summaries)
    total_output_tokens = sum(s["output_tokens"] for s in summaries)
    logging.info(f"Total token usage - Input: {total_input_tokens}, Output: {total_output_tokens}")

    return summaries
