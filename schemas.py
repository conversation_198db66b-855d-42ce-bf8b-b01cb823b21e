"""API Schema Definitions Module

This module defines Pydantic models for request/response validation and data serialization
in the API. It provides type-safe schemas for user management, project operations,
file handling, and section processing.

Key Features:
- User authentication and management schemas
- Project creation and management models
- File upload and tagging schemas
- Document section processing models
- Input validation and type checking
- Custom validators for data integrity
- Content version management schemas
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr, Field, field_validator
from datetime import datetime
import logging
from dataclasses import field

logger = logging.getLogger(__name__)

class UserDetails(BaseModel):
    """User details model for basic user information.

    Attributes:
        email: User's email address
        full_name: User's complete name
    """
    email: str
    full_name: str

class LoginRequest(BaseModel):
    """Login request model for user authentication.

    Attributes:
        username: User's login username
        password: User's password (will be hashed)
    """
    username: str
    password: str

class ProjectCreate(BaseModel):
    """Project creation request model.

    Attributes:
        project_name: Name for the new project (max 25 characters)
    """
    project_name: str = Field(..., max_length=25, description="Name for the new project (maximum 25 characters)")

    @field_validator('project_name')
    @classmethod
    def validate_project_name(cls, v):
        """Validate project name length and content."""
        if len(v) > 25:
            raise ValueError('Project name must not exceed 25 characters')
        if not v.strip():
            raise ValueError('Project name cannot be empty or only whitespace')
        return v.strip()

class TagAssignment(BaseModel):
    """Model for assigning tags to files.

    Attributes:
        tags: List of tags to assign to the file
    """
    tags: List[str] = Field(..., description="List of tags to assign to the file")

class GenerateSection(BaseModel):
    """Request model for generating a single document section.

    Attributes:
        section_title: Title of the section to generate
        user_guidelines: Optional guidelines for section generation
    """
    section_title: str
    user_guidelines: Optional[str] = None

class AddSectionSummary(BaseModel):
    """Request model for adding a section summary.

    Attributes:
        section_title: Title of the section to add summary for
    """
    section_title: str

class GenerateMultipleSections(BaseModel):
    """Request model for generating multiple document sections.

    Attributes:
        section_titles: List of section titles to generate

    Features:
        - Automatic duplicate removal via validator
        - Warning logging for duplicate entries
    """
    section_titles: List[str]

    @field_validator('section_titles')
    @classmethod
    def remove_duplicates(cls, v):
        """Remove duplicate section titles and log a warning if found.

        Args:
            v: List of section titles

        Returns:
            List of unique section titles
        """
        unique_sections = list(dict.fromkeys(v))
        if len(unique_sections) != len(v):
            logger.warning("Duplicate section titles were removed from the request")
        return unique_sections

class UserCreate(BaseModel):
    """User creation request model with required fields.

    Attributes:
        email: User's email address (validated format)
        password: User's password
        full_name: User's complete name
        phone_number: User's contact number
    """
    email: EmailStr
    password: str
    full_name: str
    phone_number: str

class UserUpdate(BaseModel):
    """User update request model with optional fields.

    Attributes:
        full_name: Optional new name for the user
        new_password: Optional new password
    """
    full_name: Optional[str] = None
    new_password: Optional[str] = None

class ForgetPasswordRequest(BaseModel):
    """Password reset request initiation model.

    Attributes:
        email: Email address for password reset
    """
    email: str

class ResetPasswordRequest(BaseModel):
    """Password reset completion model.

    Attributes:
        token: Reset token from email
        new_password: New password to set
    """
    token: str
    new_password: str

class TagRemoval(BaseModel):
    """Model for removing tags from files.

    Attributes:
        tags: List of tags to remove from the file
    """
    tags: List[str] = Field(..., description="List of tags to remove from the file")

class ProjectDelete(BaseModel):
    """Request model for deleting multiple projects.

    Attributes:
        project_ids: List of project IDs to delete
    """
    project_ids: List[str] = Field(..., description="List of project IDs to delete")

class ProjectRename(BaseModel):
    """Request model for renaming a project.

    Attributes:
        new_name: New name for the project (max 25 characters)
    """
    new_name: str = Field(..., max_length=25, description="New name for the project (maximum 25 characters)")

    @field_validator('new_name')
    @classmethod
    def validate_new_name(cls, v):
        """Validate project name length and content."""
        if len(v) > 25:
            raise ValueError('Project name must not exceed 25 characters')
        if not v.strip():
            raise ValueError('Project name cannot be empty or only whitespace')
        return v.strip()

class FileRename(BaseModel):
    """Request model for renaming a file.

    Attributes:
        new_name: New name for the file
    """
    new_name: str

class AdminUserUpdate(BaseModel):
    """Admin-only user update model.

    Attributes:
        is_approved: User approval status
    """
    is_approved: bool

class UserInList(BaseModel):
    """User list item model with full user details.

    Attributes:
        id: User's unique identifier
        email: User's email address
        full_name: User's complete name
        phone_number: User's contact number
        is_verified: Email verification status
        is_approved: Admin approval status
        created_at: Account creation timestamp
    """
    id: str
    email: str
    full_name: str
    phone_number: str
    is_verified: bool
    is_approved: bool
    created_at: datetime

class FileUploadInfo(BaseModel):
    """File upload information model.

    Attributes:
        filename: Name of the uploaded file
        tags: Optional list of tags for the file
        sf_pages: Optional list of special formatting page numbers
    """
    filename: str
    tags: List[str] = []
    sf_pages: Optional[List[int]] = None

class AIModelCreate(BaseModel):
    """Schema for creating a new AI model configuration.

    Attributes:
        model_name: Technical name of the AI model
        display_name: User-friendly display name
        description: Description of the model's capabilities
        is_active: Whether the model is available for use
        is_default: Whether this should be the default model
    """
    model_name: str = Field(..., description="Technical name of the AI model")
    display_name: str = Field(..., description="User-friendly display name")
    description: str = Field(..., description="Description of the model's capabilities")
    is_active: bool = Field(default=True, description="Whether the model is available for use")
    is_default: bool = Field(default=False, description="Whether this should be the default model")

class AIModelUpdate(BaseModel):
    """Schema for updating an existing AI model configuration.

    Attributes:
        display_name: Optional new display name
        description: Optional new description
        is_active: Optional new active status
        is_default: Optional new default status
    """
    display_name: Optional[str] = Field(None, description="User-friendly display name")
    description: Optional[str] = Field(None, description="Description of the model's capabilities")
    is_active: Optional[bool] = Field(None, description="Whether the model is available for use")
    is_default: Optional[bool] = Field(None, description="Whether this should be the default model")

class AIModelResponse(BaseModel):
    """Schema for AI model response data.

    Attributes:
        model_name: Technical name of the AI model
        display_name: User-friendly display name
        description: Description of the model's capabilities
        is_active: Whether the model is available for use
        is_default: Whether this is the default model
    """
    model_name: str
    display_name: str
    description: str
    is_active: bool
    is_default: bool

class ContentVersionMetadata(BaseModel):
    """Content version metadata model.

    Attributes:
        id: Unique identifier for the version
        project_id: ID of the project this version belongs to
        content_type: Type of content (outline or summaries)
        created_at: Timestamp when the version was created
        created_by: ID of the user who created the version
        is_active: Whether this is the active version
        description: Optional description of the version
    """
    id: str
    project_id: str
    content_type: str
    created_at: datetime
    created_by: str
    is_active: bool
    description: Optional[str] = None

class ContentVersionsList(BaseModel):
    """List of content versions with metadata.

    Attributes:
        versions: List of version metadata
        active_version_id: ID of the currently active version
    """
    versions: List[ContentVersionMetadata]
    active_version_id: Optional[str] = None

class ContentVersion(BaseModel):
    """Content version with metadata and content.

    Attributes:
        metadata: Version metadata
        content: The actual content of the version
    """
    metadata: ContentVersionMetadata
    content: Dict[str, Any]

    @field_validator('content', mode='before')
    @classmethod
    def ensure_content_is_dict(cls, v):
        """Ensure content is always a dictionary.

        This validator handles cases where content might be a string or other type,
        converting it to a dictionary format that matches the expected schema.
        """
        if isinstance(v, dict):
            return v
        elif isinstance(v, str):
            # For string content, always use "content" key for consistency
            return {"content": v}
        else:
            # For any other type, convert to string and wrap in dict
            return {"content": str(v)}

class DeletedVersionInfo(BaseModel):
    """Information about a deleted version.

    Attributes:
        id: ID of the deleted version
        content_type: Type of content that was deleted
        created_at: When the version was originally created
        description: Description of the deleted version
    """
    id: str
    content_type: str
    created_at: datetime
    description: Optional[str] = None

class DeleteVersionResponse(BaseModel):
    """Response for version deletion.

    Attributes:
        success: Whether the deletion was successful
        message: Success message
        deleted_version: Information about the deleted version
        promoted_version: Information about the version promoted to active (if any)
    """
    success: bool
    message: str
    deleted_version: DeletedVersionInfo
    promoted_version: Optional[DeletedVersionInfo] = None

class ResponseContentOutline(BaseModel):
    """Model for an outline used in response generation.

    Attributes:
        name: Name of the outline
        type: Type of outline ("system_generated" or "user_uploaded")
        content: Outline content text
    """
    name: str = Field(..., description="Name of the outline")
    type: str = Field(..., description="Type of outline: 'system_generated' for system-generated or 'user_uploaded' for user-uploaded")
    content: Optional[str] = Field(None, description="Outline content text")

class OutlinesResponse(BaseModel):
    """Response model for the get project outlines endpoint.

    Attributes:
        outlines: List of available outlines
    """
    outlines: List[ResponseContentOutline] = Field(..., description="List of available outlines for response generation")

class ResponseContent(BaseModel):
    """Model for generated response content.

    Attributes:
        content: The generated response text
        outline_type: Type of outline used ("system_generated", "user_uploaded", or "custom_outline")
        data_library_files: List of file IDs from data library
        input_tokens: Count of input tokens used
        output_tokens: Count of output tokens generated
    """
    content: str
    outline_type: str  # "system_generated", "user_uploaded", or "custom_outline"
    data_library_files: List[str] = []
    input_tokens: int = 0
    output_tokens: int = 0

class ResponseGenerationInput(BaseModel):
    """Input model for response content generation.

    Attributes:
        outline_type: Type of outline ("system_generated", "user_uploaded", or "custom_outline")
        outline_content: Content of the outline or custom outline
        data_library_files: Optional list of file IDs to include
        project_id: ID of the project
        model_name: Name of the LLM model to use
    """
    outline_type: str  # "system_generated", "user_uploaded", or "custom_outline"
    outline_content: str
    data_library_files: List[str] = []
    project_id: str = ""
    model_name: str = ""



class ResponseGenerationRequest(BaseModel):
    """Request model for generating response content.

    Attributes:
        outline_type: Type of outline ("system_generated", "user_uploaded", or "custom_outline")
        custom_outline: Custom outline text (required when outline_type is "custom_outline")
        data_library_files: Optional list of data library files to include (dummy implementation)
    """
    outline_type: str  # "system_generated", "user_uploaded", or "custom_outline"
    custom_outline: Optional[str] = None
    data_library_files: Optional[List[str]] = None

    @field_validator('custom_outline')
    @classmethod
    def validate_custom_outline(cls, v, info):
        """Validate that custom_outline is provided when outline_type is 'custom_outline'."""
        if info.data.get('outline_type') == 'custom_outline' and not v:
            raise ValueError("custom_outline is required when outline_type is 'custom_outline'")
        return v
