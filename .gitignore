# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
.venv/
.env/
*.venv
*.env
myenv/
proposal_env/
.python-version

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Caches
*.cache
.cache/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.mypy_cache/
.ruff_cache/
nosetests.xml
coverage.xml
*.cover

# IDE and Editor files
.idea/
.vscode/
*.swp
*.swo
*~
.*.sw*
.project
.pydevproject
.settings
.classpath
.factorypath

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
vector_dbs/
*.ipynb
.ipynb_checkpoints/

# Jupyter Notebook
.ipynb_checkpoints

# Local configuration
instance/
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.sqlite
*.db


.specstory