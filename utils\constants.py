"""Constants Module

This module contains all constants used throughout the application, including:
- File paths and folder names
- Configuration keys
- Default values
- Status codes and flags
- Other application-wide constants

All constants should be defined here to maintain a single source of truth.
"""

# GCP Storage Paths
class StoragePaths:
    """Constants related to Google Cloud Storage paths and file naming."""

    # Project structure
    PROJECT_PREFIX_TEMPLATE = "Projects/{project_name}_{project_id}/"

    # Main content files
    OUTLINE_FILE = "outline.json"
    SUMMARIES_FILE = "summaries.json"
    CONTENT_REVIEW_FILE = "content_review.json"
    GENERATED_PROPOSAL_SPECIFIC_EVALUATION_PARAMETERS_FILE = "generated_proposal_specific_evaluation_parameters.json"

    # History folders
    HISTORY_FOLDER = "history/"
    OUTLINE_HISTORY_FOLDER = f"{HISTORY_FOLDER}outline/"
    SUMMARIZATIONS_HISTORY_FOLDER = f"{HISTORY_FOLDER}summarizations/"
    RESPONSE_CONTENT_HISTORY_FOLDER = f"{HISTORY_FOLDER}response_content/"

    # History file templates
    OUTLINE_HISTORY_TEMPLATE = f"{OUTLINE_HISTORY_FOLDER}outline_{{timestamp}}.json"
    SUMMARIES_HISTORY_TEMPLATE = f"{SUMMARIZATIONS_HISTORY_FOLDER}summaries_{{timestamp}}.json"
    RESPONSE_CONTENT_HISTORY_TEMPLATE = f"{RESPONSE_CONTENT_HISTORY_FOLDER}response_content_{{timestamp}}.json"

    # Placeholder files
    PLACEHOLDER_SUFFIX = ".placeholder"

    # Document storage
    DOCUMENTS_FOLDER = "documents/"
    PROCESSED_DOCUMENTS_FOLDER = "processed_documents/"
    VECTOR_DB_FOLDER = "vector_db/"

    # Content Review
    CONTENT_REVIEW_RULEBOOK_FOLDER = "CONTENT_REVIEW_RULEBOOK/"
    CONTENT_REVIEW_RULEBOOK_FILE = "content_review_rulebook.json"
    CONTENT_REVIEW_USER_SPECIFIC_FOLDER = "content_review/"
    GENERATED_CONTENT_REVIEW_FILE = "generated_content_review.json"
    


    # Response content storage
    RESPONSE_CONTENT_FILE = "response_content.json"

# MongoDB Collection Names
class Collections:
    """MongoDB collection names used in the application."""

    USERS = "users"
    PROJECTS = "projects"
    VERIFICATION_TOKENS = "verification_tokens"
    PASSWORD_RESET_TOKENS = "password_reset_tokens"
    CONTENT_VERSIONS = "content_versions"
    APP_CONFIG = "app_config"

# Version Management
class VersionManagement:
    """Constants related to version management."""

    MAX_VERSIONS = 3
    TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
    DISPLAY_TIMESTAMP_FORMAT = "%b %d, %Y at %I:%M %p"  # Format: "Jun 18, 2025 at 11:05 AM"
    ACTIVE_FLAG = "is_active"

# Content Types
class ContentTypes:
    """Content type identifiers used throughout the application."""

    OUTLINE = "outline"
    SUMMARIES = "summaries"
    RESPONSE_CONTENT = "response_content"

# File Processing
class FileProcessing:
    """Constants related to file processing."""

    MIN_IMAGE_SIZE = 1024  # 1KB
    DEFAULT_TEMP_SUFFIX = ".pdf"
    IMAGE_PROCESSING_METHODS = ["vision", "tesseract"]
    DEFAULT_IMAGE_PROCESSING_METHOD = "vision"

# API Response Status
class ResponseStatus:
    """API response status constants."""

    SUCCESS = "success"
    ERROR = "error"

# Section Names
class Sections:
    """Standard section names used in the application."""

    PROPOSAL_INFO = "Proposal Info"
    BACKGROUND = "Background"
    SCOPE = "Scope"
    TASK_AREA = "Task Area"
    SUBMISSION_INSTRUCTIONS = "Submission Instructions"
    EVALUATION_FACTORS = "Evaluation Factors for Award"
    LIST_OF_ATTACHMENTS = "List of Attachments"
    KEY_PERSONNEL = "Key Personnel"
    RED_FLAGS = "Red Flags"

    # List of all standard sections
    ALL_SECTIONS = [
        PROPOSAL_INFO,
        BACKGROUND,
        SCOPE,
        TASK_AREA,
        SUBMISSION_INSTRUCTIONS,
        EVALUATION_FACTORS,
        LIST_OF_ATTACHMENTS,
        KEY_PERSONNEL,
        RED_FLAGS
    ]

# Document Tags
class DocumentTags:
    """Document tag constants for categorizing uploaded files."""

    # Core document tags
    MAIN_DOCUMENT = "Main Document"
    OUTLINE = "Outline"
    PAST_PERFORMANCE = "Past Performance"

    # Section-based tags (using existing Sections constants)
    PROPOSAL_INFO = Sections.PROPOSAL_INFO
    BACKGROUND = Sections.BACKGROUND
    SCOPE = Sections.SCOPE
    TASK_AREA = Sections.TASK_AREA
    SUBMISSION_INSTRUCTIONS = Sections.SUBMISSION_INSTRUCTIONS
    EVALUATION_FACTORS = Sections.EVALUATION_FACTORS
    LIST_OF_ATTACHMENTS = Sections.LIST_OF_ATTACHMENTS
    KEY_PERSONNEL = Sections.KEY_PERSONNEL
    RED_FLAGS = Sections.RED_FLAGS

    # List of all valid tags
    ALL_TAGS = [
        MAIN_DOCUMENT,
        PROPOSAL_INFO,
        BACKGROUND,
        SCOPE,
        TASK_AREA,
        SUBMISSION_INSTRUCTIONS,
        KEY_PERSONNEL,
        RED_FLAGS,
        EVALUATION_FACTORS,
        LIST_OF_ATTACHMENTS,
        PAST_PERFORMANCE,
        OUTLINE
    ]

# File Types
class FileTypes:
    """File type constants and extensions."""

    PDF = "pdf"
    DOCX = "docx"
    XLSX = "xlsx"
    XLS = "xls"

    # MIME types
    MIME_TYPES = {
        PDF: "application/pdf",
        DOCX: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        XLS: "application/vnd.ms-excel"
    }

    # List of supported file extensions
    SUPPORTED_EXTENSIONS = [PDF, DOCX, XLSX, XLS]

# AI Models
class AIModels:
    """AI model identifiers and configurations."""

    GEMINI_FLASH = "gemini-1.5-flash-001"
    GEMINI_FLASH_2 = "gemini-2.0-flash-exp"
    GEMINI_FLASH_2_5 = "gemini-2.5-flash-preview-04-17"

    # Display names
    MODEL_DISPLAY_NAMES = {
        GEMINI_FLASH: "Easy Bot",
        GEMINI_FLASH_2: "Smart Bot",
        GEMINI_FLASH_2_5: "Power Bot"
    }

    # Default model
    DEFAULT_MODEL = GEMINI_FLASH

# HTTP Status Codes
class HTTPStatus:
    """HTTP status codes used in API responses."""

    OK = 200
    CREATED = 201
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_SERVER_ERROR = 500

# API Endpoints
class APIEndpoints:
    """API endpoint paths."""

    # Auth endpoints
    LOGIN = "/auth/login"
    REGISTER = "/auth/register"
    VERIFY_EMAIL = "/auth/verify-email"
    RESET_PASSWORD = "/auth/reset-password"

    # Project endpoints
    PROJECTS = "/projects"
    PROJECT_DETAIL = "/projects/{project_id}"

    # Content endpoints
    SAVE_EDITED_CONTENT = "/projects/{project_id}/save-edited-content"
    LIST_CONTENT_VERSIONS = "/projects/{project_id}/content-versions"
    GET_CONTENT_VERSION = "/projects/{project_id}/content/{content_type}/version/{version_id}"

    # File endpoints
    UPLOAD_FILE = "/projects/{project_id}/upload-file"

    # Summary endpoints
    GENERATE_SUMMARY = "/projects/{project_id}/generate-summary"

    # Health check endpoints
    HEALTH = "/health"
    HEALTH_DETAILED = "/health/detailed"

    # Outline endpoints
    GENERATE_OUTLINE = "/projects/{project_id}/generate-outline"
    
    # Response Content endpoints
    RESPONSE_OUTLINES = "/projects/{project_id}/response/outlines"
    RESPONSE_GENERATE_OUTLINE = "/projects/{project_id}/response/generate-outline"
    RESPONSE_DATA_LIBRARY = "/projects/{project_id}/response/data-library"
    RESPONSE_GENERATE = "/projects/{project_id}/response/generate"
