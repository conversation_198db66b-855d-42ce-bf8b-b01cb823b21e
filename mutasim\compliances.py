import asyncio
import os
import pickle
import logging
from dotenv import load_dotenv
from langchain_google_vertexai import ChatVertexAI
from markitdown import MarkItDown
import pandas as pd
import re
from project_manager import ProjectManager
from utils.database import projects_collection
from utils.constants import DocumentTags

# Step 1: Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)

# Step 2: Initialize LLM
llm = ChatVertexAI(
    model_name="gemini-2.5-flash",
    temperature=0.3
)

# Step 4: Markdown Table Parser
def parse_markdown_table(md_text: str) -> pd.DataFrame:
    lines = md_text.strip().splitlines()
    table_lines = [line for line in lines if '|' in line]
    if len(table_lines) < 2:
        raise ValueError("Not enough lines for a valid markdown table.")
    header_line = table_lines[0].strip()
    alignment_line = table_lines[1].strip()
    if not re.fullmatch(r"\|[:\-| ]+\|", alignment_line):
        raise ValueError("Second line is not a valid markdown alignment line.")
    headers = [cell.strip() for cell in header_line.split('|')[1:-1]]
    rows = []
    for line in table_lines[2:]:
        cells = [cell.strip() for cell in line.strip().split('|')[1:-1]]
        while len(cells) < len(headers):
            cells.append("")
        rows.append(cells)
    return pd.DataFrame(rows, columns=headers)

# Step 5: Combined Prompt
def create_combined_prompt(rfp_content: str) -> str:
    return f"""
You are a proposal compliance expert.

From the RFP content provided below, extract and return **three markdown tables** using the exact structure, formatting, and headings defined below. Follow the rules and examples closely.

---

### Proposal Format  
Extract **only formatting instructions** (e.g., font size, margins, spacing, paper size)and provide them in seperate rows.  
Exclude proposal organization structure entirely.  
Ensure the **Section** column reflects the **exact solicitation section** from which the instruction was extracted.  
Strictly leave the “Notes” column blank.  

The sample below is provided for illustration only.

| Title | Section | Instructions | Notes |
|-------|---------|--------------|-------|
| 1. Page Size | L2 Quote Structure and Format Instructions | 8.5 x 11 inches sheet of paper containing information. Files shall use the following page setup parameters: margins top, bottom, left, right 1”; Gutter 0; header and footer 0.5” from edge. | |
| 2. Paragraph Spacing | L2 Quote Structure and Format Instructions | Pages shall be single spaced | |
| 3. Margins | L2 Quote Structure and Format Instructions | Margins top, bottom, left, right 1” | |
| 4. Font Size and Style Minimum | L2 Quote Structure and Format Instructions | The font type shall be Arial and the text size shall be no less than 12-point, except as noted below for tables, charts, graphs, and figures... | |

---

### Proposal Organization  
Extract the **structure of the proposal**, including volume breakdown, section names, and page limits.  
Include only page limits that are **explicitly mentioned in the same section** — do **not infer** or reuse from elsewhere.  
Strictly **provide similar structured output for all the volumes**.
Strictly leave the “Notes” column blank. 
 

The example below is illustrative.

| VOLUME | Solicitation Section | PG. LIMIT | Notes |
|--------|----------------------|-----------|-------|
| I – Contractual Documents | 1a. Cover Letter and Contractor Agreements | 3 | |
|  | 1b. Organizational Conflict of Interest (OCI) and Identification of Subcontractors | None | |
| II – Technical & Management Approach | 2a. Factor 1: Technical Approach | 20 | |
|  | 2b. Factor 2: Management Approach and Key Personnel | 10 pages for approach + 2 pages per resume | |
| III – Cost/Price Quote | 3a. Factor 3: Task Order Cost/Price Matrix | None | |
|  | 3b. Factor 3: Cost/Price Narrative | 3 | |
|  | 3c. GSA MAS Contract and GSA Catalog Pricing, to include Labor Categories, Labor Rates | None | |



---

### Compliance Matrix  
Only include sections and sub-sections that fall explicitly under each defined Volume (e.g., Volume I, Volume II, etc.) as structured in the RFP.
For each valid section and subsection in the RFP volumes, provide a single row containing both its **full instruction** and **evaluation criteria** (if available).    
Do **not split** instruction and evaluation into separate rows.
Leave the “Notes”, “Response Section”, and “Assigned to” columns blank.
Do not include the Proposal formatting instructions in it.

#### Formatting rules:
1. After the heading row, insert a row with the **Volume number and name** (e.g., “VOLUME I – TECHNICAL QUOTE”) centered, with all other cells blank.
2. Below each volume, group related **Solicitation Section Titles** (e.g., “Technical Quote”).
3. Under each section, list all corresponding **sub-sections** in the “Solicitation Sub - Section Title” column.
4. For each subsection, provide:

   - ** Section Number**:Use only the exact solicitation section number as written in the solicitation. Do not infer or create it. If not present, leave blank.
   - **Instruction**(verbatim from the RFP): Include all relevant instructions related to the given section or sub-section exactly as stated in the solicitation. Do not summarize, paraphrase, or omit any part of the instruction. 
   - ** Evaluation Criteria, if any statement in the RFP implies how that section will be reviewed, judged, scored, or assessed – even if not explicitly labeled as “Evaluation Criteria”. Copy that text exactly and do not summarize or interpret.**
   - ** If no such evaluation text is present, leave the Evaluation Criteria column blank.**
   - If multiple sub-sections within a Volume share the same Evaluation Criteria, do not repeat the evaluation text in each row.
       Instead, follow these steps:
       - Only include the full Evaluation Criteria text once, in the row of the first applicable sub-section.
       - For all other sub-sections that share the same Evaluation Criteria, in the Evaluation Criteria column, write: Refer to Evaluation Criteria in Section [Section Number](e.g., Refer to Evaluation Criteria in Section L.3.1). Make sure the section number you refer to corresponds to the first occurrence of that Evaluation Criteria in the Compliance Matrix, not in the original solicitation document.
5. Include an additional column called **"Agentic Validation"** in the Compliance Matrix table.  
This column must verify whether both the **Instruction** and the **Evaluation Criteria** are actually present in the source solicitation document.

Use only the following validation flags:
- `Instruction found`
- `Instruction missing`
- `Evaluation found`
- `Evaluation missing`

You may combine them as needed in the format:
- `"Instruction found; Evaluation found"`
- `"Instruction found; Evaluation missing"`
- `"Instruction missing; Evaluation found"`
- `"Instruction missing; Evaluation missing"`

This column helps confirm traceability and supports compliance checks.

Ensure:
- Each section appears only once.
- Only use actual sections from the RFP (do not invent or generalize).
- Include **all** sub-sections under each volume.
- You must copy the exact wording from the RFP.
- Do not rephrase, summarize, or paraphrase any part of the Instructions or Evaluation Criteria.
- Preserve the content verbatim as written in the solicitation.

The example below is illustrative:
| Solicitation Section Title | Solicitation Sub - Section Title | Solicitation Section No. | Instructions | Evaluation Criteria | Agentic Validation | Response Section | Assigned to | Notes |
|----------------------------|-----------------------------------|--------------------------|--------------|---------------------|--------------------------|------------------|-------------|-------|
| *VOLUME I – TECHNICAL QUOTE* |                                   |                          |              |                     |                          |                  |             |       |
| Technical Quote | Project Management Plan | ATTACHMENT 3 – EDFacts FITARA and FISMA Compliance Support | The technical quote shall provide a Project Management Plan. At a minimum, the plan shall describe methods the vendor will utilize to communicate results and advice and assistance to the COR... | Quotes will be evaluated based on the extent to which the vendor demonstrates that its management approach effectively addresses: Coordinating resources, minimizing turnover, documenting compliance, measuring performance, and managing transitions. | Instruction found; Evaluation found |  |  |  |
| Technical Quote | Staffing Management Plan | ATTACHMENT 3 – EDFacts FITARA and FISMA Compliance Support | The technical quote shall also provide a Staffing Management Plan (including proposed Key Personnel). | Quotes will be evaluated based on whether the organization has qualified personnel available at contract start and capable of addressing EDFacts FITARA and FISMA Compliance Support. | Instruction found; Evaluation found | | | |
| *VOLUME II – BUSINESS QUOTE* |                                   |                          |              |                     |                          |                  |             |       |
| Business Quote | Pricing Table | Section B | Provide a detailed pricing table in the format specified in Section B. | Evaluated for completeness, accuracy, and consistency with the pricing instructions. | Instruction found; Evaluation found | | | |


---

RFP Content:
{rfp_content}
"""


# Step 6: Extract Tables from Markdown Output
def extract_tables_from_markdown(markdown_text: str) -> dict:
    tables = {}
    current_title = None
    table_lines = []

    for line in markdown_text.splitlines():
        if line.strip().startswith("###"):
            if current_title and table_lines:
                tables[current_title] = "\n".join(table_lines).strip()
                table_lines = []
            current_title = line.strip().replace("###", "").strip()
        elif "|" in line:
            table_lines.append(line)

    if current_title and table_lines:
        tables[current_title] = "\n".join(table_lines).strip()

    return tables


async def get_project_files_content(project_id: str) -> str:
    """Retrieve actual project files content for compliance checking.

    This function retrieves the full content from project files (especially Main Document
    and other relevant tagged files) instead of just summary content.

    Args:
        project_id: ID of the project

    Returns:
        str: Combined content from all relevant project files

    Raises:
        Exception: If no files found or content retrieval fails
    """
    try:
        # Load the project
        project_manager = await ProjectManager.load_project(project_id)
        if not project_manager:
            raise Exception(f"Project {project_id} not found")

        # Get project document metadata from MongoDB to find relevant files
        project_doc = await projects_collection.find_one({"project_id": project_id})

        if not project_doc or "files_metadata" not in project_doc:
            raise Exception("No project document or files metadata found")

        # Step 1: Find Main Document (REQUIRED for compliance checking)
        main_document = None
        for file_metadata in project_doc.get("files_metadata", []):
            if DocumentTags.MAIN_DOCUMENT in file_metadata.get("tags", []):
                main_document = {
                    "filename": file_metadata.get("filename"),
                    "tags": file_metadata.get("tags", [])
                }
                break

        if not main_document:
            logging.warning("No Main Document found - using all available files for compliance")
            # If no main document, use all files
            files_to_process = []
            for file_metadata in project_doc.get("files_metadata", []):
                files_to_process.append({
                    "filename": file_metadata.get("filename"),
                    "tags": file_metadata.get("tags", [])
                })
        else:
            logging.info(f"Found Main Document: {main_document['filename']}")

            # Step 2: Find supplementary documents that are important for compliance
            supplementary_tags = [
                DocumentTags.TASK_AREA,
                DocumentTags.SUBMISSION_INSTRUCTIONS,
                DocumentTags.EVALUATION_FACTORS
            ]

            supplementary_files = {}
            for file_metadata in project_doc.get("files_metadata", []):
                file_tags = file_metadata.get("tags", [])
                for tag in supplementary_tags:
                    if tag in file_tags:
                        supplementary_files[tag] = {
                            "filename": file_metadata.get("filename"),
                            "tags": file_tags
                        }

            # Prepare files to process (Main Document + available supplementary files)
            files_to_process = [main_document]
            files_to_process.extend(supplementary_files.values())

        if not files_to_process:
            raise Exception("No files found to process for compliance checking")

        logging.info(f"Processing {len(files_to_process)} files for compliance: {[f['filename'] for f in files_to_process]}")

        # Process all available files and combine their content
        combined_content = []
        processed_files = []

        for file_info in files_to_process:
            filename = file_info["filename"]
            try:
                # Get the pickled content for each relevant file
                pickled_data = await project_manager.get_pickled_content(filename)
                if not pickled_data:
                    logging.warning(f"No pickled content found for {filename}, skipping")
                    continue

                logging.info(f"Reading pickled content for {filename} (tags: {file_info['tags']})")

                # Unpickle the content and process using the same logic as other content retrieval functions
                sections = pickle.loads(pickled_data)
                processed_files.append(filename)

                # Process sections for this file
                file_content = []
                if isinstance(sections, list):
                    for section in sections:
                        if isinstance(section, dict):
                            # Handle different dictionary structures
                            if 'text' in section:
                                file_content.append(section['text'])
                            elif 'content' in section:
                                file_content.append(section['content'])
                            # Handle PDF page structure
                            elif 'page_number' in section and 'content' in section:
                                file_content.append(f"Page {section['page_number']}: {section['content']}")
                        elif isinstance(section, str):
                            file_content.append(section)
                # If sections is a string, add it directly
                elif isinstance(sections, str):
                    file_content.append(sections)
                # Handle ProjectDocument object
                elif hasattr(sections, 'sections') and isinstance(sections.sections, list):
                    for page in sections.sections:
                        if isinstance(page, dict) and 'content' in page:
                            file_content.append(page['content'])

                # Add this file's content to the combined content
                if file_content:
                    combined_content.extend(file_content)
                    logging.info(f"Added {len(file_content)} content sections from {filename}")

            except Exception as e:
                logging.error(f"Error processing pickled content for {filename}: {str(e)}")
                continue

        if not processed_files:
            raise Exception("No files were successfully processed for compliance checking")

        # Check if we have any valid content (filter out error messages)
        valid_content = []
        for content in combined_content:
            # Skip error messages from PDF extraction
            if not content.strip().startswith("Error: No content could be extracted from this page"):
                valid_content.append(content)

        if not valid_content:
            raise Exception(f"No valid content found in processed files: {processed_files}")

        # Join the content
        combined_content_text = "\n\n".join(valid_content)

        logging.info(f"Retrieved {len(combined_content_text)} characters of content from {len(processed_files)} files: {processed_files}")

        return combined_content_text

    except Exception as e:
        logging.error(f"Error retrieving project files content for compliance: {str(e)}")
        raise Exception(f"Error retrieving project files content: {str(e)}")


async def generate_compliances(project_id: str):
    try:
        # 1. Get the actual project files content instead of summary content
        project_files_content = await get_project_files_content(project_id)

        print("Project files content length: %d characters", len(project_files_content))

        # 2. Create the combined prompt using the actual project files content
        prompt = create_combined_prompt(project_files_content)
        # 3. Run the prompt through the LLM
        response = llm.invoke(prompt)
        print("LLM raw response content:\n", response.content)

        # 4. Extract tables from the LLM's markdown output
        tables_md = extract_tables_from_markdown(response.content)
        print("Extracted tables (titles):", list(tables_md.keys()))
        for title, table_md in tables_md.items():
            print(f"\nTable: {title}\n{table_md}\n")

        # 5. Parse each table
        format_df = parse_markdown_table(tables_md.get("Proposal Format", ""))
        org_df = parse_markdown_table(tables_md.get("Proposal Organization", ""))
        compliance_df = parse_markdown_table(tables_md.get("Compliance Matrix", ""))
        # 6. Return as dicts for API use
        return {
            "proposal_format": format_df.to_dict(orient="records"),
            "proposal_organization": org_df.to_dict(orient="records"),
            "compliance_matrix": compliance_df.to_dict(orient="records"),
        }
    except Exception as e:
        print(f"Error in generate_compliances: {e}")
        logging.error(f"Error in generate_compliances for project {project_id}: {e}")
        return {"error": str(e)}

