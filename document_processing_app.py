"""Document Processing Application Module

This module provides functionality for processing and summarizing documents using LLMs (Language Learning Models).
It includes classes for document summarization, section management, and overall document processing workflow.

Key Components:
- DocumentSummarizer: Handles the summarization of individual document sections
- DocumentProcessingApp: Main application class that orchestrates the document processing workflow
- Document section handling with support for user guidelines and custom configurations
- Async processing capabilities for improved performance
- Integration with various LLM models (Vertex AI and Google Generative AI)

The module supports both batch processing of all sections and selective processing of specific sections.
"""

from langchain_google_vertexai import ChatVertexAI
from langchain_google_genai import GoogleGenerativeAI
from dataclasses import dataclass, field
import fitz
import logging
from typing import List, Dict, Optional
from utils import extract_sections, read_docx
from project_manager import ProjectManager
import asyncio
from fastapi import HTTPException
import pickle
from models import ProjectDocument, DocumentSection
from utils.llm_config import get_llm_instance
from utils.token_utils import count_tokens
from utils.constants import DocumentTags

# System prompt for section summarization
SECTION_SUMMARISATION_SYSTEM_PROMPT = """You are an experienced procurement developer who can summarize the specific content of procurements in just 6000 words. You will be tasked with drafting the summary of certain sections using the provided context and the summary you will draft will have all the critical details neccessary for making an understanding document of the procurement. Remember that the section summary you will produce has to be self contained and you are constrained in having a output word limit of 6000 words."""

class DocumentSummarizer:
    """Handles the summarization of document sections using LLM models.

    This class manages the process of selecting relevant documents for each section,
    generating summaries, and handling user guidelines for customization.
    """

    def __init__(self, sections, llm, project):
        """Initialize the DocumentSummarizer.

        Args:
            sections: List of document sections to process
            llm: Language model instance to use for summarization
            project: ProjectManager instance for project-related operations
        """
        self.sections = sections
        self.llm = llm
        self.project = project
        logging.info(f"Initialized DocumentSummarizer with {len(sections)} sections that can be generated.")

    def add_user_guidelines(self, section_title: str, guidelines: str):
        """Add or update user guidelines for a specific section.

        Args:
            section_title: Title of the section to update
            guidelines: New guidelines to apply to the section
        """
        for section in self.sections:
            if section.section_title == section_title:
                if section.user_guidelines != guidelines:
                    section.add_user_guidelines(guidelines)
                    logging.info(f"Added user guidelines for section: {section_title}")
                    break

    async def select_relevant_documents(self, section: DocumentSection, documents: List[ProjectDocument]) -> List[ProjectDocument]:
        """Select documents relevant to a specific section based on tags and content.

        Args:
            section: The section to find relevant documents for
            documents: List of all available documents

        Returns:
            List[ProjectDocument]: List of documents relevant to the section
        """
        logging.info(f"\nSelecting relevant documents for section: {section.section_title}")
        logging.info(f"Total available documents: {len(documents)}")
        for doc in documents:
            logging.info(f"Document: {doc.file_name}, Tags: {doc.tags}")

        relevant_docs = []

        # First pass: Get documents with no tags or Main Document tag
        for doc in documents:
            if not doc.tags or DocumentTags.MAIN_DOCUMENT in doc.tags:
                relevant_docs.append(doc)
                if not doc.tags:
                    logging.info(f"Selected - No tags: {doc.file_name}")
                else:
                    logging.info(f"Selected - {DocumentTags.MAIN_DOCUMENT}: {doc.file_name}")

        # Second pass: Get documents with matching section title
        for doc in documents:
            if doc not in relevant_docs:  # Skip if already added
                if section.section_title.lower() in [tag.lower() for tag in doc.tags]:
                    relevant_docs.append(doc)
                    logging.info(f"Selected - Matching tag: {doc.file_name} (Tags: {doc.tags})")

        if not relevant_docs:
            logging.warning(f"No relevant documents found for section {section.section_title}. Using all documents.")
            return documents

        logging.info(f"Selected {len(relevant_docs)} relevant documents for section {section.section_title}")
        return relevant_docs

    async def summarize_section(self, section, documents):
        """Generate a summary for a specific section using relevant documents.

        Args:
            section: The section to summarize
            documents: List of available documents

        Returns:
            DocumentSection: The section with generated summary

        Raises:
            HTTPException: If summary generation fails
        """
        logging.info(f"\nSummarizing section: {section.section_title}")

        # First load tags for all documents
        tag_tasks = []
        for doc in documents:
            tag_tasks.append(asyncio.create_task(
                self.project.get_document_tags_by_filename(doc.file_name)
            ))

        # Get all tags
        tags_list = await asyncio.gather(*tag_tasks)

        # Update document tags
        for doc, tags in zip(documents, tags_list):
            doc.tags = tags
            logging.info(f"Loaded tags for {doc.file_name}: {tags}")

        # Now get relevant documents with updated tags
        relevant_docs = await self.select_relevant_documents(section, documents)
        if not relevant_docs:
            logging.warning(f"No content found for section {section.section_title}")
            return section

        # Process each document's content
        content = []
        for doc in relevant_docs:
            # Use already loaded sections if available
            if hasattr(doc, 'sections') and doc.sections:
                content.append(doc.sections)
                logging.info(f"Using already loaded {len(doc.sections)} sections from {doc.file_name}")
            else:
                # Fall back to reading pickle or original file if sections not loaded
                logging.warning(f"No sections found for {doc.file_name}, loading from pickle")
                pickled_content = await self.project.get_pickled_content(doc.file_name)
                if pickled_content:
                    loaded_content = pickle.loads(pickled_content)
                    content.append(loaded_content)
                    if isinstance(loaded_content, list):
                        logging.info(f"Loaded {len(loaded_content)} sections from {doc.file_name}")
                    else:
                        logging.info(f"Loaded content from {doc.file_name}")
                else:
                    # Fall back to reading original file only if pickle doesn't exist
                    logging.warning(f"No pickle file found for {doc.file_name}, reading original file")
                    file_content = await self.project.get_uploaded_file(doc.file_name)
                    doc = await ProjectDocument.create(file_content, doc.file_name, doc.tags)
                    content.append(doc.sections)

        if not content:
            logging.warning(f"No content found for section {section.section_title}")
            return section

        # Generate prompt and count input tokens
        prompt = section.generate_prompt(content)
        input_tokens = count_tokens(SECTION_SUMMARISATION_SYSTEM_PROMPT + prompt)
        logging.info(f"Input tokens for section {section.section_title}: {input_tokens}")

        try:
            # Check if the model is experimental
            if isinstance(self.llm, GoogleGenerativeAI):
                messages = [
                            ("system", SECTION_SUMMARISATION_SYSTEM_PROMPT),
                            ("human", prompt),
                        ]
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.llm.invoke(messages)
                )
                output_tokens = count_tokens(response)
            else:
                messages = [
                            ("system", SECTION_SUMMARISATION_SYSTEM_PROMPT),
                            ("human", prompt),
                        ]
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.llm.invoke(messages, **section.config_settings)
                )
                response_content = response.content
                output_tokens = count_tokens(response_content)
                response = response_content

            logging.info(f"Output tokens for section {section.section_title}: {output_tokens}")
            section.set_response(response, input_tokens, output_tokens)
            return section
        except Exception as e:
            logging.error(f"Error generating summary for section {section.section_title}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate summary for section {section.section_title}: {str(e)}"
            )

    async def summarize(self, documents: List[ProjectDocument]) -> List[DocumentSection]:
        """Summarize all sections using the provided documents.

        Args:
            documents: List of documents to process

        Returns:
            List[DocumentSection]: List of summarized sections
        """
        summarized_sections = []
        for section in self.sections:
            summarized_section = await self.summarize_section(section, documents)
            summarized_sections.append(summarized_section)
        logging.info(f"Summarized {len(summarized_sections)} sections")
        return summarized_sections

    async def summarize_specific(self, documents: List[ProjectDocument], specific_sections: List[DocumentSection]) -> List[DocumentSection]:
        """Summarize specific sections using the provided documents.

        Args:
            documents: List of documents to process
            specific_sections: List of sections to summarize

        Returns:
            List[DocumentSection]: List of summarized sections
        """
        summarized_sections = []
        for section in specific_sections:
            summarized_section = await self.summarize_section(section, documents)
            summarized_sections.append(summarized_section)
        logging.info(f"Summarized {len(summarized_sections)} specific sections")
        return summarized_sections

class DocumentProcessingApp:
    """Main application class for document processing and summarization.

    This class orchestrates the entire document processing workflow, including:
    - Document management
    - Section configuration
    - LLM model initialization
    - Summary generation
    - Project state management
    """

    def __init__(self, documents: List[ProjectDocument], project: ProjectManager, sections: List[Dict] = None):
        """Initialize the DocumentProcessingApp.

        Args:
            documents: List of documents to process
            project: ProjectManager instance for project management
            sections: Optional list of section configurations
        """
        self.documents = documents
        self.project = project
        self.sections = sections or []  # Use provided sections or empty list

        # Initialize LLM using centralized config
        self.llm = get_llm_instance(model_name=project.model_name)
        logging.info(f"Initialized LLM with model: {project.model_name}")

        # Convert dictionary sections to DocumentSection objects
        document_sections = []
        for section in self.sections:
            doc_section = DocumentSection(
                section_title=section.get('section_title', ''),
                section_prompt=section.get('section_prompt', ''),
                source=section.get('source', ''),
                model_name=section.get('model_name', 'gemini-1.5-flash-001'),
                config_settings=section.get('config_settings', {
                    'temperature': 0.0,
                    'top_p': 0.95,
                    'top_k': 40,
                    'max_output_tokens': 8000
                }),
                user_guidelines=section.get('user_guidelines', '')
            )
            document_sections.append(doc_section)

        self.summarizer = DocumentSummarizer(document_sections, self.llm, self.project)
        logging.info(f"Initialized DocumentProcessingApp with {len(documents)} documents and {len(self.sections)} sections")

    def add_user_guidelines(self, section_title: str, guidelines: str):
        """Add or update user guidelines for a specific section.

        Args:
            section_title: Title of the section to update
            guidelines: New guidelines to apply
        """
        self.summarizer.add_user_guidelines(section_title, guidelines)
        self.project.update_section(section_title, {'user_guidelines': guidelines})

    def update_project_section(self, section_title: str, updates: Dict):
        """Update a section's configuration in the project.

        Args:
            section_title: Title of the section to update
            updates: Dictionary of updates to apply
        """
        self.project.update_section(section_title, updates)

    def add_project_response(self, section_title: str, response: str):
        """Add a response to a section in the project.

        Args:
            section_title: Title of the section
            response: Response content to add
        """
        self.project.add_response(section_title, response)

    def save_project(self):
        """Save the current state of the project."""
        self.project.save_project()

    async def run(self, specific_sections: Optional[List[str]] = None):
        """Run the document processing workflow.

        Args:
            specific_sections: Optional list of specific sections to process
        """
        all_summaries = []
        for document in self.documents:
            if specific_sections:
                summarized_sections = await self.summarize_specific_sections(self.documents, specific_sections)
            else:
                summarized_sections = await self.summarizer.summarize(self.documents)
            all_summaries.extend(summarized_sections)
        self.write_to_markdown_file(all_summaries)
        logging.info(f"Completed summarization for {len(self.documents)} documents")

    async def summarize_specific_sections(self, documents: List[ProjectDocument], specific_sections: List[str]) -> List[DocumentSection]:
        """Summarize specific sections from the documents.

        Args:
            documents: List of documents to process
            specific_sections: List of section titles to summarize

        Returns:
            List[DocumentSection]: List of summarized sections
        """
        summarized_sections = []
        for section in self.summarizer.sections:
            if section.section_title in specific_sections:
                summarized_section = await self.summarize_section(section, documents)
                summarized_sections.append(summarized_section)
        logging.info(f"Summarized {len(summarized_sections)} specific sections")
        return summarized_sections

    async def summarize_section(self, section: DocumentSection, documents: List[ProjectDocument]) -> DocumentSection:
        """Summarize a single section using the provided documents.

        Args:
            section: Section to summarize
            documents: List of documents to process

        Returns:
            DocumentSection: Summarized section
        """
        try:
            summarized_section = await self.summarizer.summarize_section(section, documents)
            logging.info(f"Generated summary for section {section.section_title}")
            return summarized_section

        except Exception as e:
            logging.error(f"Error summarizing section {section.section_title}: {str(e)}")
            section.final_response = f"Error generating summary: {str(e)}"
            return section

def initialize_app(documents: List[ProjectDocument], project_manager: ProjectManager) -> DocumentProcessingApp:
    """Initialize the document processing application.

    Args:
        documents: List of documents to process
        project_manager: ProjectManager instance for project management

    Returns:
        DocumentProcessingApp: Initialized application instance
    """
    # Get all available sections from the project
    available_sections = project_manager.project_data.get("sections", [])

    # Create DocumentProcessingApp instance with all available sections
    app = DocumentProcessingApp(
        documents=documents,
        project=project_manager,
        sections=available_sections
    )

    logging.info(f"Initialized DocumentProcessingApp with {len(documents)} documents and {len(available_sections)} available sections")
    return app
