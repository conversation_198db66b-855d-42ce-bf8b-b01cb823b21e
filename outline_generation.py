"""RFP Outline Generation Module

This module handles the generation of structured outlines from RFP (Request for Proposal) documents
using Language Learning Models. It extracts submission instructions, evaluation criteria,
and creates a hierarchical outline for proposal responses.

Features:
- Automated outline generation from RFP documents
- Integration with multiple LLM models (Vertex AI and Google Generative AI)
- Structured prompt engineering for consistent results
- Evaluation criteria integration
- Hierarchical markdown formatting
- Async processing support
"""

from langchain_google_vertexai import ChatVertexAI
from langchain_google_genai import GoogleGenerativeAI
from dotenv import load_dotenv
import os
import logging
import asyncio
from fastapi import HTTPException
from utils.llm_config import get_llm_instance
from utils.token_utils import count_tokens
from utils.constants import HTTPStatus
from RAG.rfp_prompts import get_outline_prompt


# System prompt for outline generation
OUTLINE_GENERATION_SYSTEM_PROMPT = """You are a skilled procurement analyst tasked with creating a detailed outline of procurement response requirements having word count below 6000 words. Extract the structure from the "Instructions for Submission" section, populate each section with high-level notes on required content, and integrate relevant evaluation criteria. Ensure the outline is clear, self-contained, and follows a hierarchical markdown format with all necessary sections, subsections, and referenced content included. Remember You are contrained in having a output word limit of 6000 words, so generate the outline carefully taking into conisderation the 6000 word limit."""

# Configure module logger
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()


async def generate_outline(rfp_context, model_name):
    """Generate a structured outline from RFP content using an LLM.

    This function processes the RFP content using the specified LLM model to create
    a structured outline that includes submission requirements and evaluation criteria.

    Args:
        rfp_context: Content of the RFP document
        model_name: Name of the LLM model to use

    Returns:
        str: Generated outline in markdown format

    Raises:
        ValueError: If LLM initialization fails
        HTTPException: If outline generation fails
    """
    # Configure LLM settings for outline generation
    generation_config = {
        # "temperature": 0.0,      # Use deterministic output
        "max_output_tokens": 8000,  # Allow for detailed outlines
    }

    # Initialize the LLM with specified model and configuration
    llm = get_llm_instance(
        model_name=model_name,
        custom_config=generation_config
    )

    if llm is None:
        raise ValueError("LLM must be provided to generate outline")

    logger.info("Generating outline...")
    logger.info("Model Name: " + model_name)

    # Generate the outline prompt
    rfp_outline_prompt = get_outline_prompt(rfp_context)
    input_tokens = count_tokens(rfp_outline_prompt)
    logging.info(f"The input token count for the outline is: {input_tokens}")

    try:
        # Run the LLM operation in a thread pool to prevent blocking
        loop = asyncio.get_event_loop()
        if isinstance(llm, GoogleGenerativeAI):
            # For experimental models, invoke and get the response directly
            response = await loop.run_in_executor(
                None,
                lambda: llm.invoke(rfp_outline_prompt)
            )
            output_tokens = count_tokens(response)
            logging.info(f"The output token count of the outline is: {output_tokens}")

            # Return the raw response for experimental models
            return response, input_tokens, output_tokens  # No need for response.content
        else:
            # For standard models, invoke and get the structured response
            response = await loop.run_in_executor(
                None,
                lambda: llm.invoke(rfp_outline_prompt, generation_config=generation_config)
            )
            output_tokens = count_tokens(response.content)
            return response.content, input_tokens, output_tokens  # Access content for standard models
    except Exception as e:
        logger.error(f"Error generating outline: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate outline: {str(e)}"
        )