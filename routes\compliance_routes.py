# This router must be imported and included in main.py as:
# from routes import compliance_routes
# app.include_router(compliance_routes.router, tags=["Compliance"])

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from mutasim.compliances import generate_compliances
from utils.database import compliances_collection  # <-- Add this import
from pymongo.errors import PyMongoError

router = APIRouter()

@router.post("/projects/{project_id}/generate-compliances", response_model=Dict[str, Any])
async def generate_compliances_route(project_id: str):
    """Generate compliance tables for a given project ID and save to DB."""
    try:
        result = await generate_compliances(project_id)
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        # Save to DB (upsert by project_id)
        try:
            await compliances_collection.update_one(
                {"project_id": project_id},
                {"$set": {"project_id": project_id, "compliances": result}},
                upsert=True
            )
            # Debug: Print the saved document
            saved = await compliances_collection.find_one({"project_id": project_id})
            print("Saved compliance doc:", saved)
        except PyMongoError as db_err:
            raise HTTPException(status_code=500, detail=f"DB error: {str(db_err)}")
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/compliances", response_model=Dict[str, Any])
async def get_all_compliances():
    """Get all compliances from the DB."""
    print("Getting all compliances from the DB")
    try:
        compliances_cursor = compliances_collection.find({}, {"_id": 0})
        compliances = []
        async for doc in compliances_cursor:
            compliances.append(doc)
        print(f"Number of compliances found: {len(compliances)}")
        # Always return a list, even if empty
        return {"compliances": compliances}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch compliances: {str(e)}")

@router.get("/projects/{project_id}/compliances", response_model=Dict[str, Any])
async def get_project_compliances(project_id: str):
    """Get compliances for a specific project."""
    print(f"Getting compliances for project {project_id}")
    try:
        compliance_doc = await compliances_collection.find_one(
            {"project_id": project_id},
            {"_id": 0}
        )
        if not compliance_doc:
            raise HTTPException(status_code=404, detail=f"No compliances found for project {project_id}")
        return compliance_doc
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch compliances: {str(e)}")
