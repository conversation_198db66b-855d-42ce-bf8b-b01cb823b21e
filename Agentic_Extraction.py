from crew import *
from utils import is_relevant_document, run_relevant_agent_sync
import logging
import asyncio
from concurrent.futures import ProcessPoolExecutor
from typing import List, Dict, Any
from utils.constants import Sections


SECTIONS_TO_EXTRACT = [
    Sections.PROPOSAL_INFO,
    Sections.BACKGROUND,
    Sections.SCOPE,
    Sections.TASK_AREA,
    Sections.SUBMISSION_INSTRUCTIONS,
    Sections.EVALUATION_FACTORS,
    Sections.LIST_OF_ATTACHMENTS,
    Sections.KEY_PERSONNEL
]


# SECTIONS_TO_EXTRACT = ["Background", "Scope", "Key Personnel"]

async def process_section(section: str, processed_file_content_docs: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Process a single section asynchronously."""
    logging.info(f"Processing for section {section}")
    filtered_relevant_content = []

    for processed_file_content_doc in processed_file_content_docs:
        is_relevant = is_relevant_document(section, processed_file_content_doc['tags'])
        if is_relevant:
            logging.info(f"{processed_file_content_doc['file_name']} is a relevant document for extracting content for {section}.")
            filtered_relevant_content.append(processed_file_content_doc['content'])
        else:
            logging.info(f"{processed_file_content_doc['file_name']} is not a relevant document for extracting content for {section}.")

    logging.info(f"starting agentic extraction for {section} with {len(filtered_relevant_content)} docs being used.")

    # Create a ProcessPoolExecutor for this specific task
    executor = ProcessPoolExecutor(max_workers=1)
    try:
        # Run CPU-intensive task in the executor asynchronously
        result = await asyncio.get_event_loop().run_in_executor(
            executor,
            run_relevant_agent_sync,  # Use the sync version
            section,
            "Extraction",  # Purpose of the agent
            filtered_relevant_content,
            ""  # Guidelines will be empty here
        )
        # Log the result structure for debugging
        # logging.info(f"Result type: {type(result)}")
        # logging.info(f"Result structure: {result}")

        # Handle the result based on its structure
        if isinstance(result, tuple):
            # If result is a tuple, assume first element is content and second is token usage
            if len(result) >= 2:
                content = result[0]
                token_usage = result[1]
            else:
                content = result[0] if result else ""
                token_usage = None
        else:
            content = result
            token_usage = None

        return {section: (content, token_usage)}
    finally:
        # Ensure the executor is properly shutdown
        executor.shutdown(wait=False)

async def extract_content_for_each_section(processed_file_content_docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Extract content for all sections concurrently."""
    tasks = []
    for section in SECTIONS_TO_EXTRACT:
        task = asyncio.create_task(process_section(section, processed_file_content_docs))
        tasks.append(task)

    # Wait for all tasks to complete
    extracted_section_content = await asyncio.gather(*tasks)
    return extracted_section_content




