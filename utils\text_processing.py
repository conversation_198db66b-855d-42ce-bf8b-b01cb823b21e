"""Text Processing Utilities Module

This module provides functionality for processing and manipulating text content, including:
- Markdown cleaning and formatting
- JSON parsing and extraction
- Text filtering and transformation

Key Features:
- Markdown header cleaning
- JSON proposal info parsing
- Metadata filtering
- Format conversion
"""

import logging
import re
import json
from typing import Any


def format_markdown_table(text: str) -> str:
    """
    Format markdown tables to ensure proper structure and alignment.

    This function:
    - Ensures proper spacing around pipe separators
    - Adds missing header separators
    - Aligns columns consistently
    - Removes extra whitespace within cells
    - Handles malformed table structures

    Args:
        text (str): Text content that may contain markdown tables

    Returns:
        str: Text with properly formatted markdown tables
    """
    if not text or '|' not in text:
        return text

    lines = text.split('\n')
    formatted_lines = []
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        # Check if this line looks like a table row (contains pipes)
        if '|' in line and line.count('|') >= 2:
            # Found potential table start
            table_lines = []

            # Collect all consecutive table lines
            while i < len(lines) and '|' in lines[i]:
                table_lines.append(lines[i].strip())
                i += 1

            # Process the table if we have at least 2 lines
            if len(table_lines) >= 2:
                formatted_table = _format_table_block(table_lines)
                formatted_lines.extend(formatted_table)
            else:
                # Not a proper table, add lines as-is
                formatted_lines.extend(table_lines)
        else:
            formatted_lines.append(line)
            i += 1

    return '\n'.join(formatted_lines)


def _format_table_block(table_lines: list) -> list:
    """
    Format a block of table lines into proper markdown table format.

    Args:
        table_lines (list): List of table row strings

    Returns:
        list: List of properly formatted table row strings
    """
    if not table_lines:
        return table_lines

    # Parse all rows into cells
    parsed_rows = []
    max_cols = 0

    for line in table_lines:
        # Remove leading/trailing pipes and split by pipe
        line = line.strip()
        if line.startswith('|'):
            line = line[1:]
        if line.endswith('|'):
            line = line[:-1]

        cells = [cell.strip() for cell in line.split('|')]
        parsed_rows.append(cells)
        max_cols = max(max_cols, len(cells))

    # Ensure all rows have the same number of columns
    for row in parsed_rows:
        while len(row) < max_cols:
            row.append('')

    # Calculate column widths for alignment
    col_widths = [0] * max_cols
    for row in parsed_rows:
        for j, cell in enumerate(row):
            col_widths[j] = max(col_widths[j], len(cell))

    # Ensure minimum width of 3 for separator row
    col_widths = [max(width, 3) for width in col_widths]

    formatted_rows = []

    # Format header row (first row)
    if parsed_rows:
        header_cells = [cell.ljust(col_widths[j]) for j, cell in enumerate(parsed_rows[0])]
        formatted_rows.append('| ' + ' | '.join(header_cells) + ' |')

        # Add separator row if not present or malformed
        if len(parsed_rows) < 2 or not all(cell.strip() in ['', '-', '--', '---', '----', '-----'] or
                                          set(cell.strip()) <= {'-', ':', ' '} for cell in parsed_rows[1]):
            separator_cells = ['-' * col_widths[j] for j in range(max_cols)]
            formatted_rows.append('| ' + ' | '.join(separator_cells) + ' |')
            data_start = 1
        else:
            # Format existing separator row
            separator_cells = []
            for j in range(max_cols):
                if j < len(parsed_rows[1]):
                    cell = parsed_rows[1][j].strip()
                    if ':' in cell:
                        # Preserve alignment indicators
                        if cell.startswith(':') and cell.endswith(':'):
                            separator_cells.append(':' + '-' * (col_widths[j] - 2) + ':')
                        elif cell.startswith(':'):
                            separator_cells.append(':' + '-' * (col_widths[j] - 1))
                        elif cell.endswith(':'):
                            separator_cells.append('-' * (col_widths[j] - 1) + ':')
                        else:
                            separator_cells.append('-' * col_widths[j])
                    else:
                        separator_cells.append('-' * col_widths[j])
                else:
                    separator_cells.append('-' * col_widths[j])
            formatted_rows.append('| ' + ' | '.join(separator_cells) + ' |')
            data_start = 2

        # Format data rows
        for row in parsed_rows[data_start:]:
            data_cells = [cell.ljust(col_widths[j]) for j, cell in enumerate(row)]
            formatted_rows.append('| ' + ' | '.join(data_cells) + ' |')

    return formatted_rows

def clean_text_content(text: str, mode: str = 'markdown', return_json: bool = False) -> Any:
    """
    Unified text processing function that handles various text cleaning operations.

    Args:
        text (str): The text content to clean
        mode (str): Processing mode - 'markdown', 'header', 'json', or 'auto'
            - 'markdown': Remove markdown code fences, clean HTML/comments, and format tables
            - 'header': Find ## headers and clean content after them
            - 'json': Extract and parse JSON from code blocks
            - 'auto': Automatically detect and apply appropriate cleaning including table formatting
        return_json (bool): If True and JSON is found, return parsed JSON object

    Returns:
        str or Any: Cleaned text string with properly formatted markdown tables,
                   or parsed JSON object if return_json=True

    Raises:
        ValueError: If mode='json' or return_json=True but no valid JSON is found

    New Features:
        - Markdown table formatting: Ensures proper spacing, alignment, and structure
        - Automatic header separator insertion for malformed tables
        - Column width calculation and consistent alignment
        - Preservation of table alignment indicators (:)
    """
    if not text or not isinstance(text, str):
        logging.debug(f"clean_text_content: Received empty or non-string input: {type(text)}")
        return text

    # Log initial state
    original_length = len(text)
    logging.info(f"clean_text_content: Starting processing - mode='{mode}', return_json={return_json}, text_length={original_length}")
    logging.debug(f"clean_text_content: First 100 chars: {repr(text[:100])}")

    processing_steps = []  # Track what processing steps are applied

    # Handle header mode - find ## and process from there
    if mode == 'header':
        header_pos = text.find('##')
        if header_pos > 0:
            old_length = len(text)
            text = text[header_pos:]
            processing_steps.append(f"header_extraction: removed {old_length - len(text)} chars before ##")
            logging.debug(f"clean_text_content: Header mode - found ## at position {header_pos}, trimmed to {len(text)} chars")
        else:
            logging.debug("clean_text_content: Header mode - no ## found, keeping full text")

    # Handle JSON extraction mode or auto-detection
    if mode == 'json' or (mode == 'auto' and '```json' in text) or return_json:
        logging.debug(f"clean_text_content: JSON processing triggered - mode='{mode}', has_json_block={'```json' in text}, return_json={return_json}")

        # Use regex to match content between ```json and ```
        json_match = re.search(r'```json\s*(.*?)\s*```', text, re.DOTALL)
        if json_match:
            json_content = json_match.group(1)
            processing_steps.append("json_extraction: found and extracted JSON from code block")
            logging.info(f"clean_text_content: Found JSON block with {len(json_content)} characters")

            try:
                parsed_json = json.loads(json_content)
                processing_steps.append("json_parsing: successfully parsed JSON")
                logging.info(f"clean_text_content: Successfully parsed JSON with {len(parsed_json) if isinstance(parsed_json, dict) else 'N/A'} fields")
                logging.debug(f"clean_text_content: Processing steps completed: {processing_steps}")
                return parsed_json
            except json.JSONDecodeError as e:
                processing_steps.append(f"json_parsing: failed - {str(e)}")
                logging.warning(f"clean_text_content: JSON parsing failed: {str(e)}")
                if mode == 'json':
                    raise ValueError(f"Invalid JSON content found in code block: {str(e)}")
                elif return_json:
                    # If return_json is True but JSON is invalid, fall through to text processing
                    logging.debug("clean_text_content: JSON parsing failed, continuing with text processing")
                    pass
        elif mode == 'json':
            logging.error("clean_text_content: JSON mode but no JSON code block found")
            raise ValueError("No JSON code block found in the provided text.")
        elif return_json and '```json' not in text:
            # If return_json is True but no JSON block found, fall through to text processing
            logging.debug("clean_text_content: return_json=True but no JSON block found, continuing with text processing")
            pass

    # Strip whitespace
    old_length = len(text)
    text = text.strip()
    if len(text) != old_length:
        chars_removed = old_length - len(text)
        processing_steps.append(f"whitespace_strip: removed {chars_removed} leading/trailing chars")
        logging.debug(f"clean_text_content: Stripped {chars_removed} whitespace characters")

    # Fix markdown link parsing issues - COMPREHENSIVE PREPROCESSING STEP
    # These patterns fix MDX parsing errors caused by malformed markdown links
    logging.debug("clean_text_content: Starting markdown link preprocessing")

    # 1. Remove angle brackets around email addresses in markdown links (anywhere in link text)
    # <AUTHOR> <EMAIL> more text](url) -> [text <EMAIL> more text](url)
    old_text = text
    pattern = r'\[([^\]]*?)<([^>]+@[^>]+)>([^\]]*?)\](\([^)]+\))'
    matches = re.findall(pattern, old_text)
    text = re.sub(pattern, r'[\1\2\3]\4', text)
    if text != old_text:
        processing_steps.append(f"angle_brackets_emails: fixed {len(matches)} email links with angle brackets")
        # Show first match at INFO level, rest at DEBUG
        if matches:
            first_match = matches[0]
            before = f"[{first_match[0]}<{first_match[1]}>{first_match[2]}]{first_match[3]}"
            after = f"[{first_match[0]}{first_match[1]}{first_match[2]}]{first_match[3]}"
            logging.info(f"clean_text_content: angle_brackets_emails - BEFORE: {before}")
            logging.info(f"clean_text_content: angle_brackets_emails - AFTER:  {after}")

        for match in matches[1:3]:  # Show next 2 matches at DEBUG
            before = f"[{match[0]}<{match[1]}>{match[2]}]{match[3]}"
            after = f"[{match[0]}{match[1]}{match[2]}]{match[3]}"
            logging.debug(f"clean_text_content: angle_brackets_emails - BEFORE: {before}")
            logging.debug(f"clean_text_content: angle_brackets_emails - AFTER:  {after}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: angle_brackets_emails - ... and {len(matches) - 3} more matches")

    # 2. Remove extra spaces after mailto: in email links
    # Pattern: (mailto: <EMAIL>) -> (mailto:<EMAIL>)
    old_text = text
    pattern = r'\(mailto:\s+([^)]+)\)'
    matches = re.findall(pattern, old_text)
    text = re.sub(pattern, r'(mailto:\1)', text)
    if text != old_text:
        processing_steps.append(f"mailto_spaces: fixed {len(matches)} mailto links with extra spaces")
        # Show first match at INFO level, rest at DEBUG
        if matches:
            first_match = matches[0]
            before = f"(mailto: {first_match})"
            after = f"(mailto:{first_match})"
            logging.info(f"clean_text_content: mailto_spaces - BEFORE: {before}")
            logging.info(f"clean_text_content: mailto_spaces - AFTER:  {after}")

        for match in matches[1:3]:  # Show next 2 matches at DEBUG
            before = f"(mailto: {match})"
            after = f"(mailto:{match})"
            logging.debug(f"clean_text_content: mailto_spaces - BEFORE: {before}")
            logging.debug(f"clean_text_content: mailto_spaces - AFTER:  {after}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: mailto_spaces - ... and {len(matches) - 3} more matches")

    # 3. Remove extra spaces after URL schemes in links
    # Pattern: (http: //example.com) or (https: //example.com) -> (http://example.com)
    old_text = text
    pattern = r'\((https?):\s+([^)]+)\)'
    matches = re.findall(pattern, old_text)
    text = re.sub(pattern, r'(\1:\2)', text)
    if text != old_text:
        processing_steps.append(f"url_scheme_spaces: fixed {len(matches)} URL links with extra spaces")
        # Show first match at INFO level, rest at DEBUG
        if matches:
            first_match = matches[0]
            before = f"({first_match[0]}: {first_match[1]})"
            after = f"({first_match[0]}:{first_match[1]})"
            logging.info(f"clean_text_content: url_scheme_spaces - BEFORE: {before}")
            logging.info(f"clean_text_content: url_scheme_spaces - AFTER:  {after}")

        for match in matches[1:3]:  # Show next 2 matches at DEBUG
            before = f"({match[0]}: {match[1]})"
            after = f"({match[0]}:{match[1]})"
            logging.debug(f"clean_text_content: url_scheme_spaces - BEFORE: {before}")
            logging.debug(f"clean_text_content: url_scheme_spaces - AFTER:  {after}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: url_scheme_spaces - ... and {len(matches) - 3} more matches")

    # 4. Remove invisible/non-breaking spaces and other problematic whitespace in URLs
    # This handles cases where there are hidden Unicode whitespace characters
    old_text = text
    text = re.sub(r'\(([a-zA-Z]+:)\s*([^)]+)\)', lambda m: f'({m.group(1)}{m.group(2).strip()})', text)
    if text != old_text:
        processing_steps.append("invisible_whitespace: cleaned hidden whitespace in URLs")
        logging.debug("clean_text_content: Cleaned invisible whitespace characters in URLs")

    # 5. Fix bare URLs wrapped in angle brackets (not part of markdown links)
    # Pattern: <https://example.com> -> https://example.com (when not in a markdown link)
    old_text = text
    pattern = r'<(https?://[^>\s]+)>'
    matches = re.findall(pattern, old_text)
    text = re.sub(pattern, r'\1', text)
    if text != old_text:
        processing_steps.append(f"bare_url_brackets: removed angle brackets from {len(matches)} bare URLs")
        # Show first match at INFO level
        if matches:
            first_match = matches[0]
            before = f"<{first_match}>"
            after = first_match
            logging.info(f"clean_text_content: bare_url_brackets - BEFORE: {before}")
            logging.info(f"clean_text_content: bare_url_brackets - AFTER:  {after}")

        for match in matches[1:3]:  # Show next 2 matches at DEBUG
            before = f"<{match}>"
            after = match
            logging.debug(f"clean_text_content: bare_url_brackets - BEFORE: {before}")
            logging.debug(f"clean_text_content: bare_url_brackets - AFTER:  {after}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: bare_url_brackets - ... and {len(matches) - 3} more matches")

    # 6. Fix bare email addresses wrapped in angle brackets (not part of markdown links)
    # <AUTHOR> <EMAIL> -> <EMAIL> (when not in a markdown link)
    old_text = text
    pattern = r'<([^>\s]+@[^>\s]+\.[^>\s]+)>'
    matches = re.findall(pattern, old_text)
    text = re.sub(pattern, r'\1', text)
    if text != old_text:
        processing_steps.append(f"bare_email_brackets: removed angle brackets from {len(matches)} bare email addresses")
        # Show first match at INFO level
        if matches:
            first_match = matches[0]
            before = f"<{first_match}>"
            after = first_match
            logging.info(f"clean_text_content: bare_email_brackets - BEFORE: {before}")
            logging.info(f"clean_text_content: bare_email_brackets - AFTER:  {after}")

        for match in matches[1:3]:  # Show next 2 matches at DEBUG
            before = f"<{match}>"
            after = match
            logging.debug(f"clean_text_content: bare_email_brackets - BEFORE: {before}")
            logging.debug(f"clean_text_content: bare_email_brackets - AFTER:  {after}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: bare_email_brackets - ... and {len(matches) - 3} more matches")

    # 7. Fix any remaining angle brackets in link text that could be interpreted as JSX
    # Pattern: [text with <something> in it](url) -> [text with something in it](url)
    # But preserve actual HTML tags that might be intentional
    old_text = text
    def fix_angle_brackets(m):
        if '@' in m.group(2) or not re.match(r'^[a-zA-Z][a-zA-Z0-9]*$', m.group(2).strip()):
            return f'[{m.group(1)}{m.group(2)}{m.group(3)}]{m.group(4)}'
        else:
            return m.group(0)

    text = re.sub(r'\[([^\]]*?)<([^/>]+?)>([^\]]*?)\](\([^)]+\))', fix_angle_brackets, text)
    if text != old_text:
        processing_steps.append("jsx_angle_brackets: removed problematic angle brackets in link text")
        logging.debug("clean_text_content: Fixed angle brackets that could be interpreted as JSX")

    # Remove markdown code fence delimiters
    # Handle any code fence pattern: ```format_name or just ```
    # Remove from ``` to the first whitespace (or end if no whitespace)
    if text.startswith('```'):
        old_text = text
        # Find the first whitespace character after ```
        match = re.match(r'^```[^\s]*\s*', text)
        if match:
            removed_part = match.group(0)
            text = text[len(removed_part):]
            processing_steps.append(f"leading_code_fence: removed '{removed_part.strip()}'")
            logging.debug(f"clean_text_content: Removed leading code fence: '{removed_part.strip()}'")
        else:
            # If no whitespace found, just remove the ``` part
            text = text[3:]
            processing_steps.append("leading_code_fence: removed '```'")
            logging.debug("clean_text_content: Removed leading '```'")

    # Strip whitespace again after removing leading delimiters
    old_length = len(text)
    text = text.strip()
    if len(text) != old_length:
        chars_removed = old_length - len(text)
        processing_steps.append(f"post_fence_strip: removed {chars_removed} chars")
        logging.debug(f"clean_text_content: Stripped {chars_removed} chars after fence removal")

    # Remove trailing markdown delimiters
    if text.endswith('```'):
        text = text[:-3].rstrip()
        processing_steps.append("trailing_code_fence: removed trailing '```'")
        logging.debug("clean_text_content: Removed trailing '```'")

    # Handle any code fence pattern anywhere in text: ```format_name or just ```
    # Remove from ``` to the first whitespace (or end if no whitespace)
    old_text = text
    text = re.sub(r'```[^\s]*\s*', '', text)
    if text != old_text:
        matches = len(re.findall(r'```[^\s]*\s*', old_text))
        processing_steps.append(f"inline_code_fences: removed {matches} code fence patterns")
        logging.debug(f"clean_text_content: Removed {matches} inline code fence patterns")

    # Replace HTML line breaks with newlines
    old_text = text
    pattern = r'<br\s*/?>'
    matches = re.findall(pattern, old_text, flags=re.IGNORECASE)
    text = re.sub(pattern, '\n', text, flags=re.IGNORECASE)
    if text != old_text:
        processing_steps.append(f"html_line_breaks: replaced {len(matches)} <br> tags with newlines")
        newline_repr = repr('\n')
        for match in matches[:3]:  # Show first 3 matches
            logging.debug(f"clean_text_content: html_line_breaks - BEFORE: {repr(match)} AFTER: {newline_repr}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: html_line_breaks - ... and {len(matches) - 3} more matches")

    # Replace double slashes with newlines (LLM sometimes uses // for line breaks)
    # Handle both "//" and "// " patterns, but exclude URLs (http:// and https://)
    # Use negative lookbehind to avoid matching // that are part of URLs
    old_text = text
    text = re.sub(r'(?<!http:)(?<!https:)//\s*', '\n', text)
    if text != old_text:
        matches = len(re.findall(r'(?<!http:)(?<!https:)//\s*', old_text))
        processing_steps.append(f"double_slashes: replaced {matches} // patterns with newlines")
        logging.debug(f"clean_text_content: Replaced {matches} double slash patterns with newlines")

    # Replace * with - for bullet points (only at start of line or after whitespace)
    # This avoids matching ** markdown bold formatting
    old_text = text
    text = re.sub(r'(^|\n)\*\s+', r'\1- ', text)
    if text != old_text:
        matches = len(re.findall(r'(^|\n)\*\s+', old_text))
        processing_steps.append(f"bullet_points: converted {matches} * bullets to - bullets")
        logging.debug(f"clean_text_content: Converted {matches} asterisk bullets to dash bullets")

    # Remove HTML comments
    old_text = text

    # First handle irrelevant comments
    irrelevant_pattern = r'<!--irrelevant:.*?-->'
    irrelevant_matches = re.findall(irrelevant_pattern, old_text, flags=re.DOTALL)
    text = re.sub(irrelevant_pattern, '', text, flags=re.DOTALL)

    # Then handle all other comments
    comment_pattern = r'<!--.*?-->'
    remaining_matches = re.findall(comment_pattern, text, flags=re.DOTALL)
    text = re.sub(comment_pattern, '', text, flags=re.DOTALL)

    total_matches = len(irrelevant_matches) + len(remaining_matches)

    if text != old_text:
        processing_steps.append(f"html_comments: removed {total_matches} HTML comments ({len(irrelevant_matches)} irrelevant)")

        # Show examples of removed comments
        all_matches = irrelevant_matches + remaining_matches

        # Log first example at INFO level for visibility, rest at DEBUG
        if all_matches:
            first_match = all_matches[0]
            display_match = first_match[:100] + "..." if len(first_match) > 100 else first_match
            logging.info(f"clean_text_content: html_comments - Example removed: {repr(display_match)}")

        # Log remaining examples at DEBUG level
        for match in all_matches[1:3]:  # Show next 2 matches at DEBUG
            display_match = match[:100] + "..." if len(match) > 100 else match
            logging.debug(f"clean_text_content: html_comments - REMOVED: {repr(display_match)}")
        if len(all_matches) > 3:
            logging.debug(f"clean_text_content: html_comments - ... and {len(all_matches) - 3} more comments removed")

    # Add space after colons when followed by letters, following the user's specific request
    # Original request: "change it to only change :letter to : letter"
    #
    # This is a conservative approach that only adds space after colons when:
    # 1. The colon is immediately followed by a letter (not number, space, or punctuation)
    # 2. The colon is not part of a URL scheme (http:// or https://)
    #
    # This approach accepts that some markdown cases might get modified, but focuses
    # on the core requirement while avoiding the most problematic cases (URLs, times, ratios)

    # Pattern explanation:
    # :([a-zA-Z]) - matches colon followed by any letter
    # (?<!http) - negative lookbehind: not preceded by "http"
    # (?<!https) - negative lookbehind: not preceded by "https"
    old_text = text
    pattern = r'(?<!http)(?<!https):([a-zA-Z])'
    matches = re.findall(pattern, old_text)
    text = re.sub(pattern, r': \1', text)
    if text != old_text:
        processing_steps.append(f"colon_spacing: added space after {len(matches)} colons followed by letters")
        for match in matches[:3]:  # Show first 3 matches
            before = f":{match}"
            after = f": {match}"
            logging.debug(f"clean_text_content: colon_spacing - BEFORE: {repr(before)} AFTER: {repr(after)}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: colon_spacing - ... and {len(matches) - 3} more matches")

    # Normalize multiple consecutive newlines to maximum of two newlines
    old_text = text
    pattern = r'\n{3,}'
    matches = re.findall(pattern, old_text)
    text = re.sub(pattern, '\n', text)
    if text != old_text:
        processing_steps.append("newline_normalization: normalized multiple consecutive newlines")

        # Show examples of normalized newlines
        for match in matches[:3]:  # Show first 3 matches
            before_repr = repr(match)
            after_repr = repr('\n')
            logging.debug(f"clean_text_content: newline_normalization - BEFORE: {before_repr} AFTER: {after_repr}")
        if len(matches) > 3:
            logging.debug(f"clean_text_content: newline_normalization - ... and {len(matches) - 3} more sequences normalized")

    # Format markdown tables if in markdown mode
    if mode == 'markdown' or mode == 'auto':
        old_text = text
        text = format_markdown_table(text)
        if text != old_text:
            processing_steps.append("table_formatting: formatted markdown tables")
            logging.debug("clean_text_content: Formatted markdown tables")

    # Final cleanup
    final_length = len(text.strip())
    final_text = text.strip()

    # Log final results
    processing_steps.append(f"final_strip: final length {final_length} chars")

    # Log summary with actual steps applied
    if processing_steps:
        steps_summary = ", ".join([step.split(":")[0] for step in processing_steps if not step.startswith("final_strip")])
        logging.info(f"clean_text_content: Processing complete - original: {original_length} chars, final: {final_length} chars")
        logging.info(f"clean_text_content: Processing steps applied ({len(processing_steps)} total): {steps_summary}")

        # Also log step details at INFO level for key transformations
        for step in processing_steps:
            if not step.startswith("final_strip") and (":" in step):
                step_name, step_detail = step.split(":", 1)
                if step_name in ["html_comments", "angle_brackets_emails", "mailto_spaces", "url_scheme_spaces"]:
                    logging.info(f"clean_text_content: {step_name} - {step_detail.strip()}")

        logging.debug(f"clean_text_content: Detailed processing steps: {processing_steps}")
    else:
        logging.info(f"clean_text_content: Processing complete - original: {original_length} chars, final: {final_length} chars")
        logging.info("clean_text_content: No processing steps applied - text unchanged")

    if final_length < 100:
        logging.debug(f"clean_text_content: Final text: {repr(final_text)}")
    else:
        logging.debug(f"clean_text_content: Final text (first 100 chars): {repr(final_text[:100])}")

    return final_text


# Backward compatibility functions - these now use the unified function
def clean_markdown_header(text):
    """Legacy function - use clean_text_content(text, mode='header') instead."""
    return clean_text_content(text, mode='header')


def strip_markdown_delimiters(outline_text):
    """Legacy function - use clean_text_content(text, mode='markdown') instead."""
    return clean_text_content(outline_text, mode='markdown')


def clean_json_code_block(text: str) -> Any:
    """Legacy function - use clean_text_content(text, mode='json') instead."""
    return clean_text_content(text, mode='json')


def clean_markdown_code_block(text: str) -> str:
    """Legacy function - use clean_text_content(text, mode='markdown') instead."""
    return clean_text_content(text, mode='markdown')

def parse_json_proposal_info(text):
    """Parse the JSON output from the RFP info extraction.

    This function takes the raw output from the model, cleans it by removing any
    markdown formatting or code block markers, and returns a properly parsed JSON object.

    Args:
        text (str): The raw text output from the model containing JSON data

    Returns:
        dict: A Python dictionary containing the parsed JSON data

    Raises:
        json.JSONDecodeError: If the text cannot be parsed as valid JSON
    """
    # Log the original text for debugging
    logging.debug(f"Original text to parse: {text[:100]}...")

    # Clean the text by removing any markdown code block markers
    # First, check if the text is wrapped in ```json ... ``` or just ``` ... ```
    json_pattern = r'```(?:json)?\s*(\{.*\})\s*```'
    match = re.search(json_pattern, text, re.DOTALL)

    if match:
        # Extract just the JSON part from between the code block markers
        cleaned_text = match.group(1)
        logging.debug("Found JSON inside code block markers")
    else:
        # If no code block markers, try to find JSON object directly
        # Look for text that starts with { and ends with }
        json_direct_pattern = r'(\{.*\})'
        match = re.search(json_direct_pattern, text, re.DOTALL)

        if match:
            cleaned_text = match.group(1)
            logging.debug("Found JSON object directly in text")
        else:
            # If we can't find a clear JSON pattern, just use the original text
            cleaned_text = text.strip()
            logging.debug("No clear JSON pattern found, using original text")

    # Try to parse the cleaned text as JSON
    try:
        result = json.loads(cleaned_text)
        logging.info(f"Successfully parsed JSON with {len(result)} fields")
        return result
    except json.JSONDecodeError as e:
        # If parsing fails, try more aggressive cleaning
        logging.warning(f"Initial JSON parsing failed: {str(e)}")

        # Try to find anything that looks like a JSON object
        try:
            # Find the first { and the last }
            start = cleaned_text.find('{')
            end = cleaned_text.rfind('}')

            if start != -1 and end != -1 and end > start:
                json_candidate = cleaned_text[start:end+1]
                result = json.loads(json_candidate)
                logging.info(f"Successfully parsed JSON after aggressive cleaning with {len(result)} fields")
                return result
            else:
                logging.error("Could not find valid JSON object markers")
                raise
        except Exception as e2:
            logging.error(f"JSON parsing failed after aggressive cleaning: {str(e2)}")
            raise

def filter_rfp_project_metadata(metadata):
    """Filter the rfp_project_metadata to only include specific fields.

    This function takes the full rfp_project_metadata dictionary and returns a new
    dictionary containing only the specified fields.

    Args:
        metadata (dict): The full rfp_project_metadata dictionary

    Returns:
        dict: A filtered dictionary containing only the specified fields
    """
    # Define the fields to keep
    fields_to_keep = [
        "Solicitation Number",
        "Title",
        "Solicitation Type",
        "Questions Due Date",
        "Proposal Due Date",
        "POC"
    ]

    # Create a new dictionary with only the specified fields
    filtered_metadata = {}

    for field in fields_to_keep:
        if field in metadata:
            filtered_metadata[field] = metadata[field]
        else:
            # Try to find similar field names
            for key in metadata:
                if field.lower() in key.lower():
                    filtered_metadata[field] = metadata[key]
                    logging.info(f"Mapped '{key}' to '{field}'")
                    break

    logging.info(f"Filtered rfp_project_metadata from {len(metadata)} to {len(filtered_metadata)} fields")
    return filtered_metadata

def convert_proposal_info_to_markdown(proposal_info):
    """Convert proposal info JSON to markdown bullet point format.

    This function takes the proposal info JSON and converts it to a markdown bullet point
    format for rendering on the frontend.

    Args:
        proposal_info (dict): The proposal info JSON

    Returns:
        str: Markdown bullet point representation of the proposal info
    """
    if not proposal_info:
        logging.warning("Empty proposal info provided to convert_proposal_info_to_markdown")
        return "## Proposal Info\n\nNo proposal information available."

    # Start with the header
    markdown = "## Proposal Info\n\n"

    # Function to format complex values for bullet points
    def format_value(val, parent_key=None, indent_level=0):
        indent = "  " * indent_level  # Two spaces per indent level

        if isinstance(val, dict):
            # Special handling for common nested structures
            if parent_key == "Period of Performance":
                # Format Period of Performance specially
                formatted = ""

                # Handle base period
                if "Base Period Duration" in val:
                    formatted += f"\n{indent}  - **Base Period:**\n"
                    formatted += f"{indent}    - Duration: {val.get('Base Period Duration', '')}\n"
                    formatted += f"{indent}    - Start Date: {val.get('Base Period Start Date', '')}\n"
                    formatted += f"{indent}    - End Date: {val.get('Base Period End Date', '')}"
                elif "duration" in val:
                    formatted += f"\n{indent}  - **Base Period:**\n"
                    formatted += f"{indent}    - Duration: {val.get('duration', '')}\n"
                    formatted += f"{indent}    - Start Date: {val.get('start_date', '')}\n"
                    formatted += f"{indent}    - End Date: {val.get('end_date', '')}"

                # Handle option periods
                if "option_periods" in val and isinstance(val["option_periods"], list):
                    formatted += f"\n{indent}  - **Option Periods:**"
                    for i, period in enumerate(val["option_periods"], 1):
                        if isinstance(period, dict):
                            formatted += f"\n{indent}    - {period.get('name', f'Option Period {i}')}: "
                            formatted += f"Duration: {period.get('duration', '')}, "
                            formatted += f"Start Date: {period.get('start_date', '')}, "
                            formatted += f"End Date: {period.get('end_date', '')}"

                # Handle extensions
                if "extensions" in val and isinstance(val["extensions"], list):
                    formatted += f"\n{indent}  - **Extensions:**"
                    for i, ext in enumerate(val["extensions"], 1):
                        if isinstance(ext, dict):
                            formatted += f"\n{indent}    - {ext.get('name', f'Extension {i}')}: "
                            formatted += f"Duration: {ext.get('duration', '')}, "
                            formatted += f"Start Date: {ext.get('start_date', '')}, "
                            formatted += f"End Date: {ext.get('end_date', '')}"

                return formatted

            elif parent_key == "POC":
                # Format POC specially
                formatted = ""

                # Handle nested Primary POC
                if "Primary" in val and isinstance(val["Primary"], dict):
                    primary = val["Primary"]
                    formatted += f"\n{indent}  - **Primary Contact:**\n"
                    formatted += f"{indent}    - Name: {primary.get('Name', '')}\n"
                    formatted += f"{indent}    - Title: {primary.get('Title', '')}\n"
                    formatted += f"{indent}    - Email: {primary.get('email', '')}\n"
                    formatted += f"{indent}    - Phone: {primary.get('phone', '')}"

                # Handle nested secondary POC
                if "secondary" in val and isinstance(val["secondary"], dict):
                    secondary = val["secondary"]
                    formatted += f"\n{indent}  - **Secondary Contact:**\n"
                    formatted += f"{indent}    - Name: {secondary.get('name', '')}\n"
                    formatted += f"{indent}    - Title: {secondary.get('title', '')}\n"
                    formatted += f"{indent}    - Email: {secondary.get('email', '')}\n"
                    formatted += f"{indent}    - Phone: {secondary.get('phone', '')}"

                # Fallback for flat structure
                if not formatted:
                    # Try flat structure
                    if "Primary Name" in val:
                        formatted += f"\n{indent}  - **Primary Contact:**\n"
                        formatted += f"{indent}    - Name: {val.get('Primary Name', '')}\n"
                        formatted += f"{indent}    - Title: {val.get('Primary Title', '')}\n"
                        formatted += f"{indent}    - Email: {val.get('Primary email', '')}\n"
                        formatted += f"{indent}    - Phone: {val.get('Primary phone', '')}"

                    if "secondary name" in val:
                        formatted += f"\n{indent}  - **Secondary Contact:**\n"
                        formatted += f"{indent}    - Name: {val.get('secondary name', '')}\n"
                        formatted += f"{indent}    - Title: {val.get('secondary title', '')}\n"
                        formatted += f"{indent}    - Email: {val.get('secondary email', '')}\n"
                        formatted += f"{indent}    - Phone: {val.get('secondary phone', '')}"

                return formatted

            # Default dictionary formatting
            formatted = ""
            for k, v in val.items():
                # Skip keys that will be handled specially
                if parent_key == "Period of Performance" and k in ["option_periods", "extensions"]:
                    continue

                # Recursively format nested values
                formatted_v = format_value(v, k, indent_level + 1)
                formatted += f"\n{indent}  - **{k}:** {formatted_v}"
            return formatted

        elif isinstance(val, list):
            # Format list as bullet points
            if not val:
                return ""

            # Special handling for Key Personnel
            if parent_key == "Key Personnel":
                if len(val) == 1:
                    return str(val[0])
                formatted = ""
                for item in val:
                    formatted += f"\n{indent}  - {item}"
                return formatted

            # Check if list contains dictionaries
            if any(isinstance(item, dict) for item in val):
                # Format list of dictionaries
                formatted = ""
                for i, item in enumerate(val, 1):
                    if isinstance(item, dict):
                        formatted += f"\n{indent}  - **Item {i}:**"
                        for k, v in item.items():
                            formatted_v = format_value(v, k, indent_level + 1)
                            formatted += f"\n{indent}    - {k}: {formatted_v}"
                    else:
                        formatted += f"\n{indent}  - {item}"
                return formatted
            else:
                # Simple list - format as bullet points
                if len(val) == 1:
                    return str(val[0])
                formatted = ""
                for item in val:
                    formatted += f"\n{indent}  - {item}"
                return formatted
        else:
            # Return string representation for other types
            return str(val)

    # Add each field as bullet points
    for key, value in proposal_info.items():
        # Format the value based on its type and parent key
        formatted_value = format_value(value, key)

        # Add the bullet point
        if formatted_value and formatted_value.strip():
            # If the formatted value starts with newlines (nested structure), use it as-is
            if formatted_value.startswith('\n'):
                markdown += f"- **{key}:**{formatted_value}\n\n"
            else:
                # Simple value, add it inline
                markdown += f"- **{key}:** {formatted_value}\n\n"
        else:
            # Empty or None value
            markdown += f"- **{key}:** Not specified\n\n"

    logging.info(f"Converted proposal info JSON with {len(proposal_info)} fields to markdown bullet points")
    return markdown
