"""Project management routes module.

This module provides FastAPI routes for project management operations including:
- Project creation and deletion
- Project listing and retrieval
- Project renaming and updates
- Model selection and management
All endpoints handle proper authentication, logging, and database operations.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
import logging
import uuid
from utils.auth import get_current_active_user, User
from utils.database import projects_collection
from project_manager import ProjectManager
from project_manager.utils import get_time_ago
from schemas import ProjectCreate, Project<PERSON><PERSON><PERSON>
from datetime import datetime
from utils import get_valid_models
from utils.constants import HTTPStatus


router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/projects/")
async def create_project(
    project: ProjectCreate, current_user: User = Depends(get_current_active_user)
):
    """Create a new project for the authenticated user.

    Args:
        project (ProjectCreate): Project creation data including name
        current_user (User): The authenticated user making the request

    Returns:
        dict: Created project ID and name

    Raises:
        HTTPException: 400 if project name already exists for the user
                      500 if project creation fails
    """
    logger.info(f"User {current_user.full_name} creating project: {project.project_name}")

    # Check for existing project with the same name for this user
    existing_project = await projects_collection.find_one({
        "user_id": str(current_user.id),
        "project_name": project.project_name,
        "$or": [
            {"is_deleted": False},
            {"is_deleted": {"$exists": False}}
        ]
    })

    if existing_project:
        logger.warning(f"User {current_user.full_name} attempted to create project with duplicate name: {project.project_name}")
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"A project with the name '{project.project_name}' already exists. Please choose a different name."
        )

    project_id = str(uuid.uuid4())
    project_manager = await ProjectManager.create_new_project(
        project_id, project.project_name, str(current_user.id)
    )
    await project_manager.update_timestamps(update_content=True, update_visit=True)

    logger.info(f"Successfully created project '{project.project_name}' with ID {project_id} for user {current_user.full_name}")
    return {"project_id": project_id, "project_name": project.project_name}

@router.get("/projects")
async def list_user_projects(
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    sort_on: str = Query("created_at", regex="^(created_at|updated_at|last_visited_time)$"),
    ascending: bool = Query(False)
):
    """List all projects for the authenticated user with pagination and sorting.

    Args:
        current_user (User): The authenticated user making the request
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 10.
        sort_on (str, optional): Field to sort on. Defaults to "created_at".
        ascending (bool, optional): Sort order. Defaults to False (descending).

    Returns:
        dict: Total count and list of projects with metadata
    """
    logger.info(f"User {current_user.full_name} retrieving their projects with sorting on {sort_on}")

    # Query for non-deleted projects with pagination and sorting
    user_projects_cursor = projects_collection.find(
        {
            "user_id": str(current_user.id),
            "$or": [
                {"is_deleted": False},
                {"is_deleted": {"$exists": False}}
            ]
        },
        {
            "project_id": 1,
            "project_name": 1,
            "created_at": 1,
            "updated_at": 1,
            "last_visited_time": 1,
            "files_metadata": 1,
            "rfp_project_metadata": 1,
            "_id": 0
        }
    ).sort(sort_on, 1 if ascending else -1).skip(skip).limit(limit)

    user_projects = await user_projects_cursor.to_list(length=None)

    # Process each project to add calculated fields
    for project in user_projects:
        # Calculate file_count from files_metadata array length
        project["file_count"] = len(project.get("files_metadata", []))

        # Remove files_metadata array from response to reduce payload size
        if "files_metadata" in project:
            del project["files_metadata"]

        # Ensure rfp_project_metadata exists, set to empty dict if not
        if "rfp_project_metadata" not in project:
            project["rfp_project_metadata"] = {}

    # Get total count for pagination
    total_projects = await projects_collection.count_documents({
        "user_id": str(current_user.id),
        "$or": [
            {"is_deleted": False},
            {"is_deleted": {"$exists": False}}
        ]
    })

    if not user_projects:
        logger.info(f"No projects found for user {current_user.full_name}")
        return {"message": "You have no projects yet.", "total": 0, "projects": []}

    return {
        "total": total_projects,
        "projects": user_projects
    }

@router.get("/projects/{project_id}")
async def load_project(project_id: str, current_user: User = Depends(get_current_active_user)):
    """Load a specific project with all its metadata and sections.

    Args:
        project_id (str): The ID of the project to load
        current_user (User): The authenticated user making the request

    Returns:
        dict: Project details including metadata, sections, and timestamps

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project not found
    """
    logger.info(f"User {current_user.full_name} loading project {project_id}")

    # Get project metadata from MongoDB
    project_metadata = await projects_collection.find_one({
        "project_id": project_id,
        "$or": [
            {"is_deleted": False},
            {"is_deleted": {"$exists": False}}
        ]
    })
    if not project_metadata:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_metadata.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

    # Load project data from GCP
    project_manager = await ProjectManager.load_project(
        project_id=project_id,
        user_id=str(current_user.id)
    )
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project data not found in GCP bucket")

    # Extract section information
    sections = [
        {
            "section_title": section["section_title"],
            "user_guidelines": section.get("user_guidelines", "")
        }
        for section in project_manager.project_data.get('sections', [])
    ]

    # Format timestamps with relative time
    timestamps = {}
    for field in ['created_at', 'updated_at', 'last_visited_time']:
        if isinstance(project_metadata.get(field), datetime):
            timestamps[f'{field}'] = project_metadata[field].isoformat()
            timestamps[f'{field}_ago'] = get_time_ago(project_metadata[field])
        else:
            timestamps[f'{field}'] = None
            timestamps[f'{field}_ago'] = None

    logger.info(f"Project {project_id} loaded successfully for user {current_user.full_name}")

    await project_manager.update_timestamps(update_content=False, update_visit=True)

    # Use responses directly from summaries.json (no need to regenerate markdown)
    processed_responses = project_manager.responses

    return {
        "project_id": project_manager.project_id,
        "project_name": project_manager.project_name,
        "sections_info": sections,
        "uploaded_files": project_metadata.get('files_metadata', []),
        "responses": processed_responses,
        **timestamps
    }

@router.delete("/projects/{project_id}")
async def delete_project(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Soft delete a project (mark as deleted without removing data).

    Args:
        project_id (str): The ID of the project to delete
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project not found
                      500 if deletion fails
    """
    logger.info(f"User {current_user.full_name} attempting to delete project: {project_id}")

    project_manager = await ProjectManager.load_project(project_id)

    if not project_manager:
        logger.error(f"Project {project_id} not found")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        logger.warning(f"Unauthorized attempt to delete project {project_id} by user {current_user.full_name}")
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to delete this project")

    success = await ProjectManager.soft_delete_project(project_id)

    if success:
        logger.info(f"Successfully soft deleted project {project_id}")
        return {"message": f"Successfully deleted project: {project_id}"}
    else:
        logger.error(f"Failed to delete project {project_id}")
        raise HTTPException(status_code=500, detail="Failed to delete project")

@router.put("/projects/{project_id}/rename")
async def rename_project(
    project_id: str,
    rename_data: ProjectRename,
    current_user: User = Depends(get_current_active_user)
):
    """Rename an existing project.

    Args:
        project_id (str): The ID of the project to rename
        rename_data (ProjectRename): New name for the project
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message with old and new names

    Raises:
        HTTPException: 400 if new project name already exists for the user
                      403 for unauthorized access
                      404 if project not found
                      500 if rename fails
    """
    logger.info(f"User {current_user.full_name} attempting to rename project {project_id}")

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to rename this project")

    old_name = project_manager.project_name
    new_name = rename_data.new_name

    # Check if the new name is different from the current name
    if old_name == new_name:
        logger.info(f"User {current_user.full_name} attempted to rename project {project_id} to the same name: {new_name}")
        return {"message": f"Project name is already '{new_name}'. No changes made."}

    # Check for existing project with the same new name for this user (excluding current project)
    existing_project = await projects_collection.find_one({
        "user_id": str(current_user.id),
        "project_name": new_name,
        "project_id": {"$ne": project_id},  # Exclude current project
        "$or": [
            {"is_deleted": False},
            {"is_deleted": {"$exists": False}}
        ]
    })

    if existing_project:
        logger.warning(f"User {current_user.full_name} attempted to rename project {project_id} to duplicate name: {new_name}")
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"A project with the name '{new_name}' already exists. Please choose a different name."
        )

    success = await project_manager.rename_project(new_name)

    await project_manager.update_timestamps(update_content=True, update_visit=True)

    if success:
        logger.info(f"Project with id {project_id} renamed from '{old_name}' to '{new_name}' by user {current_user.full_name}")
        return {"message": f"Project renamed successfully from '{old_name}' to '{new_name}'"}
    else:
        logger.error(f"Failed to rename project {project_id} for user {current_user.full_name}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Failed to rename project")

@router.get("/get_models_list")
async def get_models_list(current_user: User = Depends(get_current_active_user)):
    """Get a list of all valid AI models with their display names and descriptions.

    Args:
        current_user (User): The authenticated user making the request

    Returns:
        list: List of dictionaries containing model information
    """
    logger.info(f"User {current_user.full_name} retrieving list of valid models")
    return await get_valid_models()

@router.put("/projects/{project_id}/model")
async def update_model(
    project_id: str,
    model_name: str,
    current_user: User = Depends(get_current_active_user)
):
    """Update the AI model used for a project.

    Args:
        project_id (str): The ID of the project to update
        model_name (str): Name of the new model to use
        current_user (User): The authenticated user making the request

    Returns:
        dict: Success message

    Raises:
        HTTPException: 400 for invalid model name
                      403 for unauthorized access
                      404 if project not found
    """
    # Get list of valid model names
    valid_models = await get_valid_models()
    valid_model_names = [model["model_name"] for model in valid_models]

    if model_name not in valid_model_names:
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail="Invalid model name")

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

    # Update model in project manager
    project_manager.model_name = model_name
    await project_manager.save_project()

    # Update model in MongoDB
    await projects_collection.update_one(
        {"project_id": project_id},
        {"$set": {"model_name": model_name}}
    )

    await project_manager.update_timestamps(update_content=True, update_visit=True)

    return {"message": f"Model updated to {model_name}"}

@router.get("/projects/{project_id}/model")
async def get_model(project_id: str, current_user: User = Depends(get_current_active_user)):
    """Get the current AI model used for a project.

    Args:
        project_id (str): The ID of the project to query
        current_user (User): The authenticated user making the request

    Returns:
        dict: Current model name and its details (display name and description)

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project not found
    """
    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

    model_name = project_manager.model_name

    # Get model details
    model_details = None
    valid_models = await get_valid_models()
    for model in valid_models:
        if model["model_name"] == model_name:
            model_details = model
            break

    await project_manager.update_timestamps(update_content=False, update_visit=True)

    if model_details:
        return {
            "model": model_name,
            "display_name": model_details["display_name"],
            "description": model_details["description"]
        }
    else:
        # Fallback if model details not found
        return {"model": model_name}