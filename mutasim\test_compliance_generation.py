#!/usr/bin/env python3
"""
Test script for the modified compliance generation function.

This script tests the updated generate_compliances function that now uses
actual project files instead of summary content.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import from the main modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mutasim.compliances import generate_compliances, get_project_files_content


async def test_get_project_files_content(project_id: str):
    """Test the new get_project_files_content function."""
    print(f"\n=== Testing get_project_files_content for project: {project_id} ===")
    
    try:
        content = await get_project_files_content(project_id)
        print(f"✅ Successfully retrieved project files content")
        print(f"📊 Content length: {len(content)} characters")
        print(f"📄 Content preview (first 500 chars):")
        print("-" * 50)
        print(content[:500])
        print("-" * 50)
        return True
    except Exception as e:
        print(f"❌ Error retrieving project files content: {e}")
        return False


async def test_generate_compliances(project_id: str):
    """Test the modified generate_compliances function."""
    print(f"\n=== Testing generate_compliances for project: {project_id} ===")
    
    try:
        result = await generate_compliances(project_id)
        
        if "error" in result:
            print(f"❌ Error in compliance generation: {result['error']}")
            return False
        
        print(f"✅ Successfully generated compliance matrices")
        
        # Check the structure of the result
        expected_keys = ["proposal_format", "proposal_organization", "compliance_matrix"]
        for key in expected_keys:
            if key in result:
                count = len(result[key]) if isinstance(result[key], list) else 0
                print(f"📋 {key}: {count} items")
            else:
                print(f"⚠️  Missing key: {key}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in compliance generation: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Starting compliance generation tests...")
    
    # You can replace this with an actual project ID from your system
    test_project_id = input("Enter a project ID to test (or press Enter to skip): ").strip()
    
    if not test_project_id:
        print("⏭️  No project ID provided, skipping tests")
        print("\n📝 To test the compliance generation:")
        print("1. Run this script with a valid project ID")
        print("2. Make sure the project has uploaded files with appropriate tags")
        print("3. The function will now use actual project files instead of summary content")
        return
    
    print(f"🔍 Testing with project ID: {test_project_id}")
    
    # Test 1: Test the new file content retrieval function
    content_test_passed = await test_get_project_files_content(test_project_id)
    
    # Test 2: Test the full compliance generation (only if content retrieval worked)
    if content_test_passed:
        compliance_test_passed = await test_generate_compliances(test_project_id)
        
        if compliance_test_passed:
            print("\n🎉 All tests passed! The compliance generation is now working with actual project files.")
        else:
            print("\n⚠️  Content retrieval worked, but compliance generation failed.")
    else:
        print("\n⚠️  Content retrieval failed, skipping compliance generation test.")
    
    print("\n📋 Summary of changes made:")
    print("✅ Added get_project_files_content() function to retrieve actual project files")
    print("✅ Modified generate_compliances() to use project files instead of summary content")
    print("✅ Added proper error handling and logging")
    print("✅ The function now processes Main Document and supplementary files with relevant tags")


if __name__ == "__main__":
    asyncio.run(main())
