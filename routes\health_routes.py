"""Health check routes module for API monitoring and status verification.

This module provides FastAPI routes for health checking and monitoring including:
- Basic health status endpoint
- Detailed health check with dependency verification
- Database connectivity testing
- Service availability confirmation

These endpoints are used by load balancers, monitoring systems, container orchestrators,
and deployment pipelines to verify service health.
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from datetime import datetime, timezone
import logging
import sys

from utils.constants import HTTPStatus
from utils.database import db
from config import settings

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/health")
async def health_check():
    """Basic health check endpoint.
    
    Returns a simple status indicating the service is running.
    This is a lightweight endpoint suitable for frequent polling by load balancers.
    
    Returns:
        dict: Basic health status with timestamp
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "Document Summarization API"
    }


@router.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check endpoint with dependency verification.
    
    Performs comprehensive health checks including:
    - Database connectivity
    - Environment configuration
    - Service dependencies
    - System resources
    
    Returns:
        dict: Detailed health status with component-specific information
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "Document Summarization API",
        "version": "1.0.0",
        "checks": {}
    }
    
    overall_healthy = True
    
    # Database connectivity check
    try:
        # Simple ping to verify database connection
        await db.command("ping")
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
        logger.debug("Database health check passed")
    except Exception as e:
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
        overall_healthy = False
        logger.error(f"Database health check failed: {str(e)}")
    
    # Environment configuration check
    try:
        required_env_vars = [
            "BUCKET_NAME",
            "MONGO_URI",
            "SECRET_KEY"
        ]

        missing_vars = []
        for var in required_env_vars:
            if not hasattr(settings, var) or not getattr(settings, var):
                missing_vars.append(var)

        if missing_vars:
            health_status["checks"]["environment"] = {
                "status": "unhealthy",
                "message": f"Missing required environment variables: {', '.join(missing_vars)}"
            }
            overall_healthy = False
        else:
            health_status["checks"]["environment"] = {
                "status": "healthy",
                "message": "All required environment variables are set"
            }
        logger.debug("Environment configuration check completed")
    except Exception as e:
        health_status["checks"]["environment"] = {
            "status": "unhealthy",
            "message": f"Environment check failed: {str(e)}"
        }
        overall_healthy = False
        logger.error(f"Environment health check failed: {str(e)}")
    
    # Google Cloud Storage check
    try:
        from google.cloud import storage
        client = storage.Client()
        bucket = client.bucket(settings.BUCKET_NAME)
        # Test bucket access by checking if it exists
        bucket.reload()
        health_status["checks"]["storage"] = {
            "status": "healthy",
            "message": "Google Cloud Storage connection successful"
        }
        logger.debug("Storage health check passed")
    except Exception as e:
        health_status["checks"]["storage"] = {
            "status": "unhealthy",
            "message": f"Google Cloud Storage connection failed: {str(e)}"
        }
        overall_healthy = False
        logger.error(f"Storage health check failed: {str(e)}")
    
    # System resources check
    try:
        import psutil
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_usage_percent = memory.percent
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        
        # CPU usage (average over 1 second)
        cpu_usage_percent = psutil.cpu_percent(interval=1)
        
        resource_status = "healthy"
        resource_message = "System resources within normal limits"
        
        # Check for resource constraints
        if memory_usage_percent > 90:
            resource_status = "warning"
            resource_message = f"High memory usage: {memory_usage_percent:.1f}%"
        elif disk_usage_percent > 90:
            resource_status = "warning"
            resource_message = f"High disk usage: {disk_usage_percent:.1f}%"
        elif cpu_usage_percent > 90:
            resource_status = "warning"
            resource_message = f"High CPU usage: {cpu_usage_percent:.1f}%"
        
        health_status["checks"]["system_resources"] = {
            "status": resource_status,
            "message": resource_message,
            "details": {
                "memory_usage_percent": round(memory_usage_percent, 1),
                "disk_usage_percent": round(disk_usage_percent, 1),
                "cpu_usage_percent": round(cpu_usage_percent, 1)
            }
        }
        logger.debug("System resources check completed")
    except ImportError:
        health_status["checks"]["system_resources"] = {
            "status": "skipped",
            "message": "psutil not available for system resource monitoring"
        }
        logger.debug("System resources check skipped - psutil not available")
    except Exception as e:
        health_status["checks"]["system_resources"] = {
            "status": "error",
            "message": f"System resources check failed: {str(e)}"
        }
        logger.error(f"System resources health check failed: {str(e)}")
    
    # Python version and dependencies check
    try:
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        health_status["checks"]["runtime"] = {
            "status": "healthy",
            "message": f"Python {python_version} runtime operational",
            "details": {
                "python_version": python_version,
                "platform": sys.platform
            }
        }
        logger.debug("Runtime check completed")
    except Exception as e:
        health_status["checks"]["runtime"] = {
            "status": "error",
            "message": f"Runtime check failed: {str(e)}"
        }
        overall_healthy = False
        logger.error(f"Runtime health check failed: {str(e)}")
    
    # Set overall status
    if not overall_healthy:
        health_status["status"] = "unhealthy"
        return JSONResponse(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            content=health_status
        )
    
    # Check for warnings
    warning_checks = [check for check in health_status["checks"].values() 
                     if check.get("status") == "warning"]
    if warning_checks:
        health_status["status"] = "degraded"
    
    return health_status


@router.get("/health/ready")
async def readiness_check():
    """Readiness check endpoint for container orchestration.
    
    Indicates whether the service is ready to accept traffic.
    This is typically used by Kubernetes readiness probes.
    
    Returns:
        dict: Readiness status
    """
    try:
        # Check critical dependencies
        await db.command("ping")
        
        return {
            "status": "ready",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": "Service is ready to accept traffic"
        }
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail={
                "status": "not_ready",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message": f"Service is not ready: {str(e)}"
            }
        )


@router.get("/health/live")
async def liveness_check():
    """Liveness check endpoint for container orchestration.
    
    Indicates whether the service is alive and should not be restarted.
    This is typically used by Kubernetes liveness probes.
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "message": "Service is alive and operational"
    }
