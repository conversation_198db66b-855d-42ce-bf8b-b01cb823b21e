#!/usr/bin/env python3
"""
Test script for content review functionality
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from content_review import get_content_review_rulebook, get_content_review_criteria
from project_manager import ProjectManager

async def test_content_review():
    """Test the content review functionality"""
    
    print("Testing content review functionality...")
    
    # Test 1: Get rulebook data
    print("\n1. Testing get_content_review_rulebook...")
    try:
        # Test getting all criteria
        all_criteria = get_content_review_rulebook()
        print(f"✓ Retrieved all criteria: {len(all_criteria)} keys")
        
        # Test getting proposal criteria only
        proposal_criteria = get_content_review_rulebook("proposal_criterias")
        print(f"✓ Retrieved proposal criteria: {len(proposal_criteria)} items")
        
        # Test getting general criteria only
        general_criteria = get_content_review_rulebook("general_criterias")
        print(f"✓ Retrieved general criteria: {len(general_criteria)} items")
        
    except Exception as e:
        print(f"✗ Error testing rulebook: {e}")
    
    # Test 2: Test with a real project (if available)
    print("\n2. Testing get_content_review_criteria...")
    
    # You would need to replace this with an actual project ID that has a Main Document
    test_project_id = "186344d3-39d4-4b26-a55f-f5835bc0c854"  # Replace with actual project ID
    
    try:
        project_manager = await ProjectManager.load_project(test_project_id)
        if project_manager:
            print(f"✓ Loaded project: {test_project_id}")
            
            # Test getting general criteria only (should not trigger LLM)
            general_only = await get_content_review_criteria(
                project_manager=project_manager,
                criterias_type="general_criterias",
                regenerate=False
            )
            print(f"✓ Retrieved general criteria only: {len(general_only)} keys")
            
            # Test getting all criteria (should trigger LLM for proposal criteria)
            all_criteria = await get_content_review_criteria(
                project_manager=project_manager,
                criterias_type=None,
                regenerate=False
            )
            print(f"✓ Retrieved all criteria: {len(all_criteria)} keys")
            
        else:
            print(f"✗ Could not load project: {test_project_id}")
            
    except Exception as e:
        print(f"✗ Error testing criteria generation: {e}")

if __name__ == "__main__":
    asyncio.run(test_content_review())
