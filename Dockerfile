# Use the official Python base image
FROM python:3.10-slim
 
# Set the working directory in the container
WORKDIR /app
 
# Copy the requirements file into the container
COPY requirements.txt .
 
# Install system dependencies
# RUN apt-get update && apt-get install -y \
#     build-essential \
#     poppler-utils \
#     && rm -rf /var/lib/apt/lists/*
 
# Install Python dependencies
RUN pip install -r requirements.txt
 
 
# Copy the rest of the application code into the container
COPY . .
 
# Expose the port that the app runs on
EXPOSE 8080
 
# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]