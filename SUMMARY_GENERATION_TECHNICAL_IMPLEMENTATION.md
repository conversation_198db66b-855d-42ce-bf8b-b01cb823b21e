# Summary Generation Pipeline - Technical Implementation

## Overview

This document provides a technical implementation guide for the ProposalPro summary generation pipeline. The system processes summary requests through two distinct approaches: Vector Database approach and Non-Vector Database approach, with configurable LLM integration and user guidelines support.

## Architecture Flow

1. **Summary Generation Request** → Initial request processing
2. **RAG Approach Decision** → Check if Vector DB should be used
3. **Context Retrieval** → Either Vector DB or Pickle File approach
4. **LLM Processing** → Model selection and prompt creation
5. **Summary Generation** → Final content generation with token management
6. **Summary Storage** → Save to GCS with metadata

## Stage 1: Summary Generation Request

### Request Processing Components

The summary generation starts with a request containing section information and optional user guidelines:

**Input**: Summary generation request
**Process**: Request validation and routing
**Output**: Validated request ready for processing


**Technical Implementation**:
- Section title validation and normalization
- User guidelines processing and integration
- Project context retrieval and validation
- Model selection based on project configuration


## Stage 2: RAG Approach Decision Point

### Decision Flow Implementation

**Decision Point**: "CHECK RAG APPROACH TO BE USED"
**Process**: Determine whether to use Vector DB or Pickle File approach
**Configuration**: Based on `settings.create_vector_db` and vector database availability

**Vector DB Approach (YES)**:
- **Condition**: Vector database exists and `create_vector_db` is enabled
- **Process**: Load vector database from GCS
- **Implementation**: FAISS vector database with Vertex AI embeddings

**Non-Vector DB Approach (NO)**:
- **Condition**: No vector database or disabled in configuration
- **Process**: Use pickle files for direct content access
- **Implementation**: Tag-based file selection and pickle content loading


## Stage 3A: Context Retrieval Using Vector DB

### Vector Database Approach Flow

**Input**: Section title and user guidelines
**Process**: Vector-based context retrieval
**Output**: Relevant document chunks for summarization

#### 3A.1 Predefined Context Retrieval
**Process**: Get predefined retrieval queries for the requested section
**Implementation**:
- Section-specific query generation
- Multiple query support for comprehensive coverage
- Configurable query strategies (single vs multiple)

#### 3A.2 Vector Database Query
**Process**: Execute similarity search against FAISS vector database
**Implementation**:
- Load vector database from GCS
- Execute semantic similarity search
- Retrieve top-k relevant document chunks
- Apply metadata filtering and deduplication

#### 3A.3 Context Processing
**Process**: Process and combine retrieved chunks
**Implementation**:
- De-duplication and sorting of chunks
- Context combination and formatting
- Metadata preservation for source tracking

## Stage 3B: Context Retrieval Using Pickle Files

### Pickle File Approach Flow

**Input**: Section title and project context
**Process**: Tag-based file selection and content extraction
**Output**: Relevant document content for summarization

#### 3B.1 Tag-Based File Selection
**Process**: Get tags of each file and map to pickle files
**Implementation**:
- Retrieve file metadata from MongoDB
- Filter files based on document tags
- Map relevant files to their pickle counterparts

#### 3B.2 Pickle File Loading
**Process**: Load pickle files from GCS
**Implementation**:
- Load pickle files from `processed_documents/` folder
- Deserialize pickle content to document sections
- Handle fallback to original files if pickle unavailable

#### 3B.3 Content Filtering
**Process**: Check if file should be used based on tags
**Decision Point**: "CHECK IF THE FILE SHOULD BE USED BASED ON TAGS"
**Implementation**:
- Tag-based relevance scoring
- Section-specific content filtering
- Content extraction from relevant pickle files

## Stage 4: LLM Processing and Prompt Creation

### Model Selection and Configuration

**Input**: Project configuration and user preferences
**Process**: LLM instance creation and configuration
**Output**: Configured LLM ready for summary generation

#### 4.1 Model Selection
**Process**: Get model selected by user from project configuration
**Implementation**:
- Retrieve model name from project settings
- Support for multiple model types (Vertex AI, Google Generative AI)
- Default model fallback (`gemini-1.5-flash-001`)

#### 4.2 LLM Instance Creation
**Process**: Create an instance of the LLM with proper configuration
**Implementation**:
- Model-specific configuration (temperature, top_p, top_k)
- Experimental model support
- Token limit management and retry configuration

#### 4.3 Prompt Creation
**Process**: Create final prompt combining context and user guidelines
**Implementation**:
- System prompt integration for section summarization
- User guidelines incorporation
- Context formatting and token optimization
- Input token counting and validation

## Stage 5: Summary Generation

### LLM Invocation and Response Processing

**Input**: Final prompt and configured LLM
**Process**: LLM invocation and response generation
**Output**: Generated summary with metadata

#### 5.1 User Guidelines Integration
**Decision Point**: "HAS USER GUIDELINES BEEN ADDED ??"
**YES Path**: Integrate user guidelines into prompt
**NO Path**: Use standard prompt without guidelines

#### 5.2 LLM Invocation
**Process**: Invoke the LLM with final prompt
**Implementation**:
- Async LLM invocation for performance
- Model-specific message formatting
- Error handling and retry logic
- Response content extraction

#### 5.3 Token Management
**Process**: Track input and output tokens
**Implementation**:
- Input token counting before LLM call
- Output token counting after response
- Token limit validation (7500 token threshold)
- Incomplete response handling for token limits

#### 5.4 Response Validation
**Decision Point**: "IS SUMMARY SURPASSING THE OUTPUT TOKEN LIMIT"
**YES Path**: Add note about summary being incomplete
**NO Path**: Save summary to GCS as complete

## Stage 6: Summary Storage

### Final Storage and Metadata Management

**Input**: Generated summary with metadata
**Process**: Save summary to GCS with complete metadata
**Output**: Stored summary accessible for future use

#### 6.1 Summary Formatting
**Process**: Format summary with metadata
**Implementation**:
- Clean markdown header formatting
- Token usage metadata inclusion
- User guidelines preservation
- Source attribution (Vector DB vs Pickle Files)

#### 6.2 GCS Storage
**Process**: Save summary to Google Cloud Storage
**Implementation**:
- Project-specific storage paths
- JSON format with complete metadata
- Versioning support through history folders
- Atomic write operations

#### 6.3 Database Updates
**Process**: Update MongoDB with summary metadata
**Implementation**:
- Project document updates
- Summary index maintenance
- Token usage tracking
- Generation timestamp recording

## Configuration Parameters

### System Configuration

**RAG Configuration**:
- `RAG_USE_MULTIPLE_QUERIES`: Boolean flag to enable multiple query approach
- `create_vector_db`: Enable/disable vector database usage
- `BUCKET_NAME`: GCS bucket for storage operations

**LLM Configuration**:
- `DEFAULT_MODEL_NAME`: "gemini-1.5-flash-001"
- `temperature`: 0.2 (controls randomness)
- `top_p`: 0.95 (nucleus sampling)
- `top_k`: 40 (top-k sampling)
- `max_retries`: 3 (retry attempts)

**Token Management**:
- Input token counting using `count_tokens()` utility
- Output token limit: 7500 tokens
- Token limit warning integration

**Storage Paths**:
- Project prefix: `Projects/{project_name}_{project_id}/`
- Summaries file: `summaries.json`
- Processed documents: `processed_documents/`
- Vector database: `vector_db/`

## Data Flow and Transformations

### Stage-by-Stage Data Flow

#### Stage 1 → Stage 2: Request to Decision Point
**Input Format**: `GenerateSection` schema with section_title and user_guidelines
**Output Format**: Validated request with project context
**Transformation**: Request validation → Project context enrichment

#### Stage 2 → Stage 3A/3B: Decision to Context Retrieval
**Input Format**: Validated request with approach decision
**Output Format**: Context retrieval strategy
**Transformation**: Configuration check → Approach selection

#### Stage 3A: Vector DB Context Retrieval
**Input Format**: Section title and search parameters
**Output Format**: Ranked document chunks with metadata
**Transformation**: Text query → Vector similarity → Relevant chunks

#### Stage 3B: Pickle File Context Retrieval
**Input Format**: Section title and document tags
**Output Format**: Structured document sections
**Transformation**: Tag filtering → Pickle deserialization → Content extraction

#### Stage 4 → Stage 5: Context to Summary Generation
**Input Format**: Retrieved context and user guidelines
**Output Format**: LLM-ready prompt
**Transformation**: Context formatting → Prompt creation → LLM invocation

#### Stage 5 → Stage 6: Summary to Storage
**Input Format**: Generated summary with token metadata
**Output Format**: Stored summary with complete metadata
**Transformation**: Response processing → Metadata enrichment → GCS storage

## Error Handling and Recovery

### Stage-Specific Error Handling

**Stage 1: Request Processing**
- Invalid section title validation
- Missing project context handling
- User guidelines format validation

**Stage 2: Approach Decision**
- Vector database availability checks
- Configuration validation
- Fallback mechanism to pickle files

**Stage 3A: Vector DB Retrieval**
- Vector database loading failures
- Query execution timeouts
- Empty result set handling

**Stage 3B: Pickle File Retrieval**
- Missing pickle files fallback
- Pickle deserialization errors
- Tag-based filtering failures

**Stage 4: LLM Processing**
- Model initialization failures
- Configuration validation errors
- Prompt size limit handling

**Stage 5: Summary Generation**
- LLM invocation timeouts
- Response parsing errors
- Token limit exceeded handling

**Stage 6: Storage Operations**
- GCS upload failures
- Database update errors
- Atomic operation rollback

### Recovery Mechanisms

**Automatic Fallbacks**:
- Vector DB failure → Pickle file approach
- Pickle file missing → Original file processing
- Primary model failure → Default model fallback

**Retry Logic**:
- LLM invocation retries (max 3 attempts)
- GCS operation retries with exponential backoff
- Database operation retries

**Error Reporting**:
- Comprehensive logging at each stage
- Error context preservation
- User-friendly error messages
