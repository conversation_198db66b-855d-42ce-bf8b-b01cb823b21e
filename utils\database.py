"""Database Configuration and Setup Module

This module handles the MongoDB database connection and collection setup using Motor for async operations.
It provides the database client, collections, and initialization functions for the application.

Collections:
- users: Stores user account information
- projects: Stores project data
- verification_tokens: Stores email verification tokens
- password_reset_tokens: Stores password reset tokens
- content_versions: Stores version history for project content (outlines and summaries)

The module uses environment variables for configuration and creates necessary indexes for optimal performance.
"""

from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
import os
from pymongo import ASCENDING
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

# MongoDB connection configuration
MONGO_URI = os.getenv("MONGO_URI")

# Initialize MongoDB client and database with SSL configuration for macOS compatibility
client = AsyncIOMotorClient(
    MONGO_URI,
    tls=True,
    tlsAllowInvalidCertificates=True,  # For development/testing on macOS
    serverSelectionTimeoutMS=30000,   # 30 second timeout
    connectTimeoutMS=20000,           # 20 second connection timeout
    socketTimeoutMS=20000             # 20 second socket timeout
)
db = client.document_summarizer

# Collection definitions
users_collection = db.users
projects_collection = db.projects
verification_tokens_collection = db.verification_tokens
password_reset_tokens_collection = db.password_reset_tokens
content_versions_collection = db.content_versions
app_config_collection = db.app_config
compliances_collection = db.compliances  # <-- Added for compliance storage

async def create_indexes():
    """Create necessary indexes on collections for optimal query performance.

    Creates unique indexes on token fields in verification_tokens and password_reset_tokens
    collections to ensure token uniqueness and faster lookups.

    Creates compound indexes on content_versions collection for efficient version queries.
    """
    await verification_tokens_collection.create_index([("token", ASCENDING)], unique=True)
    await password_reset_tokens_collection.create_index([("token", ASCENDING)], unique=True)

    # Index for content versions to efficiently query by project_id, content_type, and created_at
    await content_versions_collection.create_index([
        ("project_id", ASCENDING),
        ("content_type", ASCENDING),
        ("created_at", ASCENDING)
    ])

    # Index for efficiently finding active versions
    await content_versions_collection.create_index([
        ("project_id", ASCENDING),
        ("content_type", ASCENDING),
        ("is_active", ASCENDING)
    ])

    # Index for app configuration collection
    await app_config_collection.create_index([("config_type", ASCENDING)], unique=True)

async def initialize_app_config():
    """Initialize application configuration in MongoDB.

    This function sets up default configuration values in the app_config collection,
    including the default list of AI models. It only creates the configuration
    if it doesn't already exist.
    """
    # Check if models configuration already exists
    existing_models_config = await app_config_collection.find_one({"config_type": "ai_models"})

    if not existing_models_config:
        # Initialize with default models
        default_models_config = {
            "config_type": "ai_models",
            "models": [
                {
                    "model_name": "gemini-1.5-flash-001",
                    "display_name": "Easy Bot",
                    "description": "Fast and efficient model for basic document processing and summarization tasks.",
                    "is_active": True,
                    "is_default": True
                },
                {
                    "model_name": "gemini-2.0-flash-exp",
                    "display_name": "Smart Bot",
                    "description": "Advanced model with improved comprehension and reasoning capabilities for complex documents.",
                    "is_active": True,
                    "is_default": False
                },
                {
                    "model_name": "gemini-2.5-flash-preview-04-17",
                    "display_name": "Power Bot",
                    "description": "Experimental high-performance model with superior analytical capabilities for detailed document analysis.",
                    "is_active": True,
                    "is_default": False
                }
            ],
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }

        await app_config_collection.insert_one(default_models_config)

async def setup_database():
    """Initialize the database by creating required indexes and configuration.

    This function should be called during application startup to ensure
    proper database configuration. It's separated from the module level
    to allow for proper async initialization.
    """
    await create_indexes()
    await initialize_app_config()
