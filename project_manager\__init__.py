"""Project Management Module

This module provides comprehensive project management functionality for document processing
and summarization workflows. It handles project lifecycle, file management, and integration
with cloud storage and database systems.

Key Features:
- Project creation, loading, and deletion
- Document management (upload, rename, delete)
- File processing and caching
- Vector database integration
- PDF generation
- Cloud storage integration (Google Cloud Storage)
- MongoDB integration for metadata
- Async operations support

The module serves as the central coordinator for project-related operations,
maintaining consistency across storage systems and handling all project resources.
"""

# Import the main ProjectManager class for backward compatibility
from project_manager.core import ProjectManager

# Import ProjectDocument for backward compatibility
from models import ProjectDocument

# Make the ProjectManager and ProjectDocument classes available at the package level
__all__ = ['ProjectManager', 'ProjectDocument']
