"""PDF Utilities Module

This module provides functionality for processing PDF documents, including:
- Table detection and analysis
- Text extraction from PDFs
- PDF page processing
- OCR integration

Key Features:
- Table dominance detection
- Solicitation content identification
- Text extraction with various methods
- PDF section extraction
"""

import pymupdf4llm
import logging
import asyncio
import fitz
import tempfile
import os
from PIL import Image
import pytesseract
from config import settings

# Import RAG functions where they are used to avoid circular imports
extract_page_as_image = None
extract_text_with_vision = None

def _load_rag_functions():
    global extract_page_as_image, extract_text_with_vision
    if extract_page_as_image is None:
        from RAG.documents_loader import extract_page_as_image, extract_text_with_vision

def is_table_dominant_page(page, tables) -> bool:
    logging.info("Analyzing table dominance for page...")
    """
    Determine if tables dominate the content of a page by comparing content length.

    Args:
        page: pdfplumber page object
        tables: List of tables found on the page

    Returns:
        bool: True if table content length is greater than non-table content length
    """
    try:
        # Get all text from the page
        total_text = page.extract_text() or ""
        total_length = len(total_text)
        logging.info(f"Total text length on page: {total_length}")

        if not tables or total_length == 0:
            logging.info("No tables found or empty page - not table dominant")
            return False

        # Calculate content length inside tables
        table_content_length = 0
        for table in tables:
            table_length = sum(len(str(cell or "")) for row in table for cell in row)
            table_content_length += table_length
            logging.debug(f"Table content length: {table_length}")

        logging.info(f"Total content length in tables: {table_content_length}")

        # Calculate content length outside tables
        non_table_length = total_length - table_content_length
        logging.info(f"Content length outside tables: {non_table_length}")

        # Consider the page table-dominant if table content is more than non-table content
        is_dominant = table_content_length > non_table_length
        logging.info(f"Is table dominant: {is_dominant}")
        return is_dominant

    except Exception as e:
        logging.error(f"Error analyzing table content: {str(e)}")
        return False

def has_solicitation_content(tables) -> bool:
    """
    Check if any table cell contains specific solicitation-related content.

    Args:
        tables: List of tables found on the page

    Returns:
        bool: True if any cell contains solicitation-related content
    """
    try:
        target_phrases = [
            "TYPE OF SOLICITATION",
            "SOLICITATION NUMBER",
            "DATE ISSUED",
            "SOLICITATION/CONTRACT FORM"
        ]

        for table in tables:
            for row in table:
                for cell in row:
                    if cell:  # Check if cell is not None
                        cell_content = str(cell).strip().upper()
                        if any(phrase in cell_content for phrase in target_phrases):
                            logging.info(f"Found solicitation content in table: {cell_content}")
                            return True
        logging.debug("No solicitation content found in tables")
        return False

    except Exception as e:
        logging.error(f"Error checking table content: {str(e)}")
        return False

def extract_text_with_tesseract(image_path: str) -> str:
    """Extract text from an image using Tesseract OCR."""
    try:
        with Image.open(image_path) as img:
            from utils.file_processing import is_blank_image
            if not is_blank_image(img):
                text = pytesseract.image_to_string(img)
                return text.strip()
    except Exception as e:
        logging.error(f"Error extracting text with Tesseract: {str(e)}")
    return ""

async def extract_text_from_image(image_path: str, method: str = "vision") -> str:
    """Extract text from an image using the specified method."""
    _load_rag_functions()  # Load RAG functions when needed
    if method == "vision":
        return await extract_text_with_vision(image_path)
    elif method == "tesseract":
        return extract_text_with_tesseract(image_path)
    else:
        logging.error(f"Unsupported text extraction method: {method}")
        return ""

async def extract_markdown_from_pdf(pdf_path, page_num, doc_handle=None):
    """
    Safely extract markdown content from a PDF page using pymupdf4llm.
    Ensures proper file handling and cleanup.

    IMPORTANT: This function includes a workaround for the '_document' attribute error
    that occurs due to version incompatibility between pymupdf4llm 0.0.10 and PyMuPDF 1.24.2.

    Args:
        pdf_path (str): Path to the PDF file
        page_num (int): Page number to extract (0-based index)
        doc_handle (fitz.Document, optional): Already opened document handle to reuse

    Returns:
        str: Extracted markdown content
    """
    try:
        # Use the correct way to call pymupdf4llm.to_markdown
        content = pymupdf4llm.to_markdown(pdf_path, pages=[page_num])
        return content
    except AttributeError as attr_err:
        if "'Document' object has no attribute '_document'" in str(attr_err):
            logging.error(f"Version incompatibility error on page {page_num + 1}: {str(attr_err)}")
            logging.error("This error is caused by incompatibility between pymupdf4llm 0.0.10 and PyMuPDF 1.24.2")
            logging.error("SOLUTION: Run 'pip install --upgrade pymupdf4llm' to fix this issue")
        else:
            logging.error(f"Attribute error extracting markdown from page {page_num + 1}: {str(attr_err)}")

        # Fall back to using PyMuPDF directly - reuse handle if available
        try:
            if doc_handle is not None:
                logging.info(f"OPTIMIZATION: Reusing document handle for fallback text extraction on page {page_num + 1}")
                page_text = doc_handle[page_num].get_text()
            else:
                logging.info(f"FALLBACK: Opening new PDF document for text extraction on page {page_num + 1}")
                doc = fitz.open(pdf_path)
                page_text = doc[page_num].get_text()
                doc.close()
            return page_text
        except Exception as fallback_err:
            logging.error(f"Fallback text extraction also failed: {str(fallback_err)}")
            return "Error: Could not extract content from this page."
    except Exception as e:
        logging.error(f"Error extracting markdown from page {page_num + 1}: {str(e)}")
        # Fall back to using PyMuPDF directly - reuse handle if available
        try:
            if doc_handle is not None:
                logging.info(f"OPTIMIZATION: Reusing document handle for fallback text extraction on page {page_num + 1}")
                page_text = doc_handle[page_num].get_text()
            else:
                logging.info(f"FALLBACK: Opening new PDF document for text extraction on page {page_num + 1}")
                doc = fitz.open(pdf_path)
                page_text = doc[page_num].get_text()
                doc.close()
            return page_text
        except Exception as fallback_err:
            logging.error(f"Fallback text extraction also failed: {str(fallback_err)}")
            return "Error: Could not extract content from this page."
    finally:
        # Force garbage collection to help release file handles
        import gc
        gc.collect()

def get_batch_page_content(page_num, batch_results):
    """
    Safely get page content from batch results.

    Args:
        page_num (int): Page number (1-based)
        batch_results (list): Results from pymupdf4llm.to_markdown with page_chunks=True

    Returns:
        str or None: Page content if available, None if not found or error
    """
    try:
        if batch_results and (page_num - 1) < len(batch_results):
            page_data = batch_results[page_num - 1]
            if isinstance(page_data, dict) and 'text' in page_data:
                return page_data['text']
            else:
                logging.warning(f"Unexpected batch result structure for page {page_num}: {type(page_data)}")
                return None
    except (IndexError, KeyError, TypeError) as e:
        logging.warning(f"Error accessing batch result for page {page_num}: {e}")
    return None

async def extract_sections(doc, text_format='md', sf_pages=None, image_pages=None):
    _load_rag_functions()  # Load RAG functions when needed
    page_contents = []
    processed_pages = set()  # Keep track of processed pages

    # Create temporary directory for images and PDF
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Save PDF to temporary file
            temp_pdf_path = os.path.join(temp_dir, "temp.pdf")
            doc.save(temp_pdf_path)

            # Open a reusable document handle for optimization
            logging.info("OPTIMIZATION: Creating reusable PDF document handle for efficient processing")
            reusable_doc = fitz.open(temp_pdf_path)

            if text_format == 'md':
                try:
                    # Get total pages safely
                    total_pages = len(doc)
                    logging.info(f"Processing PDF with {total_pages} pages")

                    # NEW: Batch extract all pages with pymupdf4llm for optimization
                    batch_results = None
                    try:
                        logging.info(" OPTIMIZATION: Batch extracting all pages with pymupdf4llm")
                        batch_results = pymupdf4llm.to_markdown(temp_pdf_path, page_chunks=True)
                        logging.info(f" Successfully batch-extracted {len(batch_results)} pages")
                    except Exception as batch_err:
                        logging.warning(f"Batch extraction failed: {batch_err}, falling back to individual processing")
                        batch_results = None

                    # Process each page individually
                    for page_num in range(1, total_pages + 1):
                        if page_num not in processed_pages:  # Only process if not already processed
                            try:
                                content = None
                                is_sf = page_num in (sf_pages or [])
                                is_image = page_num in (image_pages or [])

                                # Determine if we need to use image processing
                                process_as_image = is_sf or (is_image and settings.PROCESS_IMAGES)

                                if process_as_image:
                                    try:
                                        # Extract page as image and process - pass reusable doc handle
                                        image_path = await extract_page_as_image(temp_pdf_path, page_num - 1, temp_dir, reusable_doc)
                                        if image_path:
                                            if is_sf:
                                                # Always use Vision for SF pages
                                                logging.info(f"Processing page {page_num} with Vision API (SF page)")
                                                content = await extract_text_with_vision(image_path)
                                                if content:  # Check if content was successfully extracted
                                                    from utils.token_utils import count_tokens
                                                    token_count = count_tokens(content)
                                                    if token_count > 8000:
                                                        logging.warning(f"Vision API output for page {page_num} exceeds 8000 tokens ({token_count} tokens). Falling back to markdown text extraction.")
                                                        content = None  # Will trigger fallback
                                                    else:
                                                        logging.info(f"Vision processing completed for SF page {page_num}")
                                                        logging.info("------------------VISION OUTPUT------------------")
                                                        logging.info(f"Extracted text from page {page_num} using Vision API: {content[:100]}..." if len(content) > 100 else content)
                                                        logging.info("------------------VISION OUTPUT------------------")
                                            elif is_image:
                                                # Use configured method for image pages
                                                method = settings.IMAGE_PROCESSING_METHOD
                                                logging.info(f"Processing page {page_num} with {method} (image page)")
                                                content = await extract_text_from_image(image_path, method)
                                                if content:  # Check if content was successfully extracted
                                                    from utils.token_utils import count_tokens
                                                    token_count = count_tokens(content)
                                                    if token_count > 7500:
                                                        logging.warning(f"{method.upper()} output for page {page_num} exceeds 7500 tokens ({token_count} tokens). Falling back to markdown text extraction.")
                                                        content = None  # Will trigger fallback
                                                    else:
                                                        logging.info(f"{method.title()} processing completed for image page {page_num}")
                                                        logging.info(f"------------------{method.upper()} OUTPUT------------------")
                                                        logging.info(f"Extracted text from page {page_num} using {method.upper()}: {content[:100]}..." if len(content) > 100 else content)
                                                        logging.info(f"------------------{method.upper()} OUTPUT------------------")
                                    except Exception as img_err:
                                        logging.error(f"Error during image processing for page {page_num}: {str(img_err)}. Falling back to markdown text extraction.")
                                        content = None  # Will trigger fallback

                                # If no content extracted yet or if previous methods failed, use markdown text extraction
                                if content is None:
                                    # Try to get content from batch results first
                                    batch_content = get_batch_page_content(page_num, batch_results)
                                    if batch_content is not None:
                                        logging.info(f" OPTIMIZATION: Using batch-extracted content for page {page_num}")
                                        content = batch_content
                                        print("-----------------------")
                                        print(f"Page {page_num} content preview: {content[:100]}..." if content and len(content) > 100 else content)
                                        print("-----------------------")
                                    else:
                                        # Fallback to individual extraction
                                        logging.info(f" FALLBACK: Individual markdown text extraction for page {page_num}")
                                        try:
                                            # Extract markdown using pymupdf4llm - pass reusable doc handle
                                            content = await extract_markdown_from_pdf(temp_pdf_path, page_num - 1, reusable_doc)
                                            print("-----------------------")
                                            print(f"Page {page_num} content preview: {content[:100]}..." if content and len(content) > 100 else content)
                                            print("-----------------------")

                                        except Exception as txt_err:
                                            logging.error(f"Direct text extraction failed for page {page_num}: {str(txt_err)}")
                                            raise txt_err

                                page_contents.append({
                                    'page_number': page_num,
                                    'content': content or "Error: No content could be extracted from this page.",
                                    'is_sf_page': is_sf,
                                    'is_image_page': is_image,
                                    'processing_method': 'vision' if is_sf else (settings.IMAGE_PROCESSING_METHOD if is_image and settings.PROCESS_IMAGES else 'text')
                                })
                                processed_pages.add(page_num)

                            except Exception as page_err:
                                logging.error(f"Error processing page {page_num}: {str(page_err)}")
                                page_contents.append({
                                    'page_number': page_num,
                                    'content': f"Error processing page: {str(page_err)}",
                                    'is_sf_page': page_num in (sf_pages or []),
                                    'is_image_page': page_num in (image_pages or []),
                                    'processing_method': 'error'
                                })
                                processed_pages.add(page_num)

                except Exception as proc_err:
                    logging.error(f"Error during PDF processing: {str(proc_err)}")
                    raise
            else:
                # Handle non-markdown format
                # Try batch extraction for non-markdown format too
                batch_results = None
                try:
                    logging.info(" OPTIMIZATION: Batch extracting all pages for non-markdown format")
                    batch_results = pymupdf4llm.to_markdown(temp_pdf_path, page_chunks=True)
                    logging.info(f" Successfully batch-extracted {len(batch_results)} pages for non-markdown")
                except Exception as batch_err:
                    logging.warning(f"Batch extraction failed for non-markdown: {batch_err}, falling back to individual processing")
                    batch_results = None

                for page_num, page in enumerate(doc, 1):
                    if page_num not in processed_pages:
                        try:
                            # Try batch results first
                            batch_content = get_batch_page_content(page_num, batch_results)
                            if batch_content is not None:
                                logging.info(f" OPTIMIZATION: Using batch-extracted content for page {page_num} (non-markdown)")
                                content = batch_content
                            else:
                                # Use reusable document handle for markdown extraction
                                content = await extract_markdown_from_pdf(temp_pdf_path, page_num - 1, reusable_doc)
                                if content is None:  # Fall back to regular text extraction
                                    content = await asyncio.to_thread(
                                        lambda: page.get_text()
                                    )

                            page_contents.append({
                                'page_number': page_num,
                                'content': content,
                                'is_sf_page': page_num in (sf_pages or []),
                                'is_image_page': page_num in (image_pages or []),
                                'processing_method': 'text'
                            })
                            processed_pages.add(page_num)
                        except Exception as e:
                            logging.error(f"Error extracting text from page {page_num}: {str(e)}")
                            page_contents.append({
                                'page_number': page_num,
                                'content': f"Error extracting text: {str(e)}",
                                'is_sf_page': page_num in (sf_pages or []),
                                'is_image_page': page_num in (image_pages or []),
                                'processing_method': 'error'
                            })
                            processed_pages.add(page_num)

        except Exception as e:
            logging.error(f"Critical error in extract_sections: {str(e)}")
            raise

        finally:
            # Close the reusable document handle
            if 'reusable_doc' in locals():
                try:
                    logging.info(" OPTIMIZATION: Closing reusable PDF document handle")
                    reusable_doc.close()
                except Exception as e:
                    logging.warning(f"Error closing reusable document handle: {str(e)}")

            # Sort pages by page number to maintain order
            page_contents.sort(key=lambda x: x['page_number'])

            # Force garbage collection to help release file handles
            import gc
            gc.collect()

    logging.info(f"Successfully extracted {len(page_contents)} sections")
    return page_contents
