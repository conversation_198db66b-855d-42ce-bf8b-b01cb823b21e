"""Summary generation and management routes module.

This module provides FastAPI routes for handling project summaries including:
- Single section summary generation
- Multiple section summary generation
- Summary retrieval and management
All endpoints handle proper authentication, logging, and support both vector DB and document-based approaches.
"""

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from utils.constants import HTTPStatus, Sections, StoragePaths
import logging
from utils import run_relevant_agent, clean_markdown_header, parse_json_proposal_info, filter_rfp_project_metadata, convert_proposal_info_to_markdown, clean_text_content
from utils.auth import get_current_active_user, User
from project_manager import ProjectManager
from schemas import GenerateSection, AddSectionSummary, GenerateMultipleSections
from config import settings
from RAG.vector_db import load_vector_db
from RAG_processing import generate_summary_multiple_queries, generate_summary_single_query
from utils.llm_config import get_llm_instance
from crew import *
from utils.database import projects_collection


router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/projects/{project_id}/generate_summary")
async def generate_summary(
    project_id: str,
    section_request: GenerateSection,
    use_vector_db: bool = True,
    current_user: User = Depends(get_current_active_user)
):
    """Generate or update a summary for a specific section of a project.

    Args:
        project_id (str): The ID of the project to generate summary for
        section_request (GenerateSection): Section title and optional user guidelines
        use_vector_db (bool, optional): Whether to use vector DB for generation. Defaults to True.
        current_user (User): The authenticated user making the request

    Returns:
        dict: Generated section title and content

    Raises:
        HTTPException: 400 for invalid section or missing documents
                      403 for unauthorized access
                      404 if project not found
                      500 for generation failures
    """
    try:
        logger.info(f"User {current_user.full_name} generating summary for project {project_id}, section: {section_request.section_title}")

        project_manager = await ProjectManager.load_project(project_id)
        if not project_manager:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Project not found")

        if project_manager.project_data.get('user_id') != str(current_user.id):
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="You don't have permission to access this project")

        logger.info("Trying to get used tokens count...")

        # ------------------TOKENS--------------------------------------------
        # Get current used token values
        if not project_manager.responses or section_request.section_title not in project_manager.responses:
            input_tokens = 0
            output_tokens = 0
        else:
            input_tokens = project_manager.responses[section_request.section_title]['input_tokens']
            output_tokens = project_manager.responses[section_request.section_title]['output_tokens']

        print(f"The value of input tokens before generating summary is {input_tokens} and value of output tokens before generating summary is {output_tokens}.")
        # ------------------TOKENS--------------------------------------------


        # Validate section exists in project
        available_sections = project_manager.project_data.get("sections", [])
        valid_sections = {section["section_title"] for section in available_sections}

        if section_request.section_title not in valid_sections:
            raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail=f"Invalid section title: {section_request.section_title}")

        # Check for uploaded documents
        uploaded_files = await project_manager.list_uploaded_files()
        if not uploaded_files:
            raise HTTPException(
                status_code=HTTPStatus.BAD_REQUEST,
                detail="No documents uploaded. Please upload documents before generating summary."
            )

        # Update guidelines if provided
        guidelines_changed = False
        if section_request.user_guidelines is not None:
            logger.info(f"User {current_user.full_name} updating section guidelines for project {project_id}, section: {section_request.section_title} with new guidelines: {section_request.user_guidelines}")
            current_guidelines = await project_manager.get_section_guidelines(section_request.section_title)
            if current_guidelines != section_request.user_guidelines and current_guidelines is not None:
                await project_manager.update_section(
                    section_request.section_title,
                    {'user_guidelines': section_request.user_guidelines}
                )
        else:
            logger.info(f"User {current_user.full_name} is not using section guidelines for project {project_id}, section: {section_request.section_title}, so user guidelines are being cleared")
            await project_manager.update_section(
                section_request.section_title,
                {'user_guidelines': ""}
            )

        if use_vector_db:
            # Load vector DB from GCS
            vector_db_path = f"{project_manager.project_prefix}{StoragePaths.VECTOR_DB_FOLDER}"
            logging.info(f"Loading vector database from GCS path: {vector_db_path}")

            # Check if vector DB exists in GCS
            from google.cloud import storage
            client = storage.Client()
            bucket = client.bucket(settings.BUCKET_NAME)
            index_blob = bucket.blob(f"{vector_db_path}/index.faiss")

            if not index_blob.exists():
                raise HTTPException(
                    status_code=HTTPStatus.BAD_REQUEST,
                    detail="Vector database not found. Please ensure documents are properly processed."
                )

            # Initialize vector DB and LLM
            vector_db = await load_vector_db(vector_db_path)
            llm = get_llm_instance(model_name=project_manager.model_name)

            # Generate summary using RAG with either single or multiple queries
            if settings.RAG_USE_MULTIPLE_QUERIES:
                summarized_section = await generate_summary_multiple_queries(
                    section_request.section_title,
                    vector_db,
                    llm,
                    project_id=project_id,
                    user_guidelines=section_request.user_guidelines or ""
                )
            else:
                summarized_section = await generate_summary_single_query(
                    section_request.section_title,
                    vector_db,
                    llm,
                    project_id=project_id,
                    user_guidelines=section_request.user_guidelines or ""
                )

            # Prepare response data
            response_data = {
                "content": clean_text_content(summarized_section["final_response"], mode='markdown'),
                "input_tokens": summarized_section["input_tokens"]+input_tokens,
                "output_tokens": summarized_section["output_tokens"]+output_tokens,
                "used_vector_db": True
            }

        else:
            logger.info("Generating summary using Agentic Approach")
            # Get pickled sections content
            sections_content = await project_manager.get_pickled_sections_content()
            if sections_content is None:
                raise HTTPException(
                    status_code=HTTPStatus.BAD_REQUEST,
                    detail="No extracted section content found. Please ensure documents are properly processed and uploaded."
                )

            section_content_to_use = None
            for section_content in sections_content:
                if section_request.section_title in section_content:
                    section_content_to_use = section_content[section_request.section_title]
                    break

            if section_content_to_use is None:
                raise HTTPException(
                    status_code=HTTPStatus.BAD_REQUEST,
                    detail=f"No content found for section: {section_request.section_title}. Please ensure the section content was properly extracted."
                )

            user_guidelines=section_request.user_guidelines or ""
            if user_guidelines != "":
                user_guidelines = f"""\n\n**Additional Guidelines:**\n{user_guidelines}\n\n *VERY IMPORTANT NOTE: If any instruction in the Additional Guidelines, conflicts with the above instructions, always follow/Prioritize the Additional Guidelines. *"""


            logger.info("Calling the Agent to generate the summary....")
            drafted_content, token_usage = await run_relevant_agent(section_request.section_title, "Drafting", section_content_to_use[0], user_guidelines)

            if not drafted_content:
                raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail="Failed to Draft summary")

            # logger.info(f"Token usage of the crew is: {token_usage}")

            print("----------------TOKEN USAGE--------------------------------------")
            print(f"Token usage of the crew is: {token_usage.prompt_tokens}, {token_usage.completion_tokens}")
            print("----------------TOKEN USAGE--------------------------------------")

            cleaned_drafted_content = clean_markdown_header(drafted_content)

            response_data = {
                "content": cleaned_drafted_content,
                "input_tokens": token_usage.prompt_tokens+input_tokens,
                "output_tokens": token_usage.completion_tokens+output_tokens,
                "used_vector_db": False
            }

        # Save response and update timestamps
        await project_manager.add_response(section_request.section_title, response_data)
        await project_manager.update_timestamps(update_content=True, update_visit=True)

        # If this is a Proposal Info summary, parse and update the rfp_project_metadata in MongoDB
        if section_request.section_title == Sections.PROPOSAL_INFO:
            try:
                logger.info("Parsing Proposal Info content for metadata")
                proposal_content = response_data.get("content", "")

                if not proposal_content or not proposal_content.strip():
                    logger.warning("Empty Proposal Info content, cannot parse metadata")
                    await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {"rfp_project_metadata": {}}}
                    )
                else:
                    # Log a sample of the content for debugging
                    content_preview = proposal_content[:200] + "..." if len(proposal_content) > 200 else proposal_content
                    logger.info(f"Proposal Info content sample: {content_preview}")

                    # Parse the metadata
                    full_metadata = parse_json_proposal_info(proposal_content)

                    # Filter the metadata to only include the specified fields
                    rfp_project_metadata = filter_rfp_project_metadata(full_metadata)

                    # Log parsing results
                    logger.info(f"Parsed {len(full_metadata)} metadata fields, filtered to {len(rfp_project_metadata)} fields")
                    for key, value in rfp_project_metadata.items():
                        if isinstance(value, dict):
                            logger.info(f"  {key}: [Complex nested structure with {len(value)} items]")
                        else:
                            logger.info(f"  {key}: {value}")

                    # Update MongoDB with filtered metadata only
                    update_result = await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {
                            "rfp_project_metadata": rfp_project_metadata
                        }}
                    )

                    # Convert to markdown and update the response content in summaries.json
                    try:
                        markdown_content = convert_proposal_info_to_markdown(full_metadata)
                        response_data["content"] = clean_text_content(markdown_content, mode='markdown')
                        await project_manager.add_response(section_request.section_title, response_data)
                        logger.info("Updated Proposal Info response with markdown table content in summaries.json")
                    except Exception as md_error:
                        logger.error(f"Error converting metadata to markdown: {str(md_error)}")

                    logger.info(f"MongoDB update result: matched={update_result.matched_count}, modified={update_result.modified_count}")
            except Exception as parse_error:
                logger.error(f"Error parsing Proposal Info or updating MongoDB: {str(parse_error)}")
                logger.error(f"Exception details: {type(parse_error).__name__}", exc_info=True)
                # Update with empty dict in case of error
                try:
                    await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {"rfp_project_metadata": {}}}
                    )
                    logger.info("Reset rfp_project_metadata to empty dict due to parsing error")
                except Exception as db_error:
                    logger.error(f"Failed to reset rfp_project_metadata: {str(db_error)}")

        logger.info(f"Summary generated for section {section_request.section_title}")

        # Return the content from summaries.json (consistent for all sections)
        return {
            "section_title": section_request.section_title,
            "content": response_data["content"]
        }

    except Exception as e:
        logger.error(f"Error generating summary: {str(e)}")
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, detail=f"Failed to generate summary: {str(e)}")

@router.post("/projects/{project_id}/add_summary_section")
async def add_summary_section(
    project_id: str,
    section_request: AddSectionSummary,
    use_vector_db: bool = True,
    current_user: User = Depends(get_current_active_user)
):
    """Add a new summary section to a project, generating content if it doesn't exist.

    Args:
        project_id (str): The ID of the project to add summary to
        section_request (AddSectionSummary): Section title to add
        use_vector_db (bool, optional): Whether to use vector DB for generation. Defaults to True.
        current_user (User): The authenticated user making the request

    Returns:
        dict: Section title and content (existing or newly generated)

    Raises:
        HTTPException: 400 for invalid section or missing documents
                      403 for unauthorized access
                      404 if project not found
                      500 for generation failures
    """
    logger.info(
        f"User {current_user.full_name} requesting addition of new summary section for project {project_id}, section: {section_request.section_title}"
    )

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=404, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=403, detail="You don't have permission to access this project")


    logger.info("Trying to get used tokens count...")

    # ------------------TOKENS--------------------------------------------
    # Get current used token values
    if not project_manager.responses or section_request.section_title not in project_manager.responses:
        input_tokens = 0
        output_tokens = 0
    else:
        input_tokens = project_manager.responses[section_request.section_title]['input_tokens']
        output_tokens = project_manager.responses[section_request.section_title]['output_tokens']

    print(f"The value of input tokens before generating summary is {input_tokens} and value of output tokens before generating summary is {output_tokens}.")
    # ------------------TOKENS--------------------------------------------


    # Validate section exists
    available_sections = project_manager.project_data.get("sections", [])
    valid_sections = {section["section_title"] for section in available_sections}

    if section_request.section_title not in valid_sections:
        raise HTTPException(status_code=400, detail=f"Invalid section title: {section_request.section_title}")

    # Check if summary already exists and is not empty
    if section_request.section_title in project_manager.responses:
        existing_summary = project_manager.responses[section_request.section_title]
        content = existing_summary.get("content", "")

        # Only consider non-empty content as existing
        if content and content.strip():
            logger.info(f"Using existing summary for section: {section_request.section_title}")

            return {
                "section_title": section_request.section_title,
                "content": content
            }
        else:
            logger.info(f"Summary exists for section: {section_request.section_title} but content is empty, will regenerate")

    # Check for uploaded documents
    uploaded_files = await project_manager.list_uploaded_files()
    if not uploaded_files:
        raise HTTPException(
            status_code=400,
            detail="No documents uploaded. Please upload documents before generating summary."
        )

    try:
        if use_vector_db:
            # Load and validate vector DB
            vector_db_path = f"{project_manager.project_prefix}{StoragePaths.VECTOR_DB_FOLDER}"
            logging.info(f"Loading vector database from GCS path: {vector_db_path}")

            from google.cloud import storage
            client = storage.Client()
            bucket = client.bucket(settings.BUCKET_NAME)
            index_blob = bucket.blob(f"{vector_db_path}/index.faiss")

            if not index_blob.exists():
                raise HTTPException(
                    status_code=400,
                    detail="Vector database not found. Please ensure documents are properly processed."
                )

            # Initialize vector DB and LLM
            vector_db = await load_vector_db(vector_db_path)
            llm = get_llm_instance(model_name=project_manager.model_name)

            # Generate summary using RAG
            if settings.RAG_USE_MULTIPLE_QUERIES:
                summarized_section = await generate_summary_multiple_queries(
                    section_request.section_title,
                    vector_db,
                    llm,
                    project_id=project_id,
                    user_guidelines=""
                )
            else:
                summarized_section = await generate_summary_single_query(
                    section_request.section_title,
                    vector_db,
                    llm,
                    project_id=project_id,
                    user_guidelines=""
                )

            response_data = {
                "content": clean_text_content(summarized_section["final_response"], mode='markdown'),
                "input_tokens": summarized_section["input_tokens"]+input_tokens,
                "output_tokens": summarized_section["output_tokens"]+output_tokens,
                "used_vector_db": True
            }

        else:
            # Get pickled sections content
            sections_content = await project_manager.get_pickled_sections_content()
            if sections_content is None:
                raise HTTPException(
                    status_code=400,
                    detail="No extracted section content found. Please ensure documents are properly processed and uploaded."
                )

            section_content_to_use = None
            for section_content in sections_content:
                if section_request.section_title in section_content:
                    section_content_to_use = section_content[section_request.section_title]
                    break

            if section_content_to_use is None:
                raise HTTPException(
                    status_code=400,
                    detail=f"No content found for section: {section_request.section_title}. Please ensure the section content was properly extracted."
                )

            drafted_content, token_usage = await run_relevant_agent(section_request.section_title, "Drafting", section_content_to_use[0], "")

            if not drafted_content:
                raise HTTPException(status_code=500, detail="Failed to Draft summary")

            print("----------------TOKEN USAGE--------------------------------------")
            print(f"Token usage of the crew is: {token_usage.prompt_tokens}, {token_usage.completion_tokens}")
            print("----------------TOKEN USAGE--------------------------------------")

            cleaned_drafted_content = clean_markdown_header(drafted_content)



            response_data = {
                "content": cleaned_drafted_content,
                "input_tokens": token_usage.prompt_tokens+input_tokens,
                "output_tokens": token_usage.completion_tokens+output_tokens,
                "used_vector_db": False
            }

        # Save response and update timestamps
        await project_manager.add_response(section_request.section_title, response_data)
        await project_manager.update_timestamps(update_content=True, update_visit=True)

        # If this is a Proposal Info summary, parse and update the rfp_project_metadata in MongoDB
        if section_request.section_title == Sections.PROPOSAL_INFO:
            try:
                logger.info("Parsing Proposal Info content for metadata")
                proposal_content = response_data.get("content", "")

                if not proposal_content or not proposal_content.strip():
                    logger.warning("Empty Proposal Info content, cannot parse metadata")
                    await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {"rfp_project_metadata": {}}}
                    )
                else:
                    # Log a sample of the content for debugging
                    content_preview = proposal_content[:200] + "..." if len(proposal_content) > 200 else proposal_content
                    logger.info(f"Proposal Info content sample: {content_preview}")

                    # Parse the metadata
                    full_metadata = parse_json_proposal_info(proposal_content)

                    # Filter the metadata to only include the specified fields
                    rfp_project_metadata = filter_rfp_project_metadata(full_metadata)

                    # Log parsing results
                    logger.info(f"Parsed {len(full_metadata)} metadata fields, filtered to {len(rfp_project_metadata)} fields")
                    for key, value in rfp_project_metadata.items():
                        if isinstance(value, dict):
                            logger.info(f"  {key}: [Complex nested structure with {len(value)} items]")
                        else:
                            logger.info(f"  {key}: {value}")

                    # Update MongoDB with filtered metadata only
                    update_result = await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {
                            "rfp_project_metadata": rfp_project_metadata
                        }}
                    )

                    # Convert to markdown and update the response content in summaries.json
                    try:
                        markdown_content = convert_proposal_info_to_markdown(full_metadata)
                        response_data["content"] = clean_text_content(markdown_content, mode='markdown')
                        await project_manager.add_response(section_request.section_title, response_data)
                        logger.info("Updated Proposal Info response with markdown table content in summaries.json")
                    except Exception as md_error:
                        logger.error(f"Error converting metadata to markdown: {str(md_error)}")

                    logger.info(f"MongoDB update result: matched={update_result.matched_count}, modified={update_result.modified_count}")
            except Exception as parse_error:
                logger.error(f"Error parsing Proposal Info or updating MongoDB: {str(parse_error)}")
                logger.error(f"Exception details: {type(parse_error).__name__}", exc_info=True)
                # Update with empty dict in case of error
                try:
                    await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {"rfp_project_metadata": {}}}
                    )
                    logger.info("Reset rfp_project_metadata to empty dict due to parsing error")
                except Exception as db_error:
                    logger.error(f"Failed to reset rfp_project_metadata: {str(db_error)}")

        logger.info(f"Summary added for section {section_request.section_title}")

        # Return the content from summaries.json (consistent for all sections)
        return {
            "section_title": section_request.section_title,
            "content": response_data["content"]
        }

    except Exception as e:
        logger.error(f"Error generating summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate summary: {str(e)}")

@router.post("/projects/{project_id}/generate_multiple_summaries")
async def generate_multiple_summaries(
    project_id: str,
    sections_request: GenerateMultipleSections,
    use_vector_db: bool = True,
    current_user: User = Depends(get_current_active_user)
):
    """Generate summaries for multiple sections, reusing existing ones where available.

    This endpoint generates summaries for the requested sections but returns ALL summaries
    that exist for the project, not just the ones that were requested.

    Args:
        project_id (str): The ID of the project to generate summaries for
        sections_request (GenerateMultipleSections): List of section titles to generate
        use_vector_db (bool, optional): Whether to use vector DB for generation. Defaults to True.
        current_user (User): The authenticated user making the request

    Returns:
        JSONResponse: List of ALL summaries in the project (both existing and newly generated),
                     with status field indicating whether each was "existing" or "generated"

    Raises:
        HTTPException: 400 for invalid sections or missing documents
                      403 for unauthorized access
                      404 if project not found
                      500 for generation failures
    """
    logger.info(f"User {current_user.full_name} generating multiple summaries for project {project_id}")

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=404, detail="Project not found")

    if project_manager.project_data.get('user_id') != str(current_user.id):
        raise HTTPException(status_code=403, detail="You don't have permission to access this project")


    # Remove duplicates while preserving order
    unique_sections = list(dict.fromkeys(sections_request.section_titles))
    if len(unique_sections) != len(sections_request.section_titles):
        logger.warning(f"Removed duplicate section titles from request. Original: {len(sections_request.section_titles)}, Unique: {len(unique_sections)}")

    # Validate sections exist
    available_sections = project_manager.project_data.get("sections", [])
    valid_sections = {section["section_title"] for section in available_sections}

    invalid_sections = [title for title in unique_sections if title not in valid_sections]
    if invalid_sections:
        raise HTTPException(status_code=400, detail=f"Invalid section titles: {', '.join(invalid_sections)}")

    uploaded_files = await project_manager.list_uploaded_files()
    if not uploaded_files:
        raise HTTPException(
            status_code=400,
            detail="No documents uploaded. Please upload documents before generating summaries."
        )

    try:
        summaries = []
        sections_to_generate = []

        # Collect existing summaries first
        for section_title in unique_sections:
            if section_title in project_manager.responses:
                existing_summary = project_manager.responses[section_title]
                content = existing_summary.get("content", "")

                # Only consider non-empty content as existing
                if content and content.strip():
                    summaries.append({
                        "section_title": section_title,
                        "content": content,
                        "status": "existing"
                    })
                    logger.info(f"Using existing summary for section: {section_title}")
                else:
                    sections_to_generate.append(section_title)
                    logger.info(f"Summary exists for section: {section_title} but content is empty, will regenerate")
            else:
                sections_to_generate.append(section_title)
                logger.info(f"Will generate new summary for section: {section_title}")

        # Generate new summaries if needed
        if sections_to_generate:
            if use_vector_db:
                # Load and validate vector DB
                vector_db_path = f"{project_manager.project_prefix}{StoragePaths.VECTOR_DB_FOLDER}"
                logging.info(f"Loading vector database from GCS path: {vector_db_path}")

                from google.cloud import storage
                client = storage.Client()
                bucket = client.bucket(settings.BUCKET_NAME)
                index_blob = bucket.blob(f"{vector_db_path}/index.faiss")

                if not index_blob.exists():
                    raise HTTPException(
                        status_code=400,
                        detail="Vector database not found. Please ensure documents are properly processed."
                    )

                # Initialize vector DB and LLM
                vector_db = await load_vector_db(vector_db_path)
                llm = get_llm_instance(model_name=project_manager.model_name)

                # Generate summaries using RAG
                for section_title in sections_to_generate:
                    logger.info("Trying to get used tokens count...")

                    # Get current used token values
                    if not project_manager.responses or section_title not in project_manager.responses:
                        input_tokens = 0
                        output_tokens = 0
                    else:
                        input_tokens = project_manager.responses[section_title]['input_tokens']
                        output_tokens = project_manager.responses[section_title]['output_tokens']

                    print(f"The value of input tokens before generating summary is {input_tokens} and value of output tokens before generating summary is {output_tokens}.")

                    # Generate summary using RAG with vector DB
                    if settings.RAG_USE_MULTIPLE_QUERIES:
                        summarized_section = await generate_summary_multiple_queries(
                            section_title,
                            vector_db,
                            llm,
                            project_id=project_id,
                            user_guidelines=""
                        )
                    else:
                        summarized_section = await generate_summary_single_query(
                            section_title,
                            vector_db,
                            llm,
                            project_id=project_id,
                            user_guidelines=""
                        )

                    response_data = {
                        "content": clean_text_content(summarized_section["final_response"], mode='markdown'),
                        "input_tokens": summarized_section["input_tokens"]+input_tokens,
                        "output_tokens": summarized_section["output_tokens"]+output_tokens,
                        "used_vector_db": True
                    }

                    # Save response
                    await project_manager.add_response(section_title, response_data)

                    summaries.append({
                        "section_title": section_title,
                        "content": summarized_section["final_response"],
                        "status": "generated"
                    })

                    logger.info(f"Generated new summary for section: {section_title} using vector DB")
            else:
                # Traditional approach using pickled sections content
                for section_title in sections_to_generate:
                    logger.info("Trying to get used tokens count...")

                    # Get current used token values
                    if not project_manager.responses or section_title not in project_manager.responses:
                        input_tokens = 0
                        output_tokens = 0
                    else:
                        input_tokens = project_manager.responses[section_title]['input_tokens']
                        output_tokens = project_manager.responses[section_title]['output_tokens']

                    print(f"The value of input tokens before generating summary is {input_tokens} and value of output tokens before generating summary is {output_tokens}.")

                    # Get pickled sections content
                    sections_content = await project_manager.get_pickled_sections_content()
                    if sections_content is None:
                        raise HTTPException(
                            status_code=400,
                            detail="No extracted section content found. Please ensure documents are properly processed and uploaded."
                        )

                    section_content_to_use = None
                    for section_content in sections_content:
                        if section_title in section_content:
                            section_content_to_use = section_content[section_title]
                            break

                    if section_content_to_use is None:
                        raise HTTPException(
                            status_code=400,
                            detail=f"No content found for section: {section_title}. Please ensure the section content was properly extracted."
                        )

                    # Generate summary using traditional approach
                    drafted_content, token_usage = await run_relevant_agent(section_title, "Drafting", section_content_to_use[0], "")

                    if not drafted_content:
                        raise HTTPException(status_code=500, detail=f"Failed to generate summary for section: {section_title}")

                    cleaned_drafted_content = clean_markdown_header(drafted_content)

                    response_data = {
                        "content": cleaned_drafted_content,
                        "input_tokens": token_usage.prompt_tokens + input_tokens,
                        "output_tokens": token_usage.completion_tokens + output_tokens,
                        "used_vector_db": False
                    }

                    # Save response
                    await project_manager.add_response(section_title, response_data)

                    summaries.append({
                        "section_title": section_title,
                        "content": cleaned_drafted_content,
                        "status": "generated"
                    })

                    logger.info(f"Generated new summary for section: {section_title} using traditional approach")

            await project_manager.update_timestamps(update_content=True, update_visit=True)
            logger.info(f"Generated {len(sections_to_generate)} new summaries, reused {len(summaries) - len(sections_to_generate)} existing summaries")
        else:
            logger.info("All requested summaries already exist")

        # No special handling needed - all sections now use summaries.json consistently

        # After generating all summaries
        await project_manager.update_timestamps(update_content=True, update_visit=True)

        # Collect ALL summaries from project_manager.responses, not just the requested ones
        # This ensures we return all generated summaries regardless of what was specifically requested
        all_summaries = []
        newly_generated_sections = {s["section_title"] for s in summaries if s.get("status") == "generated"}

        for section_title, response in project_manager.responses.items():
            content = response.get("content", "")
            # Only include summaries with non-empty content
            if content and content.strip():
                # Determine status: if it was newly generated in this request, mark as "generated", otherwise "existing"
                status = "generated" if section_title in newly_generated_sections else "existing"
                all_summaries.append({
                    "section_title": section_title,
                    "content": content,
                    "status": status
                })

        # Check if Proposal Info was among the newly generated summaries and parse metadata
        proposal_info_summary = None
        for summary in summaries:
            if summary["section_title"] == Sections.PROPOSAL_INFO and summary.get("content") and summary.get("status") == "generated":
                proposal_info_summary = summary
                break

        if proposal_info_summary:
            try:
                logger.info("Parsing Proposal Info content for metadata from newly generated summary")
                proposal_content = proposal_info_summary.get("content", "")

                if not proposal_content or not proposal_content.strip():
                    logger.warning("Empty Proposal Info content, cannot parse metadata")
                    await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {"rfp_project_metadata": {}}}
                    )
                else:
                    # Log a sample of the content for debugging
                    content_preview = proposal_content[:200] + "..." if len(proposal_content) > 200 else proposal_content
                    logger.info(f"Proposal Info content sample: {content_preview}")

                    # Parse the metadata
                    full_metadata = parse_json_proposal_info(proposal_content)

                    # Filter the metadata to only include the specified fields
                    rfp_project_metadata = filter_rfp_project_metadata(full_metadata)

                    # Log parsing results
                    logger.info(f"Parsed {len(full_metadata)} metadata fields, filtered to {len(rfp_project_metadata)} fields")
                    for key, value in rfp_project_metadata.items():
                        if isinstance(value, dict):
                            logger.info(f"  {key}: [Complex nested structure with {len(value)} items]")
                        else:
                            logger.info(f"  {key}: {value}")

                    # Update MongoDB with filtered metadata only
                    update_result = await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {
                            "rfp_project_metadata": rfp_project_metadata
                        }}
                    )

                    # Convert to markdown and update the response content in summaries.json
                    try:
                        markdown_content = convert_proposal_info_to_markdown(full_metadata)
                        # Update the summary in the summaries list and save to summaries.json
                        for summary in summaries:
                            if summary["section_title"] == Sections.PROPOSAL_INFO and summary.get("status") == "generated":
                                summary["content"] = markdown_content
                                break

                        # Also update the project manager responses
                        if Sections.PROPOSAL_INFO in project_manager.responses:
                            response_data = project_manager.responses[Sections.PROPOSAL_INFO]
                            response_data["content"] = clean_text_content(markdown_content, mode='markdown')
                            await project_manager.add_response(Sections.PROPOSAL_INFO, response_data)

                        # Update the all_summaries list with the new markdown content
                        for summary in all_summaries:
                            if summary["section_title"] == Sections.PROPOSAL_INFO:
                                summary["content"] = markdown_content
                                break

                        logger.info("Updated Proposal Info response with markdown table content in summaries.json")
                    except Exception as md_error:
                        logger.error(f"Error converting metadata to markdown: {str(md_error)}")

                    logger.info(f"MongoDB update result: matched={update_result.matched_count}, modified={update_result.modified_count}")
            except Exception as parse_error:
                logger.error(f"Error parsing Proposal Info or updating MongoDB: {str(parse_error)}")
                logger.error(f"Exception details: {type(parse_error).__name__}", exc_info=True)
                # Update with empty dict in case of error
                try:
                    await projects_collection.update_one(
                        {"project_id": project_id},
                        {"$set": {"rfp_project_metadata": {}}}
                    )
                    logger.info("Reset rfp_project_metadata to empty dict due to parsing error")
                except Exception as db_error:
                    logger.error(f"Failed to reset rfp_project_metadata: {str(db_error)}")
        elif Sections.PROPOSAL_INFO in [s["section_title"] for s in summaries if s.get("status") == "existing"]:
            logger.info(f"{Sections.PROPOSAL_INFO} section exists but was not regenerated. Skipping metadata parsing.")

        logger.info(f"Returning {len(all_summaries)} total summaries (including {len(sections_to_generate)} newly generated and {len(all_summaries) - len(sections_to_generate)} existing)")

        return JSONResponse(status_code=200, content={"summaries": all_summaries})

    except Exception as e:
        logger.error(f"Error generating summaries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate summaries: {str(e)}")

@router.get("/projects/{project_id}/summaries")
async def get_project_summaries(
    project_id: str, current_user: User = Depends(get_current_active_user)
):
    """Retrieve all existing summaries for a project.

    Args:
        project_id (str): The ID of the project to get summaries from
        current_user (User): The authenticated user making the request

    Returns:
        list: All project summaries with their section titles and content

    Raises:
        HTTPException: 403 for unauthorized access
                      404 if project or summaries not found
    """
    logger.info(f"User {current_user.full_name} retrieving all summaries for project {project_id}")

    project_manager = await ProjectManager.load_project(project_id)
    if not project_manager:
        raise HTTPException(status_code=404, detail="Project not found")

    if project_manager.project_data.get("user_id") != str(current_user.id):
        raise HTTPException(
            status_code=403, detail="You don't have permission to access this project"
        )

    if not project_manager.responses:
        raise HTTPException(status_code=404, detail="No summaries found for this project")

    # Format summaries for response, filtering out empty ones
    simplified_summaries = []

    for section_title, response in project_manager.responses.items():
        content = response.get("content", "")
        # Only include summaries with non-empty content
        if content and content.strip():
            simplified_summaries.append({
                "section_title": section_title,
                "content": content,
            })

    # If all summaries were empty, return a 404
    if not simplified_summaries:
        raise HTTPException(status_code=404, detail="No valid summaries found for this project")

    logger.info(f"All summaries retrieved for project {project_id}")
    await project_manager.update_timestamps(update_content=False, update_visit=True)
    return simplified_summaries